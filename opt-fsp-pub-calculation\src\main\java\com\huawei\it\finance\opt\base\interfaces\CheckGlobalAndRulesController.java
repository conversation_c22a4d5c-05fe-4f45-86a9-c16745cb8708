/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.interfaces;

import com.huawei.it.edm.client.entity.DocumentVO;
import com.huawei.it.edm.client.exception.EdmException;
import com.huawei.it.finance.opt.application.service.IExportTaskAppService;
import com.huawei.it.finance.opt.base.application.service.ICalculationUserConfigApplicationService;
import com.huawei.it.finance.opt.base.entity.CalculationRule;
import com.huawei.it.finance.opt.base.entity.CalculationUserConfig;
import com.huawei.it.finance.opt.base.exception.CalculationException;
import com.huawei.it.finance.opt.base.infrastructure.db.dao.CalculationRuleDao;
import com.huawei.it.finance.opt.base.protocol.ApiResponse;
import com.huawei.it.finance.opt.domain.entity.ExportTask;
import com.huawei.it.finance.opt.domain.enums.ExportTaskEnum;
import com.huawei.it.finance.opt.dsl.complier.objcode.ReserveWordInObjectCode;
import com.huawei.it.finance.opt.tech.edm.service.IEdmService;
import com.huawei.it.finance.opt.tech.enums.ModuleEnum;
import com.huawei.it.finance.opt.tech.util.DataSourceUtil;
import com.huawei.it.finance.opt.tech.util.UserUtil;
import com.huawei.it.finance.opt.wrap.dao.CellDimensionDao;
import com.huawei.it.finance.opt.wrap.entity.CellElement;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.config.ApplicationConfigProperties;
import com.huawei.it.jalor5.core.util.DateUtil;
import com.huawei.it.jalor5.core.util.PathUtil;
import com.huawei.us.common.file.UsFileLiteUtils;
import com.huawei.us.common.file.UsFileUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.awt.Color;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/v1/calculation_user_config/")
@JalorResource(code = "CheckGlobalAndRulesService", desc = "检查规则部署工具")
public class CheckGlobalAndRulesController {
    @Autowired
    private ICalculationUserConfigApplicationService iCalculationUserConfigApplicationService;

    /**
     * 按照模型id、作业空间Id生成针对
     * Global表和rule表的Excel文件，并且导出到【我的导出】变成附件
     *
     * @return 响应结果
     */
    @GetMapping("checkGlobalAndRules/{modelId}")
    public ApiResponse<List<CalculationUserConfig>> checkGlobalAndRules(@PathVariable String modelId) {
        queryGlobalAndRulesAndExport(modelId);
        return ApiResponse.success(iCalculationUserConfigApplicationService.findCalculationUserConfigs());
    }


    @GetMapping("testFindJar")
    public ApiResponse<List<CalculationUserConfig>> testFindJar() {
//        final String basePath = "https://cmc.centralrepo.rnd.huawei.com/artifactory/product_maven/com/huawei/his/framework/";
//        try {
//            String[] allLibs = initJars();
//            List<String> pom = new ArrayList<>();
//            List<String> restJars = new ArrayList<>();
//            for (int i = 0; i < allLibs.length; i++) {
//                String url = basePath+ allLibs[i];
//                if(i%10 == 0){
//                    System.out.println("Step==================="+i);
//                }
//                String result = OkHttpUtils.builder().requestUrl(url).get().sync();
//                Document document = Jsoup.parse(result);
//                List<Element> links = document.getElementsByTag("a");
//                // 只要最后1个版本号
//                boolean findVersion = false;
//                for (int j = links.size()-1 ; j > 0; j--) {
//                    Element link = links.get(j);
//                    if(link.hasText()){
//                        String version = link.text();
//                        if(version.contains("SNAPSHOT") || version.contains("RELEASE")){
//                            pom.add(buildXML(allLibs[i], version));
//                            findVersion = true;
//                            break;
//                        }
//                    }
//                }
//
//                if(!findVersion){
//                    restJars.add(allLibs[i]);
//                }
//            }
//            System.out.println("Pom组件===================");
//            for (String  s: pom) {
//                System.out.println("\""+s+"\",");
//            }
//            System.out.println("没找到的组件有======================");
//            for (String  s: restJars) {
//                System.out.println("\""+s+"\",");
//            }
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
        return ApiResponse.success();
    }

    private static String buildXML(String artifactId, String version){
        String xml = "<dependency><groupId>com.huawei.his.framework</groupId>"
                + "<artifactId>"+artifactId.replaceAll("/","")+"</artifactId><version>"+version+"</version></dependency>";
        return xml;
    }


    private static String [] initJars(){
        return new String[] {
                "Jalor-mesh-sdk", "alor-workflow-impl", "application", "arms-api-gateway-adapter-common",
                "arms-app-heds-starter", "arms-app-red-starter", "arms-app-starter", "arms-base", "arms-bom",
                "arms-client-common", "arms-client-common-heds", "arms-client-common-red", "arms-cluster-client",
                "arms-common-entities", "arms-component", "arms-config-center", "arms-config-center-common",
                "arms-config-center-component", "arms-config-center-heds", "arms-config-center-parent",
                "arms-config-center-red", "arms-curator", "arms-filter", "arms-gateway-heds-starter",
                "arms-gateway-red-starter", "arms-gateway-starter", "arms-ignored-app", "arms-logging-slf4j",
                "arms-mesh-actuator-prometheus", "arms-mesh-app-heds-starter", "arms-mesh-app-red-starter",
                "arms-mesh-app-starter", "arms-mesh-base", "arms-mesh-center-configstub", "arms-mesh-client-common",
                "arms-mesh-client-common-heds", "arms-mesh-client-common-red", "arms-mesh-client-common-servlet",
                "arms-mesh-client-common-webflux", "arms-mesh-gaia", "arms-mesh-gateway-heds-starter",
                "arms-mesh-gateway-red-starter", "arms-mesh-gateway-starter", "arms-mesh-iam-common",
                "arms-mesh-prometheus-reporterstub", "arms-mesh-push2kafka", "arms-mesh-pushgateway",
                "arms-mesh-sdk-parent", "arms-mesh-spring-boot-starter", "arms-mesh-spring-cloud-gateway-starter",
                "arms-olc-client", "arms-olc-rule-client", "arms-parameter-flow-controller", "arms-property-source",
                "arms-sdk", "arms-sdk-common", "arms-sdk-common-entities", "arms-sdk-gateway", "arms-sdk-internal",
                "arms-sdk-olc", "arms-sdk-parent", "arms-sdk-sentinel", "arms-sentinel-app-spring-boot-starter",
                "arms-sentinel-client-common", "arms-sentinel-core", "arms-sentinel-gateway-spring-boot-starter",
                "arms-spring-boot-starter", "arms-spring-cloud-gateway-adapter", "arms-spring-cloud-gateway-starter",
                "authority-system-cs", "authority-tenant-connector", "authority-tenant-cs", "bpm-celonbpm-sdk-common",
                "bpm-celonbpm-sdk-jalor", "bpm-cloud-sdk", "bpm-cloud-sdk-jalor", "bpm-cloud-sdk-spring",
                "bpm-example-jalor-biz", "bpm-example-rest-test", "bpm-example-spring-biz", "bpm-external-sdk-common",
                "bpm-external-sdk-jalor", "bpm-external-sdk-spring", "bpm-heds-sdk-jalor", "bpm-heds-sdk-spring",
                "bpm-platform-admin-rest", "bpm-platform-apptoken", "bpm-platform-archiving", "bpm-platform-batch",
                "bpm-platform-common", "bpm-platform-console-rest", "bpm-platform-core", "bpm-platform-delegate",
                "bpm-platform-es", "bpm-platform-event", "bpm-platform-excel", "bpm-platform-ext6", "bpm-platform-iam",
                "bpm-platform-identity", "bpm-platform-jks", "bpm-platform-kafka", "bpm-platform-meta",
                "bpm-platform-multitenant", "bpm-platform-mybatis", "bpm-platform-oneaccess",
                "bpm-platform-operation-log", "bpm-platform-rediscache", "bpm-platform-runtime-rest",
                "bpm-platform-security", "bpm-platform-session", "bpm-platform-sso", "bpm-platform-sso-common",
                "bpm-platform-switchemployee", "bpm-platform-xss", "bpm-starter-common", "bpm-starter-console",
                "bpm-starter-platform-admin", "bpm-starter-runtime-engine", "celon-bpm-examples", "celon-bpm-external",
                "celon-bpm-hcs", "celon-bpm-his-cloud", "celon-bpm-his-common", "celon-bpm-his-inner",
                "celon-bpm-parent", "celon-bpm-spring-boot-starter-root", "celon-bpm-starter",
                "celon-bpm-starter-oncloud", "celon-bpm-todo-clouddragon", "celon-bpm-todo-inner",
                "celon-bpm-todo-liveflow", "celon-bpm-todo-xfusion", "celon-bpm-todo-yunshan", "celon-heds-external",
                "cloud", "commons", "components", "core", "cs-services", "domain", "errorcenter-management",
                "errorcenter-maven-plugin", "framework", "gateway-dynamic-route", "gateway-metrics", "gov-bom", "his-x",
                "his1", "his2", "his3", "horiz-auth", "horiz-auth-heds", "horiz-auth-his", "horiz-basedata-heds",
                "horiz-basedata-his", "horiz-broker-components", "horiz-cfservice", "horiz-charge-common",
                "horiz-charge-heds", "horiz-charge-his", "horiz-charge-kafka", "horiz-common", "horiz-other-common",
                "horiz-other-heds", "horiz-other-his", "horiz-perception-common", "horiz-perception-heds",
                "horiz-perception-his", "horiz-perception-kafka", "horiz-subscription-collaborator",
                "horiz-subscription-common", "horiz-subscription-heds", "horiz-subscription-his", "huawei-security-base",
                "huawei-security-compatible", "huawei-security-conformable", "huawei-security-dynamicsalt",
                "huaweiSecurity2", "huaweiSecurity2-base", "huaweiSecurity2-plugin", "huaweiSecurity2-plugin-compatible",
                "huaweiSecurity2-plugin-conformable", "huaweiSecurity2-plugin-dynamicsalt", "hwc", "infrastructure",
                "interfaces", "ischeduler-client-base", "ischeduler-encrypt", "ischeduler-encrypt-base",
                "ischeduler-encrypt-defaultencload", "ischeduler-encrypt-test", "jalor-aas-dimension", "jalor-admin",
                "jalor-admin-api", "jalor-aegis", "jalor-aegis-common", "jalor-aegis-nonsecure", "jalor-aes-adaptation",
                "jalor-aes-default", "jalor-ai-api", "jalor-ai-bom", "jalor-ai-demo", "jalor-ai-impl",
                "jalor-annotation-scanner-api", "jalor-annotation-scanner-common", "jalor-annotation-scanner-core",
                "jalor-annotation-scanner-impl", "jalor-api", "jalor-apiauth-apigw", "jalor-apiauth-consumer",
                "jalor-apiauth-consumer-his3", "jalor-apiauth-iam", "jalor-apiauth-iam-consumer-his3",
                "jalor-apiauth-iam-provider-his3", "jalor-apiauth-provider", "jalor-apiauth-sgov",
                "jalor-apiauth-sgov-consumer-his3", "jalor-apiauth-sgov-provider-his3", "jalor-appInfo-register",
                "jalor-arms-arwen", "jalor-arms-component", "jalor-arms-curator", "jalor-arms-filter", "jalor-arms-log",
                "jalor-arms-mesh-app-starter", "jalor-arms-mesh-base", "jalor-arms-mesh-client-common",
                "jalor-arms-mesh-client-common-servlet", "jalor-arms-mesh-client-common-webflux",
                "jalor-arms-mesh-gateway-starter", "jalor-arms-mesh-iam-common", "jalor-arms-mesh-spring-boot-starter",
                "jalor-arms-mesh-spring-cloud-gateway-starter", "jalor-arms-sso", "jalor-arms-token-server",
                "jalor-artifacts", "jalor-async-mqs", "jalor-async-task-impl", "jalor-atomikos-patch",
                "jalor-attachment-api", "jalor-attachment-common", "jalor-attachment-impl", "jalor-audit",
                "jalor-audit-aop", "jalor-auth-base", "jalor-auth-iam", "jalor-auth-iam-his3", "jalor-auth-idaas",
                "jalor-auth-jwt", "jalor-auth-mock", "jalor-auth-sso", "jalor-auth-sso-his3", "jalor-authentication",
                "jalor-authentication-oneaccess", "jalor-authentication-orgid", "jalor-authentication-saml",
                "jalor-authentication-welink", "jalor-authorization", "jalor-biz-template-archetype", "jalor-bizenv",
                "jalor-boot", "jalor-broker", "jalor-broker-heds-starter-core", "jalor-broker-his-starter-core",
                "jalor-broker-parent", "jalor-business-event", "jalor-cache", "jalor-cache-caffeine",
                "jalor-cache-composite", "jalor-cache-mixed", "jalor-cache-redis", "jalor-circuit-breaker",
                "jalor-clean-cache", "jalor-clean-cache-impl", "jalor-cleaner", "jalor-cluster", "jalor-cluster-admin",
                "jalor-cluster-autoupdate", "jalor-cluster-eureka", "jalor-cluster-nacos", "jalor-collaborator",
                "jalor-common-utils", "jalor-commons", "jalor-components-his3", "jalor-config", "jalor-config-apollo",
                "jalor-config-configcenter", "jalor-config-default-crypto", "jalor-config-defaultencload",
                "jalor-config-dynamic", "jalor-config-pcloud", "jalor-config-plugin",
                "jalor-config-plugin-global-userid", "jalor-config-welink-apollo", "jalor-configcenter-default",
                "jalor-configcenter-generate9dynamictoken", "jalor-configcenter-huaweiSecurity2", "jalor-core",
                "jalor-cs-client-stub", "jalor-cs-common", "jalor-cs-db-script", "jalor-cs-permission", "jalor-cs-proxy",
                "jalor-cs-saas", "jalor-cs-yunshan", "jalor-csrf", "jalor-data-pipeline", "jalor-data-sync",
                "jalor-data-sync-iam", "jalor-datalock-api", "jalor-datalock-impl", "jalor-dependencies",
                "jalor-deprecated", "jalor-dispersed-cacheclean-api", "jalor-dispersed-cacheclean-biz",
                "jalor-dispersed-cacheclean-compatible", "jalor-dispersed-cacheclean-cs",
                "jalor-dispersedCacheClean-api", "jalor-dispersedCacheClean-biz", "jalor-dispersedCacheClean-cs",
                "jalor-distributed-cache-session", "jalor-distributedCacheSession", "jalor-download",
                "jalor-dynamic-routing", "jalor-ebg-mapping", "jalor-ebg-mapping-api", "jalor-ebg-program",
                "jalor-edm-admin", "jalor-edm-common", "jalor-ehcache", "jalor-error-center",
                "jalor-error-center-client", "jalor-error-center-collector", "jalor-error-center-common",
                "jalor-error-center-maven-plugin", "jalor-error-center-microservice", "jalor-error-center-services",
                "jalor-error-center-sheduler", "jalor-error-collector", "jalor-excel-api", "jalor-excel-impl",
                "jalor-gateway-api", "jalor-gateway-apollo", "jalor-gateway-boot-dependencies",
                "jalor-gateway-configcenter", "jalor-gateway-core", "jalor-gateway-data-sync", "jalor-gateway-demo",
                "jalor-gateway-dependencies", "jalor-gateway-discovery", "jalor-gateway-discovery-eureka",
                "jalor-gateway-discovery-nacos", "jalor-gateway-ext-dependencies", "jalor-gateway-heds-starter",
                "jalor-gateway-his", "jalor-gateway-his-apollo", "jalor-gateway-huaweiSecurity",
                "jalor-gateway-huaweicloud-starter", "jalor-gateway-iam", "jalor-gateway-icsl", "jalor-gateway-jalor",
                "jalor-gateway-jalor-authc", "jalor-gateway-log-desensitization", "jalor-gateway-log4j-security",
                "jalor-gateway-log4j2", "jalor-gateway-log4j2-security", "jalor-gateway-logback", "jalor-gateway-mqs",
                "jalor-gateway-netty-dependencies", "jalor-gateway-oncloud-starter", "jalor-gateway-parent",
                "jalor-gateway-prometheus", "jalor-gateway-redis", "jalor-gateway-route",
                "jalor-gateway-rxjava-reactive-streams", "jalor-gateway-security", "jalor-gateway-security-apig",
                "jalor-gateway-security-auth", "jalor-gateway-security-cors", "jalor-gateway-security-csb",
                "jalor-gateway-security-csrf", "jalor-gateway-security-iam", "jalor-gateway-security-iam-client",
                "jalor-gateway-security-jwt", "jalor-gateway-security-jwt-simple", "jalor-gateway-security-oneaccess",
                "jalor-gateway-security-permission", "jalor-gateway-security-private", "jalor-gateway-security-public",
                "jalor-gateway-security-session", "jalor-gateway-security-sgov", "jalor-gateway-security-sso",
                "jalor-gateway-security-sso-multidomain", "jalor-gateway-security-support",
                "jalor-gateway-security-user-permission", "jalor-gateway-sentinel", "jalor-gateway-server",
                "jalor-gateway-spring-dependencies", "jalor-gateway-standard-api", "jalor-gateway-starter",
                "jalor-gateway-tracer", "jalor-gateway-udp", "jalor-gov", "jalor-gov-api-services", "jalor-gov-common",
                "jalor-gov-eureka-services", "jalor-gov-framework-version", "jalor-gov-gwusage-service", "jalor-gov-iam",
                "jalor-gov-platform", "jalor-gov-ratelimiter", "jalor-gov-sdk", "jalor-gov-services",
                "jalor-gov-version", "jalor-gray-adapter", "jalor-heds-components", "jalor-heds-idata",
                "jalor-heds-iexcel", "jalor-heds-mqs", "jalor-heds-scene-identity", "jalor-helper",
                "jalor-his-components", "jalor-hr-admin", "jalor-htmlarea-admin", "jalor-httpclient-httpcomponents",
                "jalor-httpclient-httpcomponents-ssl", "jalor-https", "jalor-https-security", "jalor-hw-mqs",
                "jalor-hwkafka", "jalor-hystrix-client", "jalor-hystrix-service", "jalor-i18n", "jalor-i18n-admin",
                "jalor-iam", "jalor-id-service", "jalor-idata", "jalor-idata-his3", "jalor-idata-huawei",
                "jalor-idata-huawei-blue", "jalor-iexcel", "jalor-iexcel-huawei", "jalor-iexcel-impl",
                "jalor-inspection", "jalor-integration-api", "jalor-ischeduler", "jalor-ischeduler-client",
                "jalor-ischeduler-dependencies", "jalor-jcache", "jalor-jwt", "jalor-loadbalancer",
                "jalor-loadbalancer-core", "jalor-loadbalancer-next", "jalor-logger", "jalor-logger-detected",
                "jalor-login", "jalor-logout-mqs", "jalor-lookup-impl", "jalor-mail-adaptation-encrypt",
                "jalor-mail-api", "jalor-mail-impl", "jalor-maven-plugin", "jalor-mesh-app-starter",
                "jalor-mesh-pilot-portal", "jalor-mesh-piolt", "jalor-message-api", "jalor-message-impl",
                "jalor-message-welink", "jalor-metrics", "jalor-metrics-annotation", "jalor-metrics-jmx",
                "jalor-metrics-prometheus", "jalor-model", "jalor-monitor-jmx", "jalor-monitor-jmx-dbcp",
                "jalor-monitor-jmx-dbcp2", "jalor-monolithic-login", "jalor-mqs", "jalor-msa", "jalor-multilookup-impl",
                "jalor-n10n-common", "jalor-n10n-publisher", "jalor-n10n-publisher-redis", "jalor-n10n-redis",
                "jalor-n10n-subscriber", "jalor-n10n-subscriber-redis", "jalor-n10n-subscriber-web", "jalor-org-admin",
                "jalor-orm", "jalor-orm-fix-resourceException", "jalor-orm-tenancy", "jalor-partition-utils",
                "jalor-permission", "jalor-permissionapply-api", "jalor-permissionapply-impl",
                "jalor-personalized-admin", "jalor-poi-ooxml-lite", "jalor-policy", "jalor-privacy", "jalor-probes",
                "jalor-registration", "jalor-registration-eureka", "jalor-registration-nacos", "jalor-registry-api",
                "jalor-registry-impl", "jalor-risk-check", "jalor-role-mapping", "jalor-routable-datasource",
                "jalor-rpc", "jalor-rpc-common", "jalor-rpc-stub", "jalor-rpc-stub-htmlarea", "jalor-rpc-tenant-stub",
                "jalor-rpc-vega", "jalor-rules-api", "jalor-rules-impl", "jalor-runtime", "jalor-saas-api",
                "jalor-saas-client", "jalor-saas-client-mgr", "jalor-saas-common", "jalor-saas-consumer-mqs",
                "jalor-saas-model", "jalor-saas-platform", "jalor-saas-publisher-mqs", "jalor-saas-query-service",
                "jalor-saas-redis", "jalor-saas-share", "jalor-saas-tenant", "jalor-saas-tenant-event",
                "jalor-saas-tenant-xac", "jalor-saas-web-resource", "jalor-saas-web-resource-hisconsole",
                "jalor-scene-identity", "jalor-scheduler-artifacts", "jalor-scheduler-base", "jalor-scheduler-client",
                "jalor-scheduler-client-api", "jalor-scheduler-client-demo", "jalor-scheduler-client-plugin",
                "jalor-scheduler-encrypt", "jalor-scheduler-encrypt-aes", "jalor-scheduler-encrypt-aesgcm",
                "jalor-scheduler-encrypt-old", "jalor-scheduler-high-node", "jalor-scheduler-job",
                "jalor-scheduler-recall-old", "jalor-scheduler-rpc-stub", "jalor-scheduler-starter",
                "jalor-scheduler-starter-blue", "jalor-scheduler-web", "jalor-security-access", "jalor-security-admin",
                "jalor-security-api", "jalor-security-plugin", "jalor-security-redis-serializer",
                "jalor-security-springmvc-soa", "jalor-sentinel", "jalor-sentinel-app-common",
                "jalor-sentinel-app-spring-boot-starter", "jalor-sentinel-client", "jalor-sentinel-client-common",
                "jalor-sentinel-cluster", "jalor-sentinel-cluster-server", "jalor-sentinel-component",
                "jalor-sentinel-core", "jalor-sentinel-dashboard-sso", "jalor-sentinel-gateway-spring-boot-starter",
                "jalor-sentinel-log", "jalor-servlet-collection", "jalor-sgov-huawei", "jalor-sitemap-admin",
                "jalor-spring-session-flexible", "jalor-starter-admin", "jalor-starter-ai", "jalor-starter-apollo",
                "jalor-starter-arms-ratelimiter", "jalor-starter-audit", "jalor-starter-auth-sso",
                "jalor-starter-authorization", "jalor-starter-biz", "jalor-starter-biz-blue", "jalor-starter-card",
                "jalor-starter-cluster-biz", "jalor-starter-cluster-cs", "jalor-starter-configcenter",
                "jalor-starter-core", "jalor-starter-crud", "jalor-starter-cs", "jalor-starter-cs-client",
                "jalor-starter-cs-iam", "jalor-starter-cs-idaas", "jalor-starter-cxf", "jalor-starter-download",
                "jalor-starter-edm", "jalor-starter-edm-cs", "jalor-starter-eureka", "jalor-starter-excel-biz",
                "jalor-starter-excel-cs", "jalor-starter-gov", "jalor-starter-hs2", "jalor-starter-iam",
                "jalor-starter-idata", "jalor-starter-login-biz", "jalor-starter-login-cs", "jalor-starter-mail",
                "jalor-starter-monolithic", "jalor-starter-multiple-tenant", "jalor-starter-nacos", "jalor-starter-orm",
                "jalor-starter-portal", "jalor-starter-programs", "jalor-starter-s3", "jalor-starter-servicecontract",
                "jalor-starter-small-biz", "jalor-starter-springsession", "jalor-starter-stub",
                "jalor-starter-switchemployee", "jalor-starter-tenant-biz", "jalor-starter-upload", "jalor-starter-usf",
                "jalor-starter-vegahsa", "jalor-starter-web-resource", "jalor-starter-workflow-biz",
                "jalor-starter-workflow-cs", "jalor-starters", "jalor-store-hs2", "jalor-store-local", "jalor-store-obs",
                "jalor-store-s3", "jalor-studio-backend", "jalor-studio-common", "jalor-studio-his",
                "jalor-switchemployee", "jalor-system-desensitization", "jalor-tenant-cs-api", "jalor-tenant-cs-impl",
                "jalor-todo-api", "jalor-todo-welink", "jalor-tracer", "jalor-tracer-apm", "jalor-tss", "jalor-upload",
                "jalor-user-hw", "jalor-user-saml", "jalor-user-welink", "jalor-usf-client", "jalor-usf-common",
                "jalor-usf-dependencies", "jalor-usf-security", "jalor-usf-server", "jalor-usf-validation",
                "jalor-validator", "jalor-wars-deploy", "jalor-web", "jalor-web-resource", "jalor-web-support",
                "jalor-webresource-plugins", "jalor-webservice-support", "jalor-webservice-support-sgov",
                "jalor-websocket", "jalor-welink-transport-sdk", "jalor-workflow-api", "jalor-workflow-commmon",
                "jalor-workflow-impl", "jalor-x-auth-idaas", "jalor-x-cache-redis", "jalor-x-components",
                "jalor-x-config-configcenter", "jalor-x-idata-his3", "jalor-x-idata-huawei", "jalor-x-iexcel-impl",
                "jalor-x-mqs", "jalor-x-starter-biz", "jalor-x-starter-configcenter", "jalor-x-starter-cs",
                "jalor-x-starter-excel-biz", "jalor-x-starter-excel-cs", "jalor-x-starter-idata",
                "jalor-x-starter-monolithic", "jalor-x-starters", "jalor-x-store-s3", "jalor-xauth", "jalor-xauth-api",
                "jalor-xauth-impl", "jalor-xauth-mqs", "jalor5.log.desensitization", "jalor5.test", "jalor5.xauth.impl",
                "jalor6-welink-base", "jalor7-deprecated", "jalormesh-gateway-apollo",
                "jalormesh-gateway-boot-dependencies", "jalormesh-gateway-configcenter", "jalormesh-gateway-core",
                "jalormesh-gateway-ext-dependencies", "jalormesh-gateway-log4j2", "jalormesh-gateway-parent",
                "jalormesh-gateway-redis", "jalormesh-gateway-route", "jalormesh-gateway-security-auth",
                "jalormesh-gateway-security-cors", "jalormesh-gateway-security-csrf", "jalormesh-gateway-security-iam",
                "jalormesh-gateway-security-iam-client", "jalormesh-gateway-security-jwt",
                "jalormesh-gateway-security-permission", "jalormesh-gateway-security-session",
                "jalormesh-gateway-security-sgov", "jalormesh-gateway-security-sso",
                "jalormesh-gateway-security-support", "jalormesh-gateway-server", "jalormesh-gateway-starter",
                "jalormesh-starter-biz", "jalormesh-starter-cs", "jalormesh-starter-gateway", "mes-security-admin",
                "mes-security-api", "metadata_reportify_sdk", "metric_reportify_sdk", "pcloud.env.reader",
                "runtime_metadata_sdk", "saas-errorcenter", "saas-errorcenter-client", "security-common", "security-jwt",
                "security-jwt-api", "security-jwt-auth0", "security-jwt-common", "security-jwt-impl",
                "security-jwt-jjwt", "security-sgov", "security-starter", "security-usf-handler",
                "security-usf-handler-starter-auth0", "security-usf-handler-starter-jjwt",
                "security-usf-handler-starter-sgov", "sensitive.exception.filter", "sentinel-adapter",
                "sentinel-api-common", "sentinel-api-gateway-adapter-common", "sentinel-dashboard", "sentinel-extension",
                "sentinel-parameter-flow-control", "sentinel-parent", "sentinel-spring-cloud-gateway-adapter",
                "sentinel-transport-simple-http-ext", "serviceframework-breaker", "solomon-api",
                "solomon-biz-template-archetype", "solomon-distributedlock-local", "solomon-lightsdk-parent",
                "solomon-sdk-jalor", "solomon-sdk-java", "solomon-sdk-springboot", "testDeploy"
        };
    }

    @Autowired
    private IExportTaskAppService exportTaskService;

    @Autowired
    private CellDimensionDao cellDimensionDao;

    @Resource
    private CalculationRuleDao calculationRuleDao;

    @Autowired
    private IEdmService edmService;

    private static final String EUREKA_REGISTRATION = "registry.eureka.registration.enabled";
    private void queryGlobalAndRulesAndExport(String modelId){
        String eurekaRegistrationEnabled = ApplicationConfigProperties.getContextProperty(EUREKA_REGISTRATION, "true");
        String pathByDay = "";
        if ("false".equals(eurekaRegistrationEnabled)) {
            pathByDay = "d:/"+PathUtil.getPathByDay(ModuleEnum.RULE.getCode());
        } else {
            pathByDay = PathUtil.getPathByDay(ModuleEnum.RULE.getCode());
        }
        ExportTask exportTask = new ExportTask();
        File excelFile = null;
        try {
            PathUtil.makeDirs(UsFileLiteUtils.getFile(pathByDay));
            String dataBasePath = pathByDay + File.separator + "data";
            File dirFile = UsFileUtils.getFile(dataBasePath);
            PathUtil.makeDirs(dirFile);

            // 查询global
            CellElement cellElement = new CellElement();
            cellElement.setWorkspaceId(DataSourceUtil.getWorkspaceId());
            cellElement.setModelId(modelId);
            List<CellElement> globalCells = cellDimensionDao.findGlobalCellElements(cellElement);

            // 查询所有rule
            CalculationRule rule = new CalculationRule();
            rule.setWorkspaceId(DataSourceUtil.getWorkspaceId());
            rule.setGroupId(modelId);
            List<CalculationRule> rules  = calculationRuleDao.findCalculationRules(rule);

            if (CollectionUtils.isNotEmpty(globalCells)) {
                excelFile = new File(generateExcelFile(dataBasePath, globalCells, rules));
                // 创建导出任务
                exportTask = createExportTask(excelFile.getName());
                // 上传
                DocumentVO documentVO = edmService.edmUploadFile(excelFile.getCanonicalPath());
                // 导出完成，获取URL并更新任务 exportTask
                if (documentVO != null && StringUtils.isNotBlank(documentVO.getDocId())) {
                    updateSuccessExportTask(exportTask, documentVO, 1);
                } else {
                    updateFaildExportTask(exportTask, null, 0);
                }
            }
        } catch (Throwable ex) {
            ex.printStackTrace();
            printExceptionLog(exportTask, pathByDay, ex);
        } finally {
            if (excelFile != null) {
                excelFile.deleteOnExit();
            }
        }
    }



    private static String getExcelPathName() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TIMESTAMP_STRING);
        return "RULE_CHECK_" + LocalDateTime.now().format(formatter);
    }

    private ExportTask createExportTask(String filePathName) {
        ExportTask exportTask = new ExportTask();
        exportTask.setCreatedBy(UserUtil.getUser("USERID"));
        exportTask.setStatus(ExportTaskEnum.RUN.getCode());
        exportTask.setFileName(filePathName);
        exportTask.setModuleName(ModuleEnum.FORM.getMessage());
        exportTask.setRunStartTime(LocalDateTime.now());
        exportTaskService.createExportTask(exportTask);
        return exportTask;
    }

    private void updateFaildExportTask(ExportTask exportTask, DocumentVO documentVO, int records) {
        exportTask.setStatus(ExportTaskEnum.FAIL.getCode());
        setCommonExportTask(exportTask, documentVO, records);
        exportTaskService.updateExportTask(exportTask);
    }

    private void updateSuccessExportTask(ExportTask exportTask, DocumentVO documentVO, int records) {
        exportTask.setStatus(ExportTaskEnum.SUCCESS.getCode());
        // 设置公共字段
        setCommonExportTask(exportTask, documentVO, records);
        exportTaskService.updateExportTask(exportTask);
    }

    private void setCommonExportTask(ExportTask exportTask, DocumentVO documentVO, int records) {
        if (documentVO != null) {
            exportTask.setDocId(documentVO.getDocId());
            String docSize = documentVO.getDocSize();
            exportTask.setFileSize(StringUtils.isNotBlank(docSize) ? Long.valueOf(docSize) : 0);
        }
        exportTask.setRunEndTime(LocalDateTime.now());
        exportTask.setRecords(records);
    }

    private String generateExcelFile(String dataBasePath, List<CellElement> globalCells , List<CalculationRule> rules) {
        String excelFileName = dataBasePath + File.separator + getExcelPathName() + ".xlsx";
        System.out.println("excelFileName = "+ excelFileName);
        Workbook workbook = new XSSFWorkbook();
        Sheet globalSheet = workbook.createSheet("global");
        XSSFCellStyle style = getCellStyle(globalSheet);
        // 写入Title
        String [] globalTitles = {"cell_element_id" , "model_id" , "cell_element_short_name", "workspace_id","creation_date"};
        writeTitle(globalSheet, 5, Arrays.asList(globalTitles), 0, true, style, 1 );
        // 写入数据
        writeGlobalData(globalSheet,  globalCells, 1);

        Sheet ruleSheet = workbook.createSheet("rule");
        // 写入Title
        String [] ruleTitles = {"unit_id" , "model_id" , "rich_content", "unit_code", "workspace_id","creation_date"};
        writeTitle(ruleSheet, 6, Arrays.asList(ruleTitles), 0, true, style, 1 );
        // 写入数据
        writeRulesData(ruleSheet,  globalCells , rules, 1);

        try (FileOutputStream fileOutputStream = new FileOutputStream(excelFileName)) {
            workbook.write(fileOutputStream);
            fileOutputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return excelFileName;
    }

    private void writeTitle(Sheet dataSheet, int totalCount, List<String> titles, int startIndex,
                           boolean isTitleData, XSSFCellStyle style, int columnIndex) {
        XSSFCellStyle borderStyle = getBorderStyle(dataSheet);
        XSSFCellStyle columnStyle = getColumnStyle(dataSheet);
        for (int i = startIndex; i < totalCount; i++) {
            Row row = dataSheet.createRow(i);
            for (int j = 0; j < titles.size(); j++) {
                Cell cell = row.createCell(j);
                String cellValue = titles.get(j);
                setCellValue(cell, cellValue);
                cell.setCellStyle(borderStyle);
                setColumnStyle(isTitleData, columnIndex, columnStyle, j, cell);
                if (isTitleData) {
                    cell.setCellStyle(style);
                    dataSheet.setColumnWidth(j, 5120);
                }
            }
        }
    }


    private void writeGlobalData(Sheet dataSheet, List<CellElement> cellElements, int startIndex) {
        XSSFCellStyle dataCellStyle = getBackGroundStyle(dataSheet);
        for (int i = startIndex; i < cellElements.size(); i++) {
            CellElement cellElement = cellElements.get(i);
            Row row = dataSheet.createRow(i);
            addCell(row, 0, String.valueOf(cellElement.getCellElementId()), dataCellStyle);
            addCell(row, 1, cellElement.getModelId(), dataCellStyle);
            addCell(row, 2, cellElement.getCellElementShortName(), dataCellStyle);
            addCell(row, 3, String.valueOf(cellElement.getWorkspaceId()), dataCellStyle);
            addCell(row, 4, DateUtil.date24ToString( cellElement.getCreationDate()), dataCellStyle);
        }
    }

    private void writeRulesData(Sheet dataSheet, List<CellElement> globalCells, List<CalculationRule> rules, int startIndex) {
        // id->code Map
        Map<Long, CellElement> cellIdMap = globalCells.stream().collect(Collectors.toMap(CellElement::getCellElementId, Function.identity(), (v1, v2) -> v1));
        XSSFCellStyle dataCellStyle = getBackGroundStyle(dataSheet);
        for (int i = startIndex; i < rules.size(); i++) {
            CalculationRule rule = rules.get(i);
            Row row = dataSheet.createRow(i);

            String content = "";
            List<String> factors = splitFactors(rule);
            for (String factor: factors) {
                if(factor.startsWith("__") && factor.endsWith("__")){
                    String unitId = factor.replaceAll("_", "");
                    String unitCode = cellIdMap.get(Long.parseLong(unitId)).getCellElementShortName();
                    content += unitCode;
                } else {
                    content += factor;
                }
            }

            addCell(row, 0, String.valueOf(rule.getUnitId()), dataCellStyle);
            addCell(row, 1, rule.getGroupId(), dataCellStyle);
            addCell(row, 2, content, dataCellStyle);
            addCell(row, 3, rule.getUnitCode(), dataCellStyle);
            addCell(row, 4, String.valueOf(rule.getWorkspaceId()), dataCellStyle);
            addCell(row, 5, DateUtil.date24ToString( rule.getCreationDate()), dataCellStyle);
        }
    }


    private static final XSSFColor TITLE_FOREGROUND_COLOR = new XSSFColor(new Color(180, 198, 231), null);

    private void addCell(Row row, int index,  String value, XSSFCellStyle dataCellStyle) {
        Cell cell = row.createCell(index);
        setCellValue(cell, value);
        cell.setCellStyle(dataCellStyle);
    }

    private static XSSFCellStyle getCellStyle(Sheet worksheet) {
        XSSFCellStyle cellStyle = (XSSFCellStyle) worksheet.getWorkbook().createCellStyle();
        Font font = worksheet.getWorkbook().createFont();
        font.setBold(true);
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setFillForegroundColor(TITLE_FOREGROUND_COLOR);
        setBorderStyle(cellStyle);
        return cellStyle;
    }

    private void setColumnStyle(boolean isTitleData, int columnIndex, XSSFCellStyle columnStyle, int j, Cell cell) {
        if (!isTitleData && j < columnIndex) {
            cell.setCellStyle(columnStyle);
        }
    }

    private XSSFCellStyle getColumnStyle(Sheet dataSheet) {
        XSSFCellStyle cellStyle = (XSSFCellStyle) dataSheet.getWorkbook().createCellStyle();
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setFillForegroundColor(TITLE_FOREGROUND_COLOR);
        setBorderStyle(cellStyle);
        return cellStyle;
    }

    private void setCellValue(Cell cell, String cellValue) {
        if (com.huawei.it.finance.opt.tech.util.StringUtils.isBlank(cellValue)) {
            cell.setCellValue(Strings.EMPTY);
        } else {
            cell.setCellValue(cellValue);
        }
    }

    private XSSFCellStyle getBorderStyle(Sheet dataSheet) {
        XSSFCellStyle borderStyle = (XSSFCellStyle) dataSheet.getWorkbook().createCellStyle();
        setBorderStyle(borderStyle);
        return borderStyle;
    }

    private XSSFCellStyle getBackGroundStyle(Sheet dataSheet) {
        XSSFCellStyle dataCellStyle = (XSSFCellStyle) dataSheet.getWorkbook().createCellStyle();
        dataCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        dataCellStyle.setFillForegroundColor(new XSSFColor(new Color(235, 235, 235), null));
        setBorderStyle(dataCellStyle);
        return dataCellStyle;
    }

    private static void setBorderStyle(XSSFCellStyle dataCellStyle) {
        dataCellStyle.setBorderTop(BorderStyle.THIN);
        dataCellStyle.setBorderBottom(BorderStyle.THIN);
        dataCellStyle.setBorderLeft(BorderStyle.THIN);
        dataCellStyle.setBorderRight(BorderStyle.THIN);
    }

    private static String TIMESTAMP_STRING = "yyyyMMddHHmmss";

    private static List<String> splitFactors(CalculationRule rule) {
        List<String> rules = new ArrayList<>();
        rules.add(ReserveWordInObjectCode.ID_BORDER + rule.getUnitId() + ReserveWordInObjectCode.ID_BORDER);
        rules.add(ReserveWordInObjectCode.SET);
        String ruleContent = rule.getContent();
        int maxIdx = ruleContent.length() - 1;
        int factorStartIdx = 0;
        int factorEndIdx;
        int start = 0;
        for (; factorStartIdx <= maxIdx; factorStartIdx++) {
            if (ruleContent.charAt(factorStartIdx) != '_') {
                // i是'_'的时候进入匹配factor
                continue;
            }
            factorEndIdx = factorStartIdx;
            // 前置检查:位置不够or前置不匹配 __\d
            if (factorEndIdx + 4 > maxIdx || ruleContent.charAt(factorEndIdx + 1) != '_'
                    || !Character.isDigit(ruleContent.charAt(factorEndIdx + 2))) {
                continue;
            }
            // 中间检查:中间是数字
            factorEndIdx = factorEndIdx + 3;
            for (; factorEndIdx <= maxIdx - 2; factorEndIdx++) {
                if (!Character.isDigit(ruleContent.charAt(factorEndIdx))) {
                    break;
                }
            }
            // 后置检查:需要是'__', 不匹配->子串不符合, 直接偏移
            if (ruleContent.charAt(factorEndIdx) != '_' || ruleContent.charAt(factorEndIdx + 1) != '_') {
                factorStartIdx = factorEndIdx + 1;
                continue;
            }
            factorEndIdx = factorEndIdx + 1;
            // 收录非factor以及factor
            if (factorStartIdx > start) {
                rules.add(ruleContent.substring(start, factorStartIdx));
            }
            rules.add(ruleContent.substring(factorStartIdx, factorEndIdx + 1));
            factorStartIdx = factorEndIdx;
            start = factorEndIdx + 1;
        }
        if (factorStartIdx > start) {
            // 收录余下的非factor
            rules.add(ruleContent.substring(start, factorStartIdx));
        }
        return rules;
    }

    private void printExceptionLog(ExportTask exportTask, String pathByDay, Throwable ex) {
        String logPath = pathByDay + File.separator + "log";
        PathUtil.makeDirs(UsFileLiteUtils.getFile(logPath));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TIMESTAMP_STRING);
        File logFile = UsFileUtils.getFile(String.format(Locale.ROOT, "%s%s%s%s%s", logPath, File.separator, "log",
                LocalDateTime.now().format(formatter), ".txt"));
        try (PrintWriter writer = new PrintWriter(new FileWriter(logFile, true))) {
            writer.println(ex.getMessage());
            writer.flush();
            DocumentVO documentVO = edmService.edmUploadFile(logFile.getCanonicalPath());
            if (documentVO != null && StringUtils.isNotBlank(documentVO.getDocId())) {
                updateFaildExportTask(exportTask, documentVO, 1);
            } else {
                updateFaildExportTask(exportTask, null, 0);
            }
        } catch (IOException | EdmException e) {
            throw new CalculationException("An exception occurred when printing logs.", e);
        } finally {
            if (logFile != null) {
                logFile.deleteOnExit();
            }
        }
    }
}