/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.application.service;

import com.huawei.it.finance.opt.base.entity.CalculationScript;
import com.huawei.it.finance.opt.base.entity.CalculationScriptModel;
import com.huawei.it.finance.opt.base.entity.CalculationScripts;
import com.huawei.it.finance.opt.base.entity.RulesCompare;
import com.huawei.it.finance.opt.base.entity.ScriptFolder;
import com.huawei.it.finance.opt.base.entity.ScriptModel;
import com.huawei.it.finance.opt.base.protocol.ApiResponse;
import com.huawei.it.finance.opt.base.vo.CalculationScriptRelativeModels;
import com.huawei.it.finance.opt.base.vo.ScriptModelVO;
import com.huawei.it.finance.opt.base.workflow.deploy.DslDeployContext;
import com.huawei.it.finance.opt.base.workflow.deploy.dto.DeployCellParam;
import com.huawei.it.finance.opt.dsl.complier.output.DeployEffect;
import com.huawei.it.finance.opt.fsp.pub.common.exception.DataSourceException;
import com.huawei.it.finance.opt.task.entity.QuartzTaskInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ICalculationScriptApplicationService {
    /**
     * 根据ID查询计算脚本
     *
     * @param scriptId 脚本ID
     * @return
     */
    CalculationScript getCalculationScriptById(Long scriptId);

    /**
     * 规则脚本保存功能
     *
     * @param calculationScript 计算脚本
     */
    CalculationScript saveCalculationScript(CalculationScript calculationScript);

    /**
     * 规则脚本更新功能
     *
     * @param calculationScript 计算脚本
     */
    void updateCalculationScript(CalculationScript calculationScript);

    /**
     * 规则脚本粘贴功能
     *
     * @param calculationScript 计算脚本
     */
    void pasteCalculationScript(CalculationScript calculationScript);

    /**
     * 规则脚本删除功能
     *
     * @param scriptId 脚本ID
     */
    void deleteCalculationScript(Long scriptId);

    /**
     * 规则脚本校验功能
     *
     * @param scriptModel
     * @return
     */
    ApiResponse<Object> verifyCalculationScript(ScriptModel scriptModel);

    /**
     * 规则脚本预览功能
     *
     * @param scriptModel
     * @return
     */
    RulesCompare previewCalculationScript(ScriptModel scriptModel) throws DataSourceException;

    /**
     * 校验规则脚本入参
     *
     * @param scriptModel
     */
    void checkDeploymentScriptModel(ScriptModel scriptModel);

    /**
     * 1、校验规则脚本入参
     * 2、当传入的规则内容为空时，自动填充之前部署的规则
     *
     * @param scriptModel
     */
    void checkDeploymentScriptModelForSyncDeployment(ScriptModel scriptModel);

    /**
     * 更新规则状态
     *
     * @param calculationScript
     */
    int updateStatus(CalculationScript calculationScript);

    /**
     * 规则脚本回退功能
     *
     * @param calculationScript 计算脚本
     * @return
     */
    CalculationScript rollbackCalculationScript(CalculationScript calculationScript);

    /**
     * 下载模板文件
     */
    void downloadCalculationScriptTemplate(HttpServletResponse response);

    /**
     * 规则脚本导出功能
     *
     * @param calculationScripts 多个计算脚本
     */
    void exportCalculationScripts(CalculationScripts calculationScripts);

    /**
     * 规则脚本导入功能
     *
     * @param file
     */
    void importCalculationScripts(MultipartFile file);

    /**
     * 查询某个作业空间下，所有的规则列表
     *
     * @param workspaceId
     * @return
     */
    List<CalculationScriptModel> findALlScriptsByWorkspaceId(long workspaceId);

    /**
     * 查询某个作业空间下，所有的规则列表及相关跨模型列表
     *
     * @param workspaceId
     * @return
     */
    List<CalculationScriptRelativeModels> findALlScriptsWithRelativeModelsByWorkspaceId(long workspaceId);

    /**
     * 按照之前的部署时间来排序查询多个规则列表
     *
     * @param calculationScripts
     */
    List<CalculationScriptModel> getSortedCalculationScriptsByList(CalculationScripts calculationScripts);

    /**
     * 执行规则脚本部署
     *
     * @param scriptModel
     * @return 脚本部署后细分的规则数量
     */
    int executeDeployment(ScriptModel scriptModel, QuartzTaskInfo quartzTaskInfo);

    /**
     * 更新规则状态
     *
     * @param calculationScript
     */
    int executeUpdateStatus(CalculationScript calculationScript);

    /**
     * 规则脚本状态重置功能
     *
     * @param calculationScript 计算脚本
     * @return
     */
    CalculationScript resetCalculationScript(CalculationScript calculationScript);

    ScriptModel initWorkspaceInfo(ScriptModel scriptModel);

    ScriptModel initWorkspaceInfo(long workspaceId, ScriptModel scriptModel);

    /**
     * public API使用，主要用来校验入参
     *
     * @param scriptModelVO
     * @return
     */
    ScriptModel initScriptModelForVerifyV2(ScriptModelVO scriptModelVO);

    /**
     * public API使用，主要用来校验入参
     *
     * @param scriptModelVO
     * @return
     */
    ScriptModel initScriptModelForDeployV2(ScriptModelVO scriptModelVO);

    /**
     * public API使用，主要用来自动保存规则脚本
     *
     * @param scriptModelVO
     */
    CalculationScript saveCalculationScriptV2(ScriptModelVO scriptModelVO, ScriptFolder scriptFolder);

    /**
     * public API使用，主要用来重命名规则脚本
     *
     * @param calculationScript
     */
    ApiResponse<Object> renameCalculationScriptV2(CalculationScript calculationScript);

    /**
     * 规则脚本内容更新
     *
     * @param scriptModelVO
     */
    CalculationScript updateCalculationScriptV2(ScriptModelVO scriptModelVO);

    ScriptModel convertQuartzTaskToScriptModel(QuartzTaskInfo taskInfo);

    void saveCalculationRules(CalculationScript calculationScript, DeployEffect deployEffect,
                              DslDeployContext context, DeployCellParam param) throws DataSourceException;

    void deleteTemporalRules(CalculationScript calculationScript, QuartzTaskInfo quartzTaskInfo);

    void migrateDataToRuleTable(CalculationScript calculationScript, Integer workFlowId);

    List<String> queryWorkflowInfo(Long workspaceId, Integer workFlowId);

    int deleteWorkflowInfo(Long workspaceId, Integer workFlowId);

    void migrateDataToCalculationTable(List<String> temporalTableName, Integer workFlowId);

    void migrateDataToGlobalTable(Integer workFlowId);

    void saveCalculationGraphToGesForTemporal(CalculationScript calculationScript, DslDeployContext context);
}
