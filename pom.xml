<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.huawei.it.finance.opt</groupId>
    <artifactId>opt-fsp-pub-service-parent</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0.0-SNAPSHOT</version>

    <modules>
        <module>opt-fsp-pub-dimmodel</module>
        <module>opt-fsp-pub-datasource</module>
        <module>opt-fsp-pub-calculation</module>
        <module>opt-fsp-pub-calculation-support</module>
        <module>opt-fsp-pub-service-boot</module>
        <module>opt-fsp-pub-form</module>
        <module>opt-fsp-pub-analysis</module>
        <module>opt-fsp-pub-tech-util</module>
        <module>opt-fsp-pub-biz-protocol</module>
        <module>opt-fsp-pub-personal-center</module>
        <module>opt-fsp-pub-workspace</module>
        <module>opt-fsp-pub-appisland</module>
        <module>opt-fsp-pub-knowledge</module>
        <module>opt-fsp-pub-audit</module>
        <module>opt-fsp-pub-alter-variable</module>
        <module>opt-fsp-pub-task</module>
        <module>opt-fsp-pub-ai-bot</module>
        <module>opt-fsp-pub-dataintegrator</module>
        <module>opt-fsp-pub-sync-invoke</module>
        <module>opt-fsp-pub-workflow</module>
    </modules>

    <properties>
        <!-- compile settings -->
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>

        <!-- plugin version properties -->
        <maven.surefire.plugin.version>2.22.2</maven.surefire.plugin.version>
        <dt4j.coverage.maven.plugin>2.0.3</dt4j.coverage.maven.plugin>
        <build.plugins.plugin.version>3.9.0</build.plugins.plugin.version>

        <!-- version properties -->
        <logback.version>1.2.13</logback.version>
        <commons-logging.version>1.3.0</commons-logging.version>
        <slf4j.version>1.7.36</slf4j.version>
        <commons-codec.version>1.17.1</commons-codec.version>
        <commons-io.version>2.16.1</commons-io.version>
        <spring-framework.version>5.3.39-h3</spring-framework.version>
        <spring-framework.security.version>5.8.16</spring-framework.security.version>
        <spring.boot.version>2.7.18</spring.boot.version>
        <reactor-core.version>3.5.4</reactor-core.version>
        <netty-tcnavite.version>2.0.69.Final</netty-tcnavite.version>
        <embedded-postgres.version>1.2.8</embedded-postgres.version>
        <rxjava.version>1.3.8</rxjava.version>
        <okhttp.version>4.12.0</okhttp.version>
        <okio.version>3.6.0</okio.version>
        <org.jetbrains.annotations.version>22.0.0</org.jetbrains.annotations.version>
        <org.jetbrains.kotlin.version>1.9.10</org.jetbrains.kotlin.version>
        <org.json.version>20231013</org.json.version>
        <json-sanitizer.version>1.2.3</json-sanitizer.version>
        <javax.inject.version>1</javax.inject.version>
        <zstd-jni.version>1.5.6-4</zstd-jni.version>
        <woodstox-core.version>6.6.0</woodstox-core.version>
        <archunit.version>1.1.0</archunit.version>
        <jieba-analysis.version>1.0.2</jieba-analysis.version>
        <easyexcel.version>3.3.4</easyexcel.version>
        <transmittable.thread.local.version>2.13.2</transmittable.thread.local.version>
        <arthas.version>4.0.5</arthas.version>
        <aws-java-sdk-s3.version>1.12.661</aws-java-sdk-s3.version>
        <ion-java.version>1.11.9</ion-java.version>
        <hutool.version>5.8.25</hutool.version>
        <jakarta.xml.bind.version>4.0.0</jakarta.xml.bind.version>
        <jakarta.activation.version>1.2.2</jakarta.activation.version>
        <dynamic-datasource.version>4.2.0</dynamic-datasource.version>
        <mybatis.version>3.5.9</mybatis.version>
        <mybatis-spring.version>2.1.2</mybatis-spring.version>
        <mybatis-plus-boot-starter.version>3.5.5</mybatis-plus-boot-starter.version>
        <guava.version>33.0.0-jre</guava.version>
        <failureaccess.version>1.0.1</failureaccess.version>
        <error_prone_annotations.version>2.18.0</error_prone_annotations.version>
        <aviator.version>5.3.2</aviator.version>
        <gson.version>2.9.0</gson.version>
        <jsr305.version>3.0.2</jsr305.version>
        <guice.version>5.1.0</guice.version>
        <netflix.ribbon.version>2.7.18</netflix.ribbon.version>
        <netflix.archaius.version>0.7.7</netflix.archaius.version>
        <netflix.servo.core.version>0.13.2</netflix.servo.core.version>
        <stax2-api.version>4.2.2</stax2-api.version>
        <snakeyaml.version>2.0</snakeyaml.version>
        <assertj.core.version>3.24.2</assertj.core.version>
        <aspectj.version>1.9.20</aspectj.version>
        <cxf.version>3.5.10</cxf.version>
        <commons-lang3.version>3.14.0</commons-lang3.version>
        <tomcat.coyote.version>9.0.104</tomcat.coyote.version>
        <appache.common.compress.version>1.26.1</appache.common.compress.version>
        <apache-batik.version>1.17</apache-batik.version>
        <quartz.scheduler.version>2.4.0-rc2</quartz.scheduler.version>
        <log4j2.version>2.18.0</log4j2.version>
        <lucene.version>8.11.3</lucene.version>
        <poi.version>5.4.0</poi.version>
        <xmlbeans.version>5.1.1</xmlbeans.version>
        <liquibase.version>4.19.1</liquibase.version>
        <antlr4.version>4.9.2</antlr4.version>
        <lombok.mapstruct.binding.version>0.2.0</lombok.mapstruct.binding.version>
        <lombok.version>1.18.26</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <mockito.core.version>4.5.1</mockito.core.version>
        <mybatis-spring-boot-starter.version>2.3.2</mybatis-spring-boot-starter.version>
        <asm.version>9.4</asm.version>
        <owasp-esapi-java.version>2.5.2.0</owasp-esapi-java.version>
        <owasp.antisamy.version>1.7.5</owasp.antisamy.version>
        <postgresql.version>42.7.2</postgresql.version>
        <httpcomponents.version>4.4.16</httpcomponents.version>
        <httpcomponents.httpclient.version>4.5.14</httpcomponents.httpclient.version>
        <httpcomponents.httpclient5.version>5.2.1</httpcomponents.httpclient5.version>
        <commons-pool2.version>2.11.1</commons-pool2.version>
        <commons-configuration.version>1.10</commons-configuration.version>
        <commons-configuration2.version>2.8.0</commons-configuration2.version>
        <checker.qual.version>3.41.0</checker.qual.version>
        <cache-api.version>1.1.1</cache-api.version>
        <byte.buddy.version>1.14.12</byte.buddy.version>
        <ibm.icu.version>75.1</ibm.icu.version>
        <xstream.version>1.4.21</xstream.version>
        <jackson.version>2.16.2</jackson.version>
        <javax.json.version>1.1.3</javax.json.version>
        <javax.json-api.version>1.1.4</javax.json-api.version>
        <jaxb.version>2.3.9</jaxb.version>
        <jaxb.api.version>2.3.1</jaxb.api.version>
        <dom4j.version>2.1.4</dom4j.version>
        <fastinfoset.version>1.2.16</fastinfoset.version>
        <istack-commons-runtime.version>2.16</istack-commons-runtime.version>
        <!--jalor补丁版本-->
        <jalor.version>7.0.2.6.RELEASE</jalor.version>
        <jalor.sec.sp1.version>7.0.2.6-SP1.RELEASE</jalor.sec.sp1.version>
        <jalor.sec.sp2.version>7.0.2.6-SP2.RELEASE</jalor.sec.sp2.version>
        <jalor.sec.sp3.version>7.0.2.6-SP3.RELEASE</jalor.sec.sp3.version>
        <jalor.sec.-sp4.version>7.0.2.6-SP4.RELEASE</jalor.sec.-sp4.version>
        <jalor.sec.sp4.version>7.0.2.6.SP4.RELEASE</jalor.sec.sp4.version>
        <jalor.sec.sp5.version>7.0.2.6-SP5.RELEASE</jalor.sec.sp5.version>
        <jalor.sec.sp6.version>7.0.2.6-SP6.RELEASE</jalor.sec.sp6.version>
        <jalor.sec.sp9.version>7.0.2.6-SP9.RELEASE</jalor.sec.sp9.version>
        <jalor.sec.sp14.version>7.0.2.6-SP14.RELEASE</jalor.sec.sp14.version>
        <jalor.sec.sp21.version>7.0.2.6-SP21.RELEASE</jalor.sec.sp21.version>

        <jalor.springmvc.soa.version>6.6.8.0.RELEASE</jalor.springmvc.soa.version>
        <esdk.obs.java.version>3.24.3</esdk.obs.java.version>
        <netty.version>4.1.118.Final</netty.version>
        <reactor.netty.version>1.0.39</reactor.netty.version>
        <jalor-saas-consumer-mqs.version>7.0.2.6.RELEASE</jalor-saas-consumer-mqs.version>
        <mqs-sdk.version>3.2.1.3</mqs-sdk.version>
        <apm-toolkit-trace.version>8.7.1</apm-toolkit-trace.version>
        <aegis-dependencies.version>1.2.0.309</aegis-dependencies.version>
        <edm3.client.sdk.version>3.2.3.6</edm3.client.sdk.version>
        <iam.client.sdk.version>4.2.0</iam.client.sdk.version>
        <iam.sdk.version>6.0.0</iam.sdk.version>
        <com.huawei.us.version>1.0.76.5</com.huawei.us.version>
        <com.huawei.dt.version>2.0.3</com.huawei.dt.version>
        <com.huawei.huaweiexception.version>6.1.1</com.huawei.huaweiexception.version>
        <com.huawei.useronline.version>4.1.5.8</com.huawei.useronline.version>
        <org.xmlunit.core.version>2.9.1</org.xmlunit.core.version>
        <org.junit.jupiter.version>5.8.2</org.junit.jupiter.version>
        <org.junit.platform.version>1.8.2</org.junit.platform.version>
        <jsonassert.version>1.5.1</jsonassert.version>
        <apiguardian.version>1.1.2</apiguardian.version>
        <json-path.version>2.7.0</json-path.version>
        <bcprov.jdk18on.version>1.78.1</bcprov.jdk18on.version>
        <hibernate-validator.version>6.2.4.Final</hibernate-validator.version>
        <jboss-logging.version>3.4.3.Final</jboss-logging.version>
        <jedis.version>3.10.0</jedis.version>
        <jettison.version>1.5.4</jettison.version>
        <joda-time.version>2.13.1</joda-time.version>
        <jsqlparser.version>4.9</jsqlparser.version>
        <dws.version>8.5.0.1-200</dws.version>
        <opencsv.version>5.9</opencsv.version>
        <freemarker.version>2.3.32</freemarker.version>
        <opengauss.version>V500R002C00</opengauss.version>
        <redisson.version>3.23.1</redisson.version>
        <objenesis.version>3.2</objenesis.version>
        <junit-platform-surefire-provider.version>1.3.2</junit-platform-surefire-provider.version>
        <temporal.version>1.26.1</temporal.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huawei.meta.tpdb</groupId>
            <artifactId>opengaussjdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huaban</groupId>
            <artifactId>jieba-analysis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-starter-biz</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>bcprov-jdk18on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
                <exclusion>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!-- mapstruct与lombok配套使用所需依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jul-to-slf4j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.huawei.dt</groupId>
            <artifactId>dt4j-starter-boot</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-dataformat-yaml</artifactId>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud.dws</groupId>
            <artifactId>huaweicloud-dws-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huawei.us</groupId>
            <artifactId>us-core</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <groupId>com.huawei.us</groupId>
                    <artifactId>IntegrityCheck</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>bcprov-jdk18on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.us</groupId>
            <artifactId>us-file</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud</groupId>
            <artifactId>esdk-obs-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>io.projectreactor.netty</groupId>
            <artifactId>reactor-netty-http</artifactId>
        </dependency>

        <dependency>
            <groupId>io.projectreactor.netty</groupId>
            <artifactId>reactor-netty-core</artifactId>
        </dependency>

        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>arms-mesh-app-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.arthas</groupId>
            <artifactId>arthas-spring-boot-starter</artifactId>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-queryparser</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-analyzers-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-analyzers-smartcn</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huawei.his.framework</groupId>
            <artifactId>jalor-webservice-support</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml-schemas</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>ehcache</artifactId>
                    <groupId>org.ehcache</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-parameter-names</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-surefire-provider</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>junit-platform-launcher</artifactId>
                    <groupId>org.junit.platform</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.junit.platform</groupId>
                <artifactId>junit-platform-surefire-provider</artifactId>
                <version>${junit-platform-surefire-provider.version}</version>
            </dependency>
            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>${objenesis.version}</version>
            </dependency>
            <!--引入redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <!-- commons-logging -->
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>${commons-logging.version}</version>
            </dependency>

            <!-- commons-codec -->
            <dependency>
                <artifactId>commons-codec</artifactId>
                <groupId>commons-codec</groupId>
                <version>${commons-codec.version}</version>
            </dependency>

            <!-- commons-io -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <!-- slf4j and relative bridges -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jul-to-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>

            <!-- javax inject -->
            <dependency>
                <groupId>javax.inject</groupId>
                <artifactId>javax.inject</artifactId>
                <version>${javax.inject.version}</version>
            </dependency>

            <!-- jaxb and relative components -->
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-core</artifactId>
                <version>${jaxb.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${jaxb.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>txw2</artifactId>
                <version>${jaxb.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>javax.json</artifactId>
                <version>${javax.json.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.json</groupId>
                <artifactId>javax.json-api</artifactId>
                <version>${javax.json-api.version}</version>
            </dependency>

            <!-- dom4j -->
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>

            <!-- fastinfoset -->
            <dependency>
                <groupId>com.sun.xml.fastinfoset</groupId>
                <artifactId>FastInfoset</artifactId>
                <version>${fastinfoset.version}</version>
            </dependency>

            <!-- javax cache -->
            <dependency>
                <groupId>javax.cache</groupId>
                <artifactId>cache-api</artifactId>
                <version>${cache-api.version}</version>
            </dependency>

            <!-- jackson and relative components -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-cbor</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jdk8</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.jaxrs</groupId>
                <artifactId>jackson-jaxrs-base</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.jaxrs</groupId>
                <artifactId>jackson-jaxrs-json-provider</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-parameter-names</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>io.temporal</groupId>
                <artifactId>temporal-sdk</artifactId>
                <version>${temporal.version}</version>
            </dependency>
            <dependency>
                <groupId>io.temporal</groupId>
                <artifactId>temporal-spring-boot-starter</artifactId>
                <version>${temporal.version}</version>
            </dependency>

            <!-- springframework bom -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- springframework security -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring-framework.security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-crypto</artifactId>
                <version>${spring-framework.security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-web</artifactId>
                <version>${spring-framework.security.version}</version>
            </dependency>

            <!-- spring boot -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-tomcat</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-json</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jdbc</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-logging</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-to-slf4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-test-autoconfigure</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-test</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webflux</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-websocket</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-messaging</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-reactor-netty</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-quartz</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <!-- apache cxf bom -->
            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-bom</artifactId>
                <version>${cxf.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- apache commons-lang3 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <!-- apache tomcat -->
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-annotations-api</artifactId>
                <version>${tomcat.coyote.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-coyote</artifactId>
                <version>${tomcat.coyote.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.coyote.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.coyote.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.coyote.version}</version>
            </dependency>

            <!-- apache xmlbeans -->
            <dependency>
                <groupId>org.apache.xmlbeans</groupId>
                <artifactId>xmlbeans</artifactId>
                <version>${xmlbeans.version}</version>
            </dependency>

            <!-- apache commons-compress -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${appache.common.compress.version}</version>
            </dependency>

            <!-- apache xml graphics batik-css -->
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-css</artifactId>
                <version>${apache-batik.version}</version>
            </dependency>

            <!-- apache lucene -->
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-core</artifactId>
                <version>${lucene.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-queryparser</artifactId>
                <version>${lucene.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-analyzers-common</artifactId>
                <version>${lucene.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-analyzers-smartcn</artifactId>
                <version>${lucene.version}</version>
            </dependency>

            <!-- poi -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-lite</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>${log4j2.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- quartz scheduler -->
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.scheduler.version}</version>
            </dependency>

            <!-- liquibase -->
            <dependency>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-core</artifactId>
                <version>${liquibase.version}</version>
            </dependency>

            <!-- assertj -->
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.core.version}</version>
            </dependency>

            <!-- aspectj -->
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjrt</artifactId>
                <version>${aspectj.version}</version>
            </dependency>

            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectj.version}</version>
            </dependency>

            <!-- antlr4 -->
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4</artifactId>
                <version>${antlr4.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.antlr</groupId>
                        <artifactId>ST4</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.antlr</groupId>
                        <artifactId>antlr-runtime</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.abego.treelayout</groupId>
                        <artifactId>org.abego.treelayout.core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-runtime</artifactId>
                <version>${antlr4.version}</version>
            </dependency>

            <!-- lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok.mapstruct.binding.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <!-- mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <!-- mockito -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito.core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>${mockito.core.version}</version>
            </dependency>

            <!-- mybatis-spring-boot-starter -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot-starter.version}</version>
            </dependency>

            <!-- asm -->
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>

            <!-- owasp components -->
            <dependency>
                <groupId>org.owasp.antisamy</groupId>
                <artifactId>antisamy</artifactId>
                <version>${owasp.antisamy.version}</version>
            </dependency>

            <dependency>
                <groupId>org.owasp.esapi</groupId>
                <artifactId>esapi</artifactId>
                <version>${owasp-esapi-java.version}</version>
            </dependency>

            <!-- postgresql -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>

            <!-- apache http components -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcomponents.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore-nio</artifactId>
                <version>${httpcomponents.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpcomponents.httpclient.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpcomponents.httpclient5.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents.core5</groupId>
                <artifactId>httpcore5</artifactId>
                <version>${httpcomponents.httpclient5.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents.core5</groupId>
                <artifactId>httpcore5-h2</artifactId>
                <version>${httpcomponents.httpclient5.version}</version>
            </dependency>

            <!-- apache commons-pool2 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>

            <!-- apache commons-configuration -->
            <dependency>
                <groupId>commons-configuration</groupId>
                <artifactId>commons-configuration</artifactId>
                <version>${commons-configuration.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-configuration2</artifactId>
                <version>${commons-configuration2.version}</version>
            </dependency>

            <!-- checker qual -->
            <dependency>
                <groupId>org.checkerframework</groupId>
                <artifactId>checker-qual</artifactId>
                <version>${checker.qual.version}</version>
            </dependency>

            <!-- byte buddy -->
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>${byte.buddy.version}</version>
            </dependency>

            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${byte.buddy.version}</version>
            </dependency>

            <!-- icu4j -->
            <dependency>
                <groupId>com.ibm.icu</groupId>
                <artifactId>icu4j</artifactId>
                <version>${ibm.icu.version}</version>
            </dependency>

            <!-- xstream -->
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>

            <!-- netty bom and relative components -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <type>pom</type>
                <version>${netty.version}</version>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-tcnative</artifactId>
                <version>${netty-tcnavite.version}</version>
            </dependency>

            <dependency>
                <groupId>io.projectreactor.netty</groupId>
                <artifactId>reactor-netty-http</artifactId>
                <version>${reactor.netty.version}</version>
            </dependency>

            <dependency>
                <groupId>io.projectreactor.netty</groupId>
                <artifactId>reactor-netty-core</artifactId>
                <version>${reactor.netty.version}</version>
            </dependency>

            <dependency>
                <groupId>io.projectreactor</groupId>
                <artifactId>reactor-core</artifactId>
                <version>${reactor-core.version}</version>
            </dependency>

            <!-- embedded-postgres -->
            <dependency>
                <groupId>io.zonky.test</groupId>
                <artifactId>embedded-postgres</artifactId>
                <version>${embedded-postgres.version}</version>
            </dependency>

            <!-- rxjava -->
            <dependency>
                <groupId>io.reactivex</groupId>
                <artifactId>rxjava</artifactId>
                <version>${rxjava.version}</version>
            </dependency>

            <!-- baomidou dynamic-datasource -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>

            <!-- baomidou mybatis-plus starter -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>

            <!-- google guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>failureaccess</artifactId>
                <version>${failureaccess.version}</version>
            </dependency>

            <!-- google errorprone -->
            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>${error_prone_annotations.version}</version>
            </dependency>

            <!-- google aviator -->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>

            <!-- google gson -->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <!-- google jsr305 -->
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>${jsr305.version}</version>
            </dependency>

            <!-- google guice -->
            <dependency>
                <groupId>com.google.inject</groupId>
                <artifactId>guice</artifactId>
                <version>${guice.version}</version>
            </dependency>

            <!-- netflix components -->
            <dependency>
                <groupId>com.netflix.ribbon</groupId>
                <artifactId>ribbon-core</artifactId>
                <version>${netflix.ribbon.version}</version>
            </dependency>

            <dependency>
                <groupId>com.netflix.ribbon</groupId>
                <artifactId>ribbon-loadbalancer</artifactId>
                <version>${netflix.ribbon.version}</version>
            </dependency>

            <dependency>
                <groupId>com.netflix.archaius</groupId>
                <artifactId>archaius-core</artifactId>
                <version>${netflix.archaius.version}</version>
            </dependency>

            <dependency>
                <groupId>com.netflix.servo</groupId>
                <artifactId>servo-core</artifactId>
                <version>${netflix.servo.core.version}</version>
            </dependency>

            <!-- hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- jakarta xml bind -->
            <dependency>
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${jakarta.xml.bind.version}</version>
            </dependency>

            <!-- jakarta activation -->
            <dependency>
                <groupId>com.sun.activation</groupId>
                <artifactId>jakarta.activation</artifactId>
                <version>${jakarta.activation.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.activation</groupId>
                <artifactId>jakarta.activation-api</artifactId>
                <version>${jakarta.activation.version}</version>
            </dependency>

            <!-- okhttp3 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <!-- okio -->
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okio-jvm</artifactId>
                <version>${okhttp.version}</version>
            </dependency>



            <!-- json-sanitizer -->
            <dependency>
                <groupId>com.mikesamuel</groupId>
                <artifactId>json-sanitizer</artifactId>
                <version>${json-sanitizer.version}</version>
            </dependency>

            <!-- jetbrains annotations -->
            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>${org.jetbrains.annotations.version}</version>
            </dependency>

            <!-- jetbrains kotlin -->
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>${org.jetbrains.kotlin.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jdk7</artifactId>
                <version>${org.jetbrains.kotlin.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jdk8</artifactId>
                <version>${org.jetbrains.kotlin.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-common</artifactId>
                <version>${org.jetbrains.kotlin.version}</version>
            </dependency>

            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${org.json.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.istack</groupId>
                <artifactId>istack-commons-runtime</artifactId>
                <version>${istack-commons-runtime.version}</version>
            </dependency>

            <!-- zstd-jni -->
            <dependency>
                <groupId>com.github.luben</groupId>
                <artifactId>zstd-jni</artifactId>
                <version>${zstd-jni.version}</version>
            </dependency>

            <!-- woodstox -->
            <dependency>
                <groupId>com.fasterxml.woodstox</groupId>
                <artifactId>woodstox-core</artifactId>
                <version>${woodstox-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.woodstox</groupId>
                <artifactId>stax2-api</artifactId>
                <version>${stax2-api.version}</version>
            </dependency>

            <!-- snakeyaml -->
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>

            <!-- junit5 -->
            <dependency>
                <groupId>com.tngtech.archunit</groupId>
                <artifactId>archunit-junit5</artifactId>
                <version>${archunit.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tngtech.archunit</groupId>
                <artifactId>archunit-junit5-api</artifactId>
                <version>${archunit.version}</version>
            </dependency>

            <!-- jieba-analysis -->
            <dependency>
                <groupId>com.huaban</groupId>
                <artifactId>jieba-analysis</artifactId>
                <version>${jieba-analysis.version}</version>
            </dependency>

            <!-- easyexcel -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!-- transmittable -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable.thread.local.version}</version>
            </dependency>

            <!-- arthas -->
            <dependency>
                <groupId>com.taobao.arthas</groupId>
                <artifactId>arthas-spring-boot-starter</artifactId>
                <version>${arthas.version}</version>
            </dependency>

            <!-- amazon aws-java-sdk-s3 -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazon.ion</groupId>
                <artifactId>ion-java</artifactId>
                <version>${ion-java.version}</version>
            </dependency>

            <!-- dws jdbc -->
            <dependency>
                <groupId>com.huaweicloud.dws</groupId>
                <artifactId>huaweicloud-dws-jdbc</artifactId>
                <version>${dws.version}</version>
            </dependency>

            <!-- opengauss jdbc -->
            <dependency>
                <groupId>com.huawei.meta.tpdb</groupId>
                <artifactId>opengaussjdbc</artifactId>
                <version>${opengauss.version}</version>
            </dependency>

            <!-- his framework and relative security plugins -->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-starter-core</artifactId>
                <version>${jalor.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-starter-biz</artifactId>
                <version>${jalor.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-log4j2</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-csv</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-jxpath</groupId>
                        <artifactId>commons-jxpath</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-sgov-huawei</artifactId>
                <version>${jalor.version}</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-validator</artifactId>
                <version>${jalor.sec.sp2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-rpc</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-rpc-common</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-rpc-vega</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-rpc-stub</artifactId>
                <version>${jalor.sec.sp2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-audit</artifactId>
                <version>${jalor.version}</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-audit-aop</artifactId>
                <version>${jalor.sec.sp4.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-web-resource</artifactId>
                <version>${jalor.version}</version>
            </dependency>

            <!-- 基于Jalor7.0.2.6.RELEASE的安全补丁升级，参考链接 https://3ms.huawei.com/hi/group/2692001/wiki_7745301.html 开始-->
            <!-- 如果后续升级新版本的Release包，需要再清除相关安全补丁配置 -->
            <!-- 安全补丁【30】:用户自定义域名配置-->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-web</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>

            <!-- 【重要补丁】【58】 递归Jalor日志类属性存在栈溢出问题-->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-api</artifactId>
                <version>${jalor.sec.sp6.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-core</artifactId>
                <version>${jalor.sec.sp21.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-model</artifactId>
                <version>${jalor.sec.sp9.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-excel-impl</artifactId>
                <version>${jalor.sec.sp3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-login</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-loadbalancer</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>
            <!-- 安全补丁【7】【BUG修复】日志打印明文秘钥-->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-config-configcenter</artifactId>
                <version>${jalor.sec.sp2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-config</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-inspection</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-authorization</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>
            <!-- 安全补丁【6】【BUG修复】不安全的随机数-->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-helper</artifactId>
                <version>${jalor.sec.sp14.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-usf-client</artifactId>
                <version>${jalor.sec.-sp4.version}</version>
            </dependency>
            <!-- 安全补丁【2】新特性：jalor-https升级适配bcprov-jdk18on 1.75版本-->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-https</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>
            <!-- 安全补丁【1】内部接口清单配置兼容/services路径-->
            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-permission</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-web-support</artifactId>
                <version>${jalor.sec.sp5.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-boot</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>arms-mesh-app-starter</artifactId>
                <version>${jalor.sec.sp1.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-starter-audit</artifactId>
                <version>${jalor.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-webservice-support</artifactId>
                <version>${jalor.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.framework</groupId>
                <artifactId>jalor-saas-consumer-mqs</artifactId>
                <version>${jalor.version}</version>
            </dependency>

            <!-- mqs -->
            <dependency>
                <groupId>com.huawei.his.mqs</groupId>
                <artifactId>mqs-sdk</artifactId>
                <version>${mqs-sdk.version}</version>
            </dependency>

            <!-- apm -->
            <dependency>
                <groupId>com.huawei.apm</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${apm-toolkit-trace.version}</version>
            </dependency>

            <!-- aegis dependencies -->
            <dependency>
                <groupId>com.huawei.wisecloud.secure</groupId>
                <artifactId>aegis-dependencies</artifactId>
                <version>${aegis-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions>
                    <exclusion>
                        <groupId>com.huawei.wisecloud.secure</groupId>
                        <artifactId>NetPrevention</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>bcprov-jdk18on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- edm3 client -->
            <dependency>
                <groupId>com.huawei.it.edm</groupId>
                <artifactId>edm3-client-sdk</artifactId>
                <version>${edm3.client.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-beans</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>bcprov-jdk18on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- iam sdk -->
            <dependency>
                <groupId>com.huawei.iam</groupId>
                <artifactId>iam-sdk</artifactId>
                <version>${iam.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.huawei.his.foundation</groupId>
                <artifactId>iam-client-sdk</artifactId>
                <version>${iam.client.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>bcprov-jdk18on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>org.json</artifactId>
                        <groupId>json</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- huawei us bom -->
            <dependency>
                <groupId>com.huawei.us</groupId>
                <artifactId>us-core</artifactId>
                <version>${com.huawei.us.version}</version>
                <type>pom</type>
                <exclusions>
                    <exclusion>
                        <groupId>com.huawei.us</groupId>
                        <artifactId>IntegrityCheck</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>net.sourceforge.htmlunit</groupId>
                        <artifactId>neko-htmlunit</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.huawei.us</groupId>
                <artifactId>us-file</artifactId>
                <version>${com.huawei.us.version}</version>
            </dependency>

            <!-- jalor5 useronline -->
            <dependency>
                <groupId>com.huawei.it.jalor5</groupId>
                <artifactId>jalor5.useronline.api</artifactId>
                <version>${com.huawei.useronline.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- huawei exception -->
            <dependency>
                <groupId>com.huawei.it.hae3.exception</groupId>
                <artifactId>huaweiexception</artifactId>
                <version>${com.huawei.huaweiexception.version}</version>
            </dependency>

            <!-- huaweicloud esdk -->
            <dependency>
                <groupId>com.huaweicloud</groupId>
                <artifactId>esdk-obs-java</artifactId>
                <version>${esdk.obs.java.version}</version>
            </dependency>

            <!-- bouncycastle -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bcprov.jdk18on.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>${bcprov.jdk18on.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcutil-jdk18on</artifactId>
                <version>${bcprov.jdk18on.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging</artifactId>
                <version>${jboss-logging.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.jettison</groupId>
                <artifactId>jettison</artifactId>
                <version>${jettison.version}</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>${jsqlparser.version}</version>
            </dependency>

            <dependency>
                <groupId>com.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>${opencsv.version}</version>
            </dependency>

            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>

            <!-- dt4j and junit relative jars , remember to keep them 'test' scope -->
            <dependency>
                <groupId>com.huawei.dt</groupId>
                <artifactId>dt4j-starter-boot</artifactId>
                <version>${com.huawei.dt.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.xmlunit</groupId>
                <artifactId>xmlunit-core</artifactId>
                <version>${org.xmlunit.core.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${org.junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${org.junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-params</artifactId>
                <version>${org.junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${org.junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.junit.platform</groupId>
                <artifactId>junit-platform-engine</artifactId>
                <version>${org.junit.platform.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.skyscreamer</groupId>
                <artifactId>jsonassert</artifactId>
                <version>${jsonassert.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.apiguardian</groupId>
                <artifactId>apiguardian-api</artifactId>
                <version>${apiguardian.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>${json-path.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path-assert</artifactId>
                <version>${json-path.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.huawei.dt</groupId>
                    <artifactId>dt4j-coverage-maven-plugin</artifactId>
                    <version>${dt4j.coverage.maven.plugin}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <inherited>false</inherited>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven.surefire.plugin.version}</version>
                <configuration>
                    <properties>
                        <includeTags>junit5</includeTags>
                    </properties>
                    <!-- 设置并发执行的粒度行-->
                    <parallel>classes</parallel>
                    <!-- 设置并行运行线程数-->
                    <threadCount>4</threadCount>
                    <!-- 开启多个JVM进程运行单元测试,配置1.5C -->
                    <forkCount>1</forkCount>
                    <!-- reuseForks表示一个测试进程执行完了之后是杀掉还是重用来继续执行后续的测试 -->
                    <reuseForks>true</reuseForks>
                    <skipTests>false</skipTests>
                    <trimStackTrace>false</trimStackTrace>
                    <!-- 暂无需执行的测试类 -->
                    <excludes>
                        **/GesRestTemplateTest.java,**/GesBusinessApiServiceTest.java,**/CypherQueryServiceTest.java,**/EdgeServiceTest.java,**/GesKhopServiceTest.java,**/VertexServiceTest.java,
                        **/KgEntityDomainServiceTest.java,**/KgRelationDomainServiceTest.java,**/KgEntityAttributeDomainServiceTest.java,**/KgRelationAttributeDomainServiceTest.java,
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.huawei.dt</groupId>
                <artifactId>dt4j-coverage-maven-plugin</artifactId>
                <configuration>
                    <reportName>OptFspPubTestReport</reportName>
                    <reportVersion>1.0.0</reportVersion>
                    <reportUser>UTUser</reportUser>
                    <openOnFinish>true</openOnFinish>
                    <!-- 覆盖率排除的包或类路径 -->
                    <excludeStats>
                        <!-- 分析模块 -->
                        <item>com/huawei/it/finance/opt/pub/analysis/*</item>
                        <!-- 维度模型模块 -->
                        <item>com/huawei/it/finance/pub/dimmodel/*</item>
                        <!-- 计算模块 -->
                        <item>com/huawei/it/finance/opt/base/application/service/impl/DynamicScriptApplicationService</item>
                        <item>com/huawei/it/finance/opt/base/application/service/impl/CalculationScriptApplicationService</item>
                        <item>com/huawei/it/finance/opt/base/domain/service/impl/DynamicScriptDomainService</item>
                        <item>com/huawei/it/finance/opt/base/domain/service/impl/DslDeployDomainService</item>
                        <item>com/huawei/it/finance/opt/base/domain/service/impl/CalculationScriptDomainService</item>
                        <!-- 表单模块 -->
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormRunDomainServiceImpl</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormConfigDomainServiceImpl</item>
                        <!-- 知识模块 -->
                        <item>com/huawei/it/finance/opt/pub/knowledge/*</item>
                        <!-- 替代变量模块 -->
                        <item>com/huawei/it/finance/opt/application/service/impl/AlterVariableAppService</item>
                        <item>com/huawei/it/finance/opt/application/service/impl/AlterVariableReferAppService</item>
                        <!-- 数据源模块 -->
                        <item>com/huawei/it/finance/opt/fsp/pub/application/service/impl/DataSourceApplicationService
                        </item>
                        <item>
                            com/huawei/it/finance/opt/fsp/pub/application/service/impl/QueryMetricValueApplicationService
                        </item>
                        <item>com/huawei/it/finance/opt/fsp/pub/application/service/impl/SourceTableApplicationService
                        </item>
                        <item>com/huawei/it/finance/opt/fsp/pub/application/service/impl/TableFieldsApplicationService
                        </item>
                        <item>
                            com/huawei/it/finance/opt/fsp/pub/application/service/impl/TableModelReferApplicationService
                        </item>
                        <item>com/huawei/it/finance/opt/fsp/pub/domain/service/impl/MetricMapperDomainService</item>
                        <item>com/huawei/it/finance/opt/fsp/pub/domain/service/impl/TableModelReferDomainService</item>
                        <!-- 表单模块 -->
                        <item>com/huawei/it/finance/opt/interfaces/FormConfigController</item>
                        <item>com/huawei/it/finance/opt/interfaces/FormDimModelController</item>
                        <item>com/huawei/it/finance/opt/interfaces/FormFolderController</item>
                        <item>com/huawei/it/finance/opt/interfaces/FormRunController</item>
                        <item>com/huawei/it/finance/opt/interfaces/FormCalculateController</item>
                        <item>com/huawei/it/finance/opt/application/service/impl/FormConfigAppService</item>
                        <item>com/huawei/it/finance/opt/application/service/impl/FormCalculateAppService</item>
                        <item>com/huawei/it/finance/opt/application/service/impl/FormRunAppService</item>
                        <item>com/huawei/it/finance/opt/application/service/impl/ModelAsyncCalculationJob</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormDynamicScriptDomainServiceImpl</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormGridsOptimizerFacade</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormGridsOptimizerSvr00</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormCellRuleOverrideServiceImpl</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormCellRuleSetService</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormValidParamServiceImpl</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormRunServiceImpl</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormBlockServiceImpl</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormDslPreviewService</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormMemoryServiceImpl</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormPovServiceImpl</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/InitPovAndBlockService</item>
                        <item>com/huawei/it/finance/opt/domain/service/impl/FormCalculateDomainService</item>
                        <!-- 作业岛 -->
                        <item>com/huawei/it/finance/pub/appisland/domain/service/impl/AppIslandDomainService</item>
                        <item>com/huawei/it/finance/pub/appisland/domain/service/impl/AppIslandPermissionService</item>
                        <item>com/huawei/it/finance/opt/application/service/impl/FormDimModelAppService</item>
                        <item>com/huawei/it/finance/opt/application/service/impl/FormFolderAppService</item>
                        <!-- AI-BOT模块 -->
                        <item>com/huawei/it/finance/opt/ai/bot/*</item>

                        <item>com/huawei/it/finance/opt/fsp/pub/application/service/impl/DataStorageApplicationService
                        </item>
                        <item>
                            com/huawei/it/finance/opt/fsp/pub/application/service/impl/DimFieldsMapperApplicationService
                        </item>
                        <item>
                            com/huawei/it/finance/opt/fsp/pub/application/service/impl/MetricMapperApplicationService
                        </item>
                        <item>
                            com/huawei/it/finance/opt/fsp/pub/application/service/impl/SourceTableAutoApplicationService
                        </item>

                        <item>com/huawei/it/finance/opt/fsp/pub/domain/service/impl/DimFieldMapperDomainService</item>
                        <item>com/huawei/it/finance/opt/fsp/pub/domain/service/impl/SourceTableAutoDomainService</item>
                        <item>com/huawei/it/finance/opt/fsp/pub/domain/service/impl/SourceTableDomainService</item>
                        <item>com/huawei/it/finance/opt/fsp/pub/domain/service/impl/TableFieldsDomainService</item>

                        <item>com/huawei/it/finance/opt/fsp/pub/interfaces/facade/impl/SourceTableAutoController</item>


                        <item>com/huawei/it/finance/opt/interfaces/AnalysisApiController</item>
                        <item>com/huawei/it/finance/opt/interfaces/CalculationScriptsController</item>
                        <item>com/huawei/it/finance/opt/interfaces/FormController</item>
                        <item>com/huawei/it/finance/opt/interfaces/TaskController</item>
                        <item>com/huawei/it/finance/opt/interfaces/WorkspaceBizController</item>

                        <item>com/huawei/it/finance/opt/fsp/pub/domain/service/impl/MqsModelEventDomainService</item>

                        <item>com/huawei/it/finance/opt/application/service/impl/AppenderAppServiceImpl</item>
                        <item>com/huawei/it/finance/opt/interfaces/PersonalCenterController</item>
                        <item>com/huawei/it/finance/pub/workspace/domain/service/impl/WorkspaceCopyEventDomainService
                        </item>
                        <item>com/huawei/it/finance/pub/workspace/domain/service/impl/WorkspaceDomainService</item>
                    </excludeStats>
                    <!--覆盖率包含的类路径-->
                    <includeStats>
                        <item>*/application/service/impl/*</item>
                        <item>*/domain/service/impl/*</item>
                        <item>*/interfaces/*</item>
                    </includeStats>
                </configuration>
                <executions>
                    <!-- 必须添加instrument任务，否则无法采集到class的指令执行数据 -->
                    <execution>
                        <id>instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>