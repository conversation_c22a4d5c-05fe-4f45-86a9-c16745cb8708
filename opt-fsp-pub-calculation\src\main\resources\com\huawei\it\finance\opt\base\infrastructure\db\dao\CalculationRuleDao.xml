<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.finance.opt.base.infrastructure.db.dao.CalculationRuleDao">

    <select id="findCalculationRules" resultType="com.huawei.it.finance.opt.base.entity.CalculationRule">
        select row_id as rowId,
            rule_id as ruleId,
            unit_id as unitId,
            group_id as groupId,
            business_version_id as businessVersionId,
            version_id as versionId,
            source_type as sourceType,
            script_id as scriptId,
            row_num as rowNumber,
            line_num as lineNumber,
            priority as priority,
            content as content,
            rich_content as richContent,
            unit_name as unitName,
            unit_code as unitCode,
            unit_short_name as unitShortName,
            multiple as multiple,
            '@@SELECT_COLUMNS_WORKSPACE@@',
            '@@SELECT_COLUMNS_AUDITS@@'
        from dm_fsppub_calculation_rule_t
        <trim prefix="where" prefixOverrides="and ">
            <if test='ruleId != null'>
                and rule_id = #{ruleId}
            </if>
            <if test='businessVersionId != null'>
                and business_version_id = #{businessVersionId}
            </if>
            <if test='versionId != null'>
                and version_id = #{versionId}
            </if>
            <if test='scriptId != null'>
                and script_id = #{scriptId}
            </if>
            <if test='unitId != null'>
                and unit_id = #{unitId}
            </if>
            <if test='groupId != null'>
                and group_id = #{groupId}
            </if>
            <if test='multiple != null'>
                and multiple = #{multiple}
            </if>
            '@@WHERE_CONDITIONS_WORKSPACE@@'
        </trim>
        order by priority
    </select>

    <select id="findHighestPriorityUnitCalculationRules" resultType="com.huawei.it.finance.opt.base.entity.CalculationRule">
        select
        rule_id as ruleId,
        unit_id as unitId,
        group_id as groupId,
        business_version_id as businessVersionId,
        version_id as versionId,
        source_type as sourceType,
        script_id as scriptId,
        row_num as rowNumber,
        line_num as lineNumber,
        priority as priority,
        content as content,
        rich_content as richContent,
        unit_name as unitName,
        unit_code as unitCode,
        multiple as multiple,
        '@@SELECT_COLUMNS_WORKSPACE@@'
        from dm_fsppub_calculation_rule_t
        <trim prefix="where" prefixOverrides="and ">
            <if test='ruleId != null'>
                and rule_id = #{ruleId}
            </if>
            <if test='businessVersionId != null'>
                and business_version_id = #{businessVersionId}
            </if>
            <if test='versionId != null'>
                and version_id = #{versionId}
            </if>
            <if test='scriptId != null'>
                and script_id = #{scriptId}
            </if>
            <if test='unitId != null'>
                and unit_id = #{unitId}
            </if>
            <if test='groupId != null'>
                and group_id = #{groupId}
            </if>
            <if test='multiple != null'>
                and multiple = #{multiple}
            </if>
            '@@WHERE_CONDITIONS_WORKSPACE@@'
        </trim>
        order by priority desc,last_update_date desc,creation_date desc
    </select>

    <select id="findCompactCalculationRules" resultType="com.huawei.it.finance.opt.base.entity.CalculationRule">
        select
        rule_id as ruleId,
        unit_id as unitId,
        group_id as groupId,
        business_version_id as businessVersionId,
        version_id as versionId,
        priority as priority,
        content as content,
        '@@SELECT_COLUMNS_WORKSPACE@@'
        from dm_fsppub_calculation_rule_t
        <trim prefix="where" prefixOverrides="and ">
            <if test='ruleId != null'>
                and rule_id = #{ruleId}
            </if>
            <if test='businessVersionId != null'>
                and business_version_id = #{businessVersionId}
            </if>
            <if test='versionId != null'>
                and version_id = #{versionId}
            </if>
            <if test='scriptId != null'>
                and script_id = #{scriptId}
            </if>
            <if test='unitId != null'>
                and unit_id = #{unitId}
            </if>
            <if test='groupId != null'>
                and group_id = #{groupId}
            </if>
            <if test='workspaceId != null'>
                and workspace_id = #{workspaceId}
            </if>
        </trim>
        ORDER BY PRIORITY ,LAST_UPDATE_DATE DESC,CREATION_DATE DESC
    </select>

    <select id="batchQueryCalculationRules" resultType="com.huawei.it.finance.opt.base.entity.CalculationRule">
        select
        rule_id as ruleId,
        unit_id as unitId,
        group_id as groupId,
        script_id as scriptId,
        <if test="isDetail">
            rich_content as richContent,
            unit_name as unitName,
            unit_code as unitCode,
        </if>
        content as content
        from dm_fsppub_calculation_rule_t
        where unit_id in
        <foreach collection="calculationRuleList" item="rule" open="(" separator="," close=")">
            #{rule.unitId}
        </foreach>
        '@@WHERE_CONDITIONS_WORKSPACE@@'
        ORDER BY PRIORITY ,LAST_UPDATE_DATE DESC,CREATION_DATE DESC
    </select>

    <delete id="delete">
        delete from dm_fsppub_calculation_rule_t
        <trim prefix="where" prefixOverrides="and ">
            <if test='ruleId != null'>
                and rule_id = #{ruleId}
            </if>
            <if test='businessVersionId != null'>
                and business_version_id = #{businessVersionId}
            </if>
            <if test='versionId != null'>
                and version_id = #{versionId}
            </if>
            <if test='unitId != null'>
                and unit_id = #{unitId}
            </if>
            <if test='scriptId != null'>
                and script_id = #{scriptId}
            </if>
            <if test='groupId != null'>
                and group_id = #{groupId}
            </if>
            '@@WHERE_CONDITIONS_WORKSPACE@@'
        </trim>
    </delete>

    <insert id="batchInsertCalculationRules" keyColumn="unit_id"
            parameterType="com.huawei.it.finance.opt.base.entity.CalculationRule"
            useGeneratedKeys="false">
        insert into dm_fsppub_calculation_rule_t
        (
            unit_id,
            rule_id,
            group_id,
            business_version_id,
            version_id,
            source_type,
            script_id,
            row_num,
            line_num,
            priority,
            content,
            rich_content,
            unit_name,
            unit_code,
            workspace_id,
            unit_short_name,
            multiple,
            '@@INSERT_COLUMNS_AUDITS@@'
        )
        values
        <foreach collection="calculationRuleList" item="rule" index="index" open="(" separator="),(" close=")">
            #{rule.unitId,jdbcType=INTEGER},
            #{rule.ruleId,jdbcType=VARCHAR},
            #{rule.groupId,jdbcType=VARCHAR},
            #{rule.businessVersionId,jdbcType=INTEGER},
            #{rule.versionId,jdbcType=INTEGER},
            #{rule.sourceType,jdbcType=VARCHAR},
            #{rule.scriptId,jdbcType=INTEGER},
            #{rule.rowNumber,jdbcType=INTEGER},
            #{rule.lineNumber,jdbcType=INTEGER},
            #{rule.priority,jdbcType=INTEGER},
            #{rule.content,jdbcType=VARCHAR},
            #{rule.richContent,jdbcType=VARCHAR},
            #{rule.unitName,jdbcType=VARCHAR},
            #{rule.unitCode,jdbcType=VARCHAR},
            #{rule.workspaceId,jdbcType=INTEGER},
            #{rule.unitShortName,jdbcType=INTEGER},
            #{rule.multiple,jdbcType=INTEGER},
            '@@INSERT_VALUES_AUDITS@@'
        </foreach>
    </insert>

<!--     where条件1:只写入指定workFlowId的数据，-->
<!--      where条件2:在相同unit_id的条件下，只写入activity_id较大的一条数据，不能直接根据更新时间，因为分布式的条件下，部署的时间不是串行-->
    <insert id="migrateTempTableToRuleTable" parameterType="com.huawei.it.finance.opt.base.entity.CalculationScript">
        WITH ranked_data AS (
            SELECT
                *,
                ROW_NUMBER() OVER (
                    PARTITION BY unit_id
                    ORDER BY activity_id DESC
                    ) AS rn
            FROM dm_fsppub_calculation_rule_temporal_t
            WHERE workflow_id = #{workFlowId}
        )
        INSERT INTO dm_fsppub_calculation_rule_t (
         unit_id, group_id, business_version_id, version_id,
         source_type, script_id, row_num, line_num, priority,
         content, rich_content, unit_name, unit_code, workspace_id,
         creation_date, created_by, last_update_date, last_updated_by,
         rule_id, unit_short_name, multiple
        )
        SELECT
            unit_id, group_id, business_version_id, version_id,
            source_type, script_id, row_num, line_num, priority,
            content, rich_content, unit_name, unit_code, workspace_id,
            creation_date, created_by, last_update_date, last_updated_by,
            rule_id, unit_short_name, multiple
        FROM ranked_data
        WHERE rn = 1;
    </insert>

    <insert id="migrateTempTableDbFuncInfoTable" parameterType="com.huawei.it.finance.opt.base.entity.CalculationScript">
        INSERT INTO dm_fsppub_calculation_dbfunc_info_t
        (   script_id, dbfunc_name, parm_type_list,
            model_id, workspace_id, creation_date,
            created_by, last_update_date, last_updated_by)
        SELECT script_id, dbfunc_name, parm_type_list,
               model_id, workspace_id, creation_date,
               created_by, last_update_date, last_updated_by
        FROM dm_fsppub_calculation_dbfunc_info_temporal_t
        WHERE workflow_id = #{workFlowId}
    </insert>

    <insert id="migrateTempTableDbFuncCellInfoTable" parameterType="com.huawei.it.finance.opt.base.entity.CalculationScript">
        INSERT INTO dm_fsppub_calculation_dbfunc_cell_info_t
        (   unit_id, dynamic_dim_infos, dbfunc_name,
            workspace_id, creation_date, created_by,
            last_update_date, last_updated_by)
        SELECT unit_id, dynamic_dim_infos, dbfunc_name,
               workspace_id, creation_date, created_by,
               last_update_date, last_updated_by
        FROM dm_fsppub_calculation_dbfunc_cell_info_temporal_t
        WHERE workflow_id = #{workFlowId}
    </insert>

    <delete id="batchDeleteExistingCalculationRules" parameterType="com.huawei.it.finance.opt.base.entity.CalculationRule">
        delete from dm_fsppub_calculation_rule_t where unit_id in
        <foreach collection="calculationRuleList" item="rule" index="index" open="(" separator="," close=")">
            #{rule.unitId,jdbcType=INTEGER}
        </foreach>
    </delete>

    <delete id="deleteTempDbFuncInfoTable" parameterType="com.huawei.it.finance.opt.base.entity.CalculationScript">
        DELETE FROM dm_fsppub_calculation_dbfunc_info_temporal_t
        WHERE workflow_id = #{workFlowId}
    </delete>

    <delete id="deleteTempRuleTable" parameterType="com.huawei.it.finance.opt.base.entity.CalculationScript">
        DELETE FROM dm_fsppub_calculation_rule_temporal_t
        WHERE workflow_id = #{workFlowId}
    </delete>

    <delete id="deleteTempDbFuncCellInfoTable" parameterType="com.huawei.it.finance.opt.base.entity.CalculationScript">
        DELETE FROM dm_fsppub_calculation_dbfunc_cell_info_temporal_t
        WHERE workflow_id = #{workFlowId}
    </delete>

    <select id="batchQueryCellElementNamesByIds"
            resultType="com.huawei.it.finance.opt.wrap.entity.CellElement">
        select
        cell_element_id as cellElementId,
        cell_element_name as cellElementName
        from dm_fsppub_calculation_global_cell_t
        where workspace_id = #{workspaceId} and cell_element_id in
        <foreach collection="cellElementIds" item="cellId" open="(" separator="," close=")">
            #{cellId}
        </foreach>
    </select>

    <select id="findMultipleCalculationRules" resultType="com.huawei.it.finance.opt.base.entity.CalculationRule">
        select row_id as rowId,
        rule_id as ruleId,
        unit_id as unitId,
        group_id as groupId,
        script_id as scriptId,
        priority as priority,
        content as content,
        rich_content as richContent,
        unit_name as unitName,
        unit_code as unitCode,
        unit_short_name as unitShortName,
        multiple as multiple,
        '@@SELECT_COLUMNS_WORKSPACE@@',
        '@@SELECT_COLUMNS_AUDITS@@'
        from dm_fsppub_calculation_rule_t
        where multiple = 1 and group_id = #{modelId} '@@WHERE_CONDITIONS_WORKSPACE@@'
        order by unit_id, priority
    </select>

    <update id="update">
        update dm_fsppub_calculation_rule_t
        set
        <if test='calculationRule.priority != null'>
            priority = #{calculationRule.priority},
        </if>
        '@@UPDATE_COLUMNS_AUDITS@@'
        <trim prefix="where" prefixOverrides="and ">
            <if test='calculationRule.rowId != null'>
                and row_id = #{calculationRule.rowId}
            </if>
            <if test='calculationRule.unitId != null'>
                and unit_id = #{calculationRule.unitId}
            </if>
            <if test='calculationRule.groupId != null'>
                and group_id = #{calculationRule.groupId}
            </if>
            <if test='calculationRule.businessVersionId != null'>
                and business_version_id = #{calculationRule.businessVersionId}
            </if>
            <if test='calculationRule.versionId != null'>
                and version_id = #{calculationRule.versionId}
            </if>
            <if test='calculationRule.scriptId != null'>
                and script_id = #{calculationRule.scriptId}
            </if>
            <if test='calculationRule.ruleId != null'>
                and rule_id = #{calculationRule.ruleId}
            </if>
            '@@WHERE_CONDITIONS_WORKSPACE@@'
        </trim>
    </update>

    <select id="batchQueryCalculationRulesV2" resultType="com.huawei.it.finance.opt.base.entity.CalculationRule">
        select
        rule_id as ruleId,
        unit_id as unitId,
        group_id as groupId,
        script_id as scriptId,
        <if test="isDetail">
            rich_content as richContent,
            unit_name as unitName,
            unit_code as unitCode,
        </if>
        content as content
        from dm_fsppub_calculation_rule_t
        <where>
            <if test="idStr != null and idStr != ''">
                and unit_id in (${idStr})
            </if>
            <if test='workspaceId != null'>
                and workspace_id = #{workspaceId}
            </if>
        </where>
        ORDER BY PRIORITY, LAST_UPDATE_DATE DESC,CREATION_DATE DESC
    </select>

    <select id="queryWorkflowInfo" resultType="java.lang.String">
        select distinct(temporal_table_name)
        from dm_fsppub_calculation_workflow_info_t
        where workspace_id = #{workspaceId}
        and workflow_id = #{workFlowId};
    </select>

    <delete id="deleteWorkflowInfo">
        delete
        from dm_fsppub_calculation_workflow_info_t
        where workspace_id = #{workspaceId}
        and workflow_id = #{workFlowId};
    </delete>

    <select id="queryRulesByScriptAndWorkspaceId"
            resultType="com.huawei.it.finance.opt.base.entity.CalculationRule">
        select unit_id   as unitId,
               group_id  as groupId,
               unit_code as unitCode,
               content   as content
        from dm_fsppub_calculation_rule_t
        where workspace_id = #{workspaceId} and script_id = #{scriptId}
    </select>
</mapper>