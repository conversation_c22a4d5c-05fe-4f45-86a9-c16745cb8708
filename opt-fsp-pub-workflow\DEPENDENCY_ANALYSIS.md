# Workflow模块依赖分析及循环依赖检查

## 依赖关系图

```
opt-fsp-pub-workflow
├── opt-fsp-pub-tech-util (✓ 无循环依赖)
├── opt-fsp-pub-task (✓ 无循环依赖)
├── temporal-sdk (✓ 外部依赖)
├── temporal-spring-boot-starter (✓ 外部依赖)
└── spring-* (✓ 外部依赖)

opt-fsp-pub-calculation
├── opt-fsp-pub-workflow (新增依赖)
├── opt-fsp-pub-task (✓ 无循环依赖)
├── opt-fsp-pub-appisland (✓ 无循环依赖)
└── 其他依赖...
```

## 循环依赖检查结果

### ✅ 无循环依赖
经过仔细设计，workflow模块的依赖关系如下：

1. **workflow模块依赖**：
   - `opt-fsp-pub-tech-util` ✓
   - `opt-fsp-pub-task` ✓
   - `temporal-sdk` ✓
   - Spring相关依赖 ✓

2. **calculation模块依赖**：
   - `opt-fsp-pub-workflow` ✓ (新增)
   - 其他现有依赖保持不变

3. **依赖方向**：
   ```
   calculation → workflow → tech-util
   calculation → workflow → task
   ```

### 🔍 避免循环依赖的设计策略

#### 1. 接口分离原则
- workflow模块只定义接口和DTO，不依赖calculation模块的具体实现
- calculation模块实现workflow模块定义的接口

#### 2. 依赖倒置原则
- workflow模块定义`DeployCellActivity`接口
- calculation模块提供`DeployCellActivityImpl`实现
- workflow通过接口调用，不直接依赖实现类

#### 3. 适配器模式
- 如果需要在workflow中使用calculation的功能，通过适配器接口实现
- 适配器实现放在calculation模块中

## 模块职责划分

### opt-fsp-pub-workflow模块
**职责**：工作流编排和管理
- 定义工作流接口和DTO
- 实现Temporal工作流逻辑
- 提供工作流服务接口
- 管理工作流生命周期

**不包含**：
- 具体的业务逻辑实现
- 数据库操作
- DSL编译逻辑

### opt-fsp-pub-calculation模块
**职责**：计算和部署业务逻辑
- 实现workflow模块定义的Activity接口
- DSL编译和部署逻辑
- 数据库操作
- 业务规则处理

## 迁移步骤

### 第一阶段：创建workflow模块（已完成）
1. ✅ 创建workflow模块基础结构
2. ✅ 定义接口和DTO
3. ✅ 实现工作流逻辑
4. ✅ 配置Temporal

### 第二阶段：迁移Activity实现
1. 在calculation模块中创建Activity实现类
2. 实现workflow模块定义的接口
3. 注册Activity到Temporal Worker

### 第三阶段：清理原有代码
1. 删除calculation模块中的原workflow代码
2. 更新import语句
3. 更新测试代码

## 验证方法

### 1. Maven依赖检查
```bash
mvn dependency:tree -Dverbose
```

### 2. 编译检查
```bash
mvn clean compile
```

### 3. 循环依赖检测工具
可以使用Maven插件检测循环依赖：
```xml
<plugin>
    <groupId>org.sonarsource.scanner.maven</groupId>
    <artifactId>sonar-maven-plugin</artifactId>
</plugin>
```

## 风险评估

### 低风险 ✅
- workflow模块独立性强
- 接口设计清晰
- 依赖方向单一

### 注意事项 ⚠️
- 确保Activity实现正确注册到Worker
- 保持接口稳定性
- 版本兼容性管理

## 总结

通过精心设计的模块分离方案，成功避免了循环依赖问题：

1. **单向依赖**：calculation → workflow，workflow不依赖calculation
2. **接口分离**：workflow定义接口，calculation提供实现
3. **职责清晰**：workflow负责编排，calculation负责执行
4. **扩展性好**：便于添加新的工作流类型和活动

这种设计既实现了模块化的目标，又保持了良好的架构设计原则。
