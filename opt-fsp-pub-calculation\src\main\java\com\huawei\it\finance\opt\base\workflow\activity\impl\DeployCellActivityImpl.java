/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.workflow.activity.impl;

import com.huawei.it.finance.opt.base.application.service.ICalculationScriptApplicationService;
import com.huawei.it.finance.opt.base.application.service.impl.CalculationScriptApplicationService;
import com.huawei.it.finance.opt.base.domain.repository.ICalculationScriptRepo;
import com.huawei.it.finance.opt.base.domain.service.impl.CalculationScriptDomainService;
import com.huawei.it.finance.opt.base.entity.CalculationScript;
import com.huawei.it.finance.opt.base.workflow.deploy.DslDeployContext;
import com.huawei.it.finance.opt.dsl.complier.ast.node.ComputeRule;
import com.huawei.it.finance.opt.dsl.complier.output.DeployEffect;
import com.huawei.it.finance.opt.dsl.service.CompileService;
import com.huawei.it.finance.opt.task.entity.QuartzTaskInfo;
import com.huawei.it.finance.opt.task.infrastructure.db.repository.IQuartzTaskInfoRepo;
import com.huawei.it.finance.opt.tech.common.constant.DynamicDataSourceConstant;
import com.huawei.it.finance.opt.workflow.Constants;
import com.huawei.it.finance.opt.workflow.deploy.DeployCellActivity;
import com.huawei.it.finance.opt.workflow.deploy.dto.DeployCellParam;
import com.huawei.it.finance.opt.workflow.deploy.dto.WorkflowParam;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.core.util.exception.NoDataFoundException;
import io.temporal.spring.boot.ActivityImpl;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;

import static com.huawei.it.jalor5.core.request.impl.RequestContextConstants.KEY_TRACE_ID;

/**
 * 部署单元格活动实现类
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
@Slf4j
@Component
@ActivityImpl(taskQueues = Constants.PARALLEL_DEPLOY_QUEUE_NAME)
public class DeployCellActivityImpl extends BaseActivity implements DeployCellActivity {
    
    private final CalculationScriptDomainService calcDomainService;

    @Autowired
    private ICalculationScriptApplicationService iCalculationScriptApplicationService;

    private final CompileService compileService;

    public DeployCellActivityImpl(CalculationScriptApplicationService calcAppService,
                                  IQuartzTaskInfoRepo iQuartzTaskInfoRepo,
                                  CalculationScriptDomainService calcDomainService,
                                  CompileService compileService, 
                                  ICalculationScriptRepo iCalculationScriptRepo) {
        super(calcAppService, iQuartzTaskInfoRepo, iCalculationScriptRepo);
        this.calcDomainService = calcDomainService;
        this.compileService = compileService;
    }

    @Override
    public void deployCellExecute(DeployCellParam param) {
        try {
            if (param.getStartIndex() == null) {
                return;
            }
            MDC.put(DynamicDataSourceConstant.CURRENT_WORKSPACE_ID, String.valueOf(param.getWorkspaceId()));
            MDC.put(DynamicDataSourceConstant.DYNAMIC_DB, param.getDynamicDB());
            
            // 构建上下文
            DslDeployContext context = buildDeployContext(param);
            
            //编译DSL
            context.executeStep(2, "2. 编译规则脚本");
            log.info("2. 编译规则脚本");
            compileService.compileDSL(context);
            
            //获取任务处理的规则子集
            List<ComputeRule> currentComputeRules = buildComputeRules(param, context);
            
            //部署规则子集
            context.executeStep(6, "3. 部署规则脚本");
            log.info("3. 部署规则脚本");
            DeployEffect deployEffect = compileService.deployDSL(currentComputeRules, context);
            CalculationScript calculationScript = buildScript(param);
            context.executeStep(9, "4.1 保存规则子集");
            log.info("4.1 保存规则子集");
            
            //保存规则子集
            iCalculationScriptApplicationService.saveCalculationRules(calculationScript, deployEffect,
                    context, param);
                    
        } catch (NoDataFoundException e) {
            log.info("can not find deploy task, task_id:{}", param.getTaskId());
        } catch (Exception e) {
            log.error("deploy task error, task_id:{}", param.getTaskId(), e);
        } finally {
            MDC.remove(KEY_TRACE_ID);
            MDC.remove(DynamicDataSourceConstant.CURRENT_WORKSPACE_ID);
            MDC.remove(DynamicDataSourceConstant.DYNAMIC_DB);
            RequestContextManager.removeCurrent();
        }
    }

    @Override
    public void deployResultSaveExecute(WorkflowParam workflowParam) {
        try {
            String dynamicDB = workflowParam.getDynamicDB();
            MDC.put(DynamicDataSourceConstant.CURRENT_WORKSPACE_ID, String.valueOf(workflowParam.getWorkspaceId()));
            MDC.put(DynamicDataSourceConstant.DYNAMIC_DB, dynamicDB);
            DslDeployContext context = buildDeployContext(workflowParam);
            
            // 保存本次作业流中部署的规则
            context.executeStep(10, "4.2 分布式保存 汇总后新增规则和脚本:先删除原表的数据再合入新数据 ");
            log.info("4.2 分布式保存 汇总后新增规则和脚本:先删除原表的数据再合入新数据");
            saveRuleResult(workflowParam, dynamicDB);
            
            // 保存本次作业流中部署的增量单元格
            context.executeStep(11, "4.3 分布式保存 汇总后增量单元格:先合入增量单元格到gloabl和calculation表，再删除缓存数据");
            log.info("4.3 分布式保存 汇总后增量单元格:先合入增量单元格到gloabl和calculation表，再删除缓存数据");
            saveCellResult(workflowParam);
            
        } catch (NoDataFoundException e) {
            log.info("can not find deploy task, task_id:{}", workflowParam.getTaskId());
        } catch (Exception e) {
            log.error("deploy task error, task_id:{}", workflowParam.getTaskId(), e);
        } finally {
            MDC.remove(KEY_TRACE_ID);
            MDC.remove(DynamicDataSourceConstant.CURRENT_WORKSPACE_ID);
            MDC.remove(DynamicDataSourceConstant.DYNAMIC_DB);
            RequestContextManager.removeCurrent();
        }
    }

    private void saveRuleResult(WorkflowParam workflowParam, String dynamicDB) {
        CalculationScript calculationScript = CalculationScript.builder().scriptId(workflowParam.getScriptId())
                .modelId(workflowParam.getModelId()).build();
        calculationScript.setWorkspaceId(workflowParam.getWorkspaceId());
        calculationScript.setDbCode(dynamicDB);
        QuartzTaskInfo taskInfo = new QuartzTaskInfo();
        taskInfo.setJobName(workflowParam.getJobName());
        try {
            iCalculationScriptApplicationService.deleteTemporalRules(calculationScript, taskInfo);
            iCalculationScriptApplicationService.migrateDataToRuleTable(calculationScript, workflowParam.getWorkFlowId());
        } catch (Exception e) {
            log.error("saveRuleResult fail, workFlow {}", workflowParam.getWorkFlowId());
        }
    }

    void saveCellResult(WorkflowParam workflowParam) {
        if (workflowParam == null || workflowParam.getWorkFlowId() == null || workflowParam.getWorkspaceId() == null) {
            return;
        }
        QuartzTaskInfo taskInfo = new QuartzTaskInfo();
        taskInfo.setJobName(workflowParam.getJobName());
        StopWatch sw = new StopWatch("5. 分布式保存 汇总后增量单元格:先合入增量单元格到gloabl和calculation表，再删除缓存数据");
        sw.start("5. 分布式保存 汇总后增量单元格:先合入增量单元格到gloabl和calculation表，再删除缓存数据");
        
        // 1.获取本次workflow部署的相关记录
        List<String> temporalTableName = iCalculationScriptApplicationService.queryWorkflowInfo(
                workflowParam.getWorkspaceId(), workflowParam.getWorkFlowId());
        // 2.写入calculation表,并清除缓存数据
        iCalculationScriptApplicationService.migrateDataToCalculationTable(temporalTableName, workflowParam.getWorkFlowId());
        // 3.入global表，并清除缓存数据
        iCalculationScriptApplicationService.migrateDataToGlobalTable(workflowParam.getWorkFlowId());
        // 4.删除本次workflow部署的相关记录
        iCalculationScriptApplicationService.deleteWorkflowInfo(workflowParam.getWorkspaceId(), workflowParam.getWorkFlowId());
        sw.stop();
    }

    private List<ComputeRule> buildComputeRules(DeployCellParam param, DslDeployContext context) {
        if (context.getComputeRules() == null || context.getComputeRules().isEmpty()) {
            return new ArrayList<>();
        }
        int total = context.getComputeRules().size();
        int startIndex = param.getStartIndex();
        int endIndex = param.getEndIndex() > total ? total : param.getEndIndex();
        // 取子集的范围是 左闭右开, 赋值给endIndex的total值不需要size-1
        List<ComputeRule> currentComputeRules = context.getComputeRules().subList(startIndex, endIndex);
        for (ComputeRule computeRule : currentComputeRules) {
            computeRule.expandComputeUnit();
        }
        return currentComputeRules;
    }
}
