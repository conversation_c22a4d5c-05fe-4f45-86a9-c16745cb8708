/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.infrastructure.db.repository.impl;

import com.huawei.it.finance.opt.base.domain.repository.ICalculationRuleRepo;
import com.huawei.it.finance.opt.base.entity.CalculationRule;
import com.huawei.it.finance.opt.base.entity.CalculationScript;
import com.huawei.it.finance.opt.base.entity.dbfunction.DbFuncDimsCombEntity;
import com.huawei.it.finance.opt.base.entity.dbfunction.DbFuncEntity;
import com.huawei.it.finance.opt.base.infrastructure.db.dao.CalculationRuleDao;
import com.huawei.it.finance.opt.base.util.CalculationScriptConstants;
import com.huawei.it.finance.opt.workflow.deploy.dto.DeployCellParam;
import com.huawei.it.finance.opt.dsl.complier.objcode.ReserveWordInObjectCode;
import com.huawei.it.finance.opt.fsp.pub.common.exception.DataSourceException;
import com.huawei.it.finance.opt.tech.util.DataSourceUtil;
import com.huawei.it.finance.opt.tech.util.DictConfigUtil;
import com.huawei.it.finance.opt.tech.util.FutureUtil;
import com.huawei.it.finance.opt.wrap.entity.CellElement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.MANUAL;
import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.RULE_DEPLOY_NEW_MODELS;
import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.SQL_BUILD_POLICY;
import static com.huawei.it.finance.opt.workflow.deploy.impl.DslDeployWorkflowImpl.isWorkflowDeploy;

@Slf4j
@Component
public class CalculationRuleRepo implements ICalculationRuleRepo {
    @Value("${opt_fsp_cell_batch_query_num:1000}")
    private int CELL_BATCH_QUERY_NUM;

    @Value("${new-deploy-models:}")
    private String newDeployModels;

    @Resource
    private CalculationRuleDao calculationRuleDao;

    @Autowired
    private BatchInsertRepo batchInsertRepo;

    @Autowired
    private DbFunctionRepo dbFunctionRepo;

    private final static String ALIAS_ARRAY_PATTERN = "\\('([^']*)'\\)";

    @Override
    public List<CalculationRule> getCalculationRules(CalculationRule calculationRule) {
        return calculationRuleDao.findCalculationRules(calculationRule);
    }

    /**
     * 根据单元格ID批量查询单元格名称
     *
     */
    @Override
    public List<CellElement> getCellElementNamesByIds(List<Long> cellElementIds, long workspaceId) {
        List<CellElement> cellElementList = new ArrayList<>();
        List<List<Long>> cellElementIdsPartition = ListUtils.partition(cellElementIds, CELL_BATCH_QUERY_NUM);
        for (List<Long> cellElementIdsSlice : cellElementIdsPartition) {
            cellElementList
                    .addAll(calculationRuleDao.batchQueryCellElementNamesByIds(cellElementIdsSlice, workspaceId));
        }
        return cellElementList;
    }

    /**
     * 查询最高优先级的计算规则
     *
     */
    @Override
    public List<CalculationRule> getHighestPriorityUnitCalculationRules(CalculationRule calculationRule) {
        Set<Long> unitIds = new HashSet<>();
        return calculationRuleDao.findHighestPriorityUnitCalculationRules(calculationRule).stream().filter(c -> {
            Long unitId = c.getUnitId();
            if (unitIds.contains(unitId)) {
                return false;
            } else {
                unitIds.add(unitId);
                return true;
            }
        }).collect(Collectors.toList());
    }

    /**
     * 根据规则脚本，删除所有已经部署的单元格粒度规则
     *
     * @param calculationScript 计算脚本
     */
    @Override
    public void deleteAllCalculationRulesByCalculationScript(CalculationScript calculationScript) {
        CalculationRule calculationRule = new CalculationRule();
        calculationRule.setScriptId(calculationScript.getScriptId());
        calculationRuleDao.delete(calculationRule);
    }

    /**
     * 批量插入DB函数涉及单元格信息
     *
     */
    @Override
    public void batchInsertDbFuncDimsCombs(Map<Long, CalculationRule> ruleMap, DeployCellParam param) throws DataSourceException {
        try {
            List<DbFuncDimsCombEntity> dbFuncDimsCombEntityList = new ArrayList<>();
            Iterator<Long> keyIt = ruleMap.keySet().iterator();
            while (keyIt.hasNext()) {
                CalculationRule rule = ruleMap.get(keyIt.next());
                if (rule.getDbFuncDimsCombMap() != null && !rule.getDbFuncDimsCombMap().isEmpty()) {
                    dbFuncDimsCombEntityList.addAll(this.initDbFuncDimsCombEntity(rule));
                }
            }
            if (!dbFuncDimsCombEntityList.isEmpty()) {
                Long scriptId = getFirstScriptIdFromRuleMap(ruleMap);
                if(isWorkflowDeploy(scriptId)){
                    doBatchInsertTempDbFuncDimsCombs(dbFuncDimsCombEntityList, param);
                }else{
                    doBatchInsertDbFuncDimsCombs(dbFuncDimsCombEntityList);
                }
            }
        } catch (Exception e) {
            log.error("error: ", e);
            throw new DataSourceException("execute save DbFunc cell infos sql failed!");
        }

    }

    public Long getFirstScriptIdFromRuleMap(Map<Long, CalculationRule> ruleMap) {
        for (CalculationRule rule : ruleMap.values()) {
            if (rule != null) {
                return rule.getScriptId();
            }
        }
        return null; // 如果没有找到非空的 CalculationRule 对象，返回 null
    }


    private void doBatchInsertDbFuncDimsCombs(List<DbFuncDimsCombEntity> dbFuncDimsCombEntityList) {
        dbFunctionRepo.batchInsertDbFuncCellInfo(dbFuncDimsCombEntityList);
    }

    private void doBatchInsertTempDbFuncDimsCombs(List<DbFuncDimsCombEntity> dbFuncDimsCombEntityList, DeployCellParam param) {
        dbFunctionRepo.batchInsertTempDbFuncCellInfo(dbFuncDimsCombEntityList, param);
    }

    @Override
    public void doBatchDeleteDbFuncDimsCombs(List<Long> scriptIds) {
        dbFunctionRepo.batchDeleteDbFuncCellInfo(scriptIds);
    }

    @Override
    public void doBatchDeleteDbFunc(List<Long> scriptIds) {
        dbFunctionRepo.batchDeleteDbFuncInfo(scriptIds);
    }

    @Override
    public List<String> queryDbFuncNamesByScriptIds(List<Long> scriptIds) {
        List<DbFuncEntity> dbFuncEntities = dbFunctionRepo.queryDbFuncByScriptIds(scriptIds);
        return dbFuncEntities.stream().map(DbFuncEntity::getDbFuncName).collect(Collectors.toList());
    }

    private List<DbFuncDimsCombEntity> initDbFuncDimsCombEntity(CalculationRule rule) {
        List<DbFuncDimsCombEntity> dbFuncDimsCombEntityList = new ArrayList<>();
        Map<String, String> dimsCombMap = rule.getDbFuncDimsCombMap();
        for (Map.Entry<String, String> dimsCode : dimsCombMap.entrySet()) {
            DbFuncDimsCombEntity dbFuncDimsCombEntity = DbFuncDimsCombEntity.builder().unitId(rule.getUnitId())
                    .dbFuncName(dimsCode.getKey()).dimsCombCode(dimsCode.getValue()).workspaceId(rule.getWorkspaceId())
                    .build();
            dbFuncDimsCombEntityList.add(dbFuncDimsCombEntity);
        }
        return dbFuncDimsCombEntityList;
    }

    /**
     * 批量插入单元格粒度规则
     *
     */
    @Override
    public void batchInsertCalculationRules(Map<Long, CalculationRule> ruleMap) {
        List<CalculationRule> calculationScriptList = generateCalculationRules(ruleMap);
        doBatchInsertCalculationRules(calculationScriptList);
    }

    @Override
    public void batchInsertCalculationRulesForTemporal(Map<Long, CalculationRule> ruleMap, DeployCellParam param) {
        List<CalculationRule> calculationScriptList = generateCalculationRules(ruleMap);
        doBatchInsertCalculationRulesForTemporal(calculationScriptList, param);
    }

    private @NotNull List<CalculationRule> generateCalculationRules(Map<Long, CalculationRule> ruleMap) {
        List<CalculationRule> calculationScriptList = new ArrayList<>();
        Iterator<Long> keyIt = ruleMap.keySet().iterator();
        while (keyIt.hasNext()) {
            CalculationRule rule = ruleMap.get(keyIt.next());
            String content = rule.getContent();
            String richContent = rule.getRichContent();
            // 根据 content 来判断该规则是否为多路径
            // 根据第一个字符是不是|来做区分。 且针对OR对应的语法|| ，不能简单split。
            boolean isMultiple = false;
            if (content.length() > 0) {
                String firstChar = content.substring(0, 1);
                if (ReserveWordInObjectCode.SEQ.equals(firstChar)) {
                    // 说明是多路径的规则
                    isMultiple = true;
                }
            }
            if (isMultiple) {
                String[] subContents = readMultipleRuleContentsFromStr(content);
                String[] aliases = readAliasArrayFromRichContent(richContent);
                int priority = 0;
                for (int i = 0; i < subContents.length; i++) {
                    String subContent = "";
                    if (i < subContents.length) {
                        subContent = subContents[i];
                    }
                    if (!StringUtils.isBlank(subContent)) {
                        priority++;
                        calculationScriptList.add(initMultipleRule(rule, priority, subContent, aliases[i]));
                    }
                }
            } else {
                rule.setMultiple(CalculationScriptConstants.RULE_NOT_MULTIPLE);
                calculationScriptList.add(rule);
            }
        }
        return calculationScriptList;
    }

    /**
     * 多线程并发INSERT INTO
     *
     * @param calculationScriptList
     */
    private void doBatchInsertCalculationRules(List<CalculationRule> calculationScriptList) {
        String modelId = Optional.ofNullable(calculationScriptList.get(0)).map(CalculationRule::getGroupId)
                .orElse(UUID.randomUUID().toString());
        newDeployModels = DictConfigUtil.getProperty(RULE_DEPLOY_NEW_MODELS, newDeployModels);
        if ("*".equals(newDeployModels) || newDeployModels.contains(modelId)) {
            log.info("this model {} is using new save calculation rule code.", modelId);
            batchInsertRepo.batchInsert(calculationScriptList);
        } else {
            int partitionNum = FutureUtil.getPartitionNum(calculationScriptList.size());
            List<List<CalculationRule>> calculationRulePartitionList = ListUtils.partition(calculationScriptList,
                    partitionNum);
            List<Runnable> taskList = new ArrayList<>(calculationRulePartitionList.size());
            for (List<CalculationRule> calculationRuleList : calculationRulePartitionList) {
                taskList.add(() -> calculationRuleDao.batchInsertCalculationRules(calculationRuleList));
            }
            FutureUtil.submitTasksAndWaitForCompletion(taskList);
        }
    }

    private void doBatchInsertCalculationRulesForTemporal(List<CalculationRule> calculationScriptList,
                                                          DeployCellParam param) {
        String modelId = Optional.ofNullable(calculationScriptList.get(0)).map(CalculationRule::getGroupId)
                .orElse(UUID.randomUUID().toString());
        log.info("this model {} is using new save calculation rule code.", modelId);
        batchInsertRepo.batchInsertRuleTempTable(calculationScriptList, param.getWorkFlowId(), param.getActivityId());

    }


    private CalculationRule initMultipleRule(CalculationRule rule, int priority, String content, String alias) {
        CalculationRule calculationRule = CalculationRule.builder().ruleId(rule.getRuleId()).unitId(rule.getUnitId())
                .businessVersionId(rule.getBusinessVersionId()).versionId(rule.getVersionId())
                .groupId(rule.getGroupId()).sourceType(rule.getSourceType()).scriptId(rule.getScriptId())
                .rowNumber(rule.getRowNumber()).lineNumber(rule.getLineNumber()).priority(priority)
                .unitCode(rule.getUnitCode()).unitName(rule.getUnitName()).content(content)
                .multiple(CalculationScriptConstants.RULE_IS_MULTIPLE).build();
        calculationRule.setWorkspaceId(rule.getWorkspaceId());
        // AKA属性设置
        if (!StringUtils.isEmpty(alias)) {
            calculationRule.setUnitShortName(alias);
        } else {
            calculationRule.setUnitShortName("规则：" + priority);
        }
        return calculationRule;
    }

    /**
     * 把一个多路径规则的字符串信息拆分成N个独立的小规则
     *
     * @param content
     * @return
     */
    private String[] readMultipleRuleContentsFromStr(String content) {
        String filteredContent = content.replaceAll(CalculationScriptConstants.CALCULATION_ALIAS_FUNCTION_SEQ, "");
        int length = filteredContent.length();
        List<String> subContents = new ArrayList<>();
        int startIndex = 1;
        for (int i = 1; i < length - 1; i++) {
            // 从这里要注意越界异常，能进入这里说明content字符串的首字母一定是|符号，因此i1开始遍历，需要找出前后都不是|符号的index，以此找到添加到subContents列表的子串。
            String subStr = filteredContent.substring(i, i + 1);
            String previousStr = filteredContent.substring(i - 1, i);
            String nextStr = filteredContent.substring(i + 1, i + 2);
            if (ReserveWordInObjectCode.SEQ.equals(subStr) && !ReserveWordInObjectCode.SEQ.equals(previousStr)
                    && !ReserveWordInObjectCode.SEQ.equals(nextStr)) {
                subContents.add(filteredContent.substring(startIndex, i));
                startIndex = i + 1;
            }
        }
        if (startIndex < length - 1) {
            subContents.add(filteredContent.substring(startIndex, length));
        }
        String[] array = new String[subContents.size()];
        return subContents.toArray(array);
    }

    private void addAliasKeywords(StringBuffer buffer) {
        buffer.append(ReserveWordInObjectCode.ALIAS);
        buffer.append(ReserveWordInObjectCode.LEFT_BRACKET);
        buffer.append(ReserveWordInObjectCode.SINGLE_QUOTES);
        buffer.append(ReserveWordInObjectCode.SINGLE_QUOTES);
        buffer.append(ReserveWordInObjectCode.RIGHT_BRACKET);
    }

    private String[] readAliasArrayFromRichContent(String richContent) {
        String filteredContent = richContent.replaceAll(CalculationScriptConstants.CALCULATION_ALIAS_FUNCTION_SEQ, "");
        int length = filteredContent.length();
        StringBuffer fixedContentBuffer = new StringBuffer();
        for (int i = 1; i < length - 1; i++) {
            // 补齐AKA避免因为DSL漏写别名函数而产生异常。
            String subStr = filteredContent.substring(i, i + 1);
            String nextStr = filteredContent.substring(i + 1, i + 2);
            fixedContentBuffer.append(subStr);
            if (ReserveWordInObjectCode.SEQ.equals(subStr) && ReserveWordInObjectCode.SEQ.equals(nextStr)) {
                addAliasKeywords(fixedContentBuffer);
                continue;
            }
            if (ReserveWordInObjectCode.SEQ.equals(subStr) && ReserveWordInObjectCode.SEMICOLON.equals(nextStr)) {
                addAliasKeywords(fixedContentBuffer);
            }
        }
        List<String> list = match(fixedContentBuffer.toString());
        return list.toArray(new String[list.size()]);
    }

    private List<String> match(String input) {
        List<String> list = new ArrayList<>();
        Pattern pattern = Pattern.compile(ALIAS_ARRAY_PATTERN);
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            String alias = matcher.group(1);
            list.add(alias);
        }
        return list;
    }

    /**
     * 删除一组所有已经部署的单元格粒度规则
     *
     * @param groupId
     * @return
     */
    @Override
    public void deleteAllCalculationRulesByGroupId(String groupId, long workspaceId) {
        CalculationRule calculationRule = new CalculationRule();
        calculationRule.setGroupId(groupId);
        calculationRule.setWorkspaceId(workspaceId);
        calculationRuleDao.delete(calculationRule);
    }

    /**
     * 删除一个规则里所有已经部署的单元格粒度规则
     *
     * @param scriptId
     * @param workspaceId
     * @return
     */
    @Override
    public void deleteCalculationRulesByScriptId(long scriptId, long workspaceId) {
        CalculationRule calculationRule = new CalculationRule();
        calculationRule.setScriptId(scriptId);
        calculationRule.setWorkspaceId(workspaceId);
        calculationRuleDao.delete(calculationRule);
    }

    @Override
    public List<CalculationRule> batchQueryCalculationRules(List<CalculationRule> param, boolean isDetail) {
        String policy = DictConfigUtil.getProperty(SQL_BUILD_POLICY, MANUAL);
        if (MANUAL.equals(policy)) {
            List<Long> unidIdList = param.stream().map(CalculationRule::getUnitId).collect(Collectors.toList());
            return batchQueryCalculationRulesV2(unidIdList, isDetail);
        } else {
            return calculationRuleDao.batchQueryCalculationRules(param, isDetail);
        }
    }

    /**
     * 删除一个作业空间下的所有已经部署的单元格粒度规则
     *
     * @param workspaceId
     */
    @Override
    public void deleteAllCalculationRulesByWorkspaceId(long workspaceId) {
        CalculationRule calculationRule = new CalculationRule();
        calculationRule.setWorkspaceId(workspaceId);
        calculationRuleDao.delete(calculationRule);
    }

    /**
     * 查询一个模型瞎的多路径的计算规则
     *
     * @param modelId
     * @param workspaceId
     * @return
     */
    @Override
    public List<CalculationRule> getCalculationRules(String modelId, long workspaceId) {
        return calculationRuleDao.findMultipleCalculationRules(modelId, workspaceId);
    }

    @Override
    public void updateCalculationRule(CalculationRule calculationRule) {
        calculationRuleDao.update(calculationRule);
    }

    @Override
    public List<CalculationRule> batchQueryCalculationRulesV2(List<Long> idList, boolean isDetail) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }

        String inPartSql = String.join(",", idList.stream().map(String::valueOf).toArray(String[]::new));
        return calculationRuleDao.batchQueryCalculationRulesV2(inPartSql, isDetail, DataSourceUtil.getWorkspaceId());
    }

    @Override
    public void migrateTempTableToRuleTable(CalculationScript calculationScript, Integer workFlowId) {
        calculationRuleDao.migrateTempTableToRuleTable(calculationScript, workFlowId);
        // 删除缓存表数据
        calculationRuleDao.deleteTempRuleTable(calculationScript, workFlowId);
    }

    @Override
    public void migrateTempTableDbFuncInfoTable(CalculationScript calculationScript, Integer workFlowId) {
        calculationRuleDao.migrateTempTableDbFuncInfoTable(calculationScript, workFlowId);
        // 删除缓存表数据
        calculationRuleDao.deleteTempDbFuncInfoTable(calculationScript, workFlowId);
    }

    @Override
    public void migrateTempTableDbFuncCellInfoTable(CalculationScript calculationScript, Integer workFlowId) {
        calculationRuleDao.migrateTempTableDbFuncCellInfoTable(calculationScript, workFlowId);
        // 删除缓存表数据
        calculationRuleDao.deleteTempDbFuncCellInfoTable(calculationScript, workFlowId);
    }

    @Override
    public List<String> queryWorkflowInfo(Long workspaceId, Integer workFlowId) {
        return calculationRuleDao.queryWorkflowInfo(workspaceId, workFlowId);
    }

    @Override
    public int deleteWorkflowInfo(Long workspaceId, Integer workFlowId){
        return calculationRuleDao.deleteWorkflowInfo(workspaceId, workFlowId);
    }


    @Override
    public List<CalculationRule> queryRulesByScriptAndWorkspaceId(@Param("workspaceId") Long workspaceId,
                                                           @Param("scriptId") Long scriptId){
        return calculationRuleDao.queryRulesByScriptAndWorkspaceId(workspaceId, scriptId);
    }
}