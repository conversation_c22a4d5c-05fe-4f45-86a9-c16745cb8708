/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.workflow.deploy;

import com.huawei.it.finance.opt.workflow.deploy.dto.WorkflowParam;
import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;

/**
 * DSL部署工作流接口
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
@WorkflowInterface
public interface DslDeployWorkflow {
    
    /**
     * 部署DSL规则
     *
     * @param workflowParam 工作流参数
     * @return 部署结果状态码
     */
    @WorkflowMethod
    int deploy(WorkflowParam workflowParam);
}
