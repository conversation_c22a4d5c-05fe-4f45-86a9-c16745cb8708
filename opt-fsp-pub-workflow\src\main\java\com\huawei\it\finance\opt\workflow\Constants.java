/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.workflow;

/**
 * Workflow常量类
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
public class Constants {
    /**
     * 并行部署队列名称
     */
    public static final String PARALLEL_DEPLOY_QUEUE_NAME = "ParallelDeployQueue";

    /**
     * 工作流超时时间（秒）
     */
    public static final int WORKFLOW_TIMEOUT_SECONDS = 3600;

    /**
     * 活动超时时间（秒）
     */
    public static final int ACTIVITY_TIMEOUT_SECONDS = 1800;

    /**
     * 重试次数
     */
    public static final int MAX_RETRY_ATTEMPTS = 3;

    private Constants() {
        // 私有构造函数，防止实例化
    }
}
