/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.infrastructure.db.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.huawei.it.finance.opt.base.entity.CalculationRule;
import com.huawei.it.finance.opt.base.entity.CalculationScript;
import com.huawei.it.finance.opt.wrap.entity.CellElement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("#mysession.dynamicDB")
public interface CalculationRuleDao {
    List<CalculationRule> findCalculationRules(CalculationRule rule);

    // TODO:这个方法没用到，目前只在UT用到了，可以清理
    List<CalculationRule> findHighestPriorityUnitCalculationRules(CalculationRule rule);

    List<CalculationRule> findCompactCalculationRules(CalculationRule rule);

    int delete(CalculationRule rule);

    int batchInsertCalculationRules(@Param("calculationRuleList") List<CalculationRule> calculationRuleList);

    // TODO:这个方法没用到，目前只在UT用到了，可以清理
    int batchDeleteExistingCalculationRules(@Param("calculationRuleList") List<CalculationRule> calculationRuleList);

    List<CalculationRule> batchQueryCalculationRules(@Param("calculationRuleList") List<CalculationRule> param,
                                                     @Param("isDetail") boolean isDetail);

    List<CellElement> batchQueryCellElementNamesByIds(@Param("cellElementIds") List<Long> cellElementIds,
                                                      @Param("workspaceId") long workspaceId);

    List<CalculationRule> findMultipleCalculationRules(@Param("modelId") String modelId,
                                                       @Param("workspaceId") long workspaceId);

    void update(@Param("calculationRule") CalculationRule calculationRule);

    List<CalculationRule> batchQueryCalculationRulesV2(@Param("idStr") String idStr, @Param("isDetail") boolean isDetail,
                                                       @Param("workspaceId") Long workspaceId);

    int migrateTempTableToRuleTable(@Param("calculationScript") CalculationScript calculationScript,
                                    @Param("workFlowId") Integer workFlowId);

    int migrateTempTableDbFuncInfoTable(@Param("calculationScript") CalculationScript calculationScript,
                                        @Param("workFlowId") Integer workFlowId);

    int migrateTempTableDbFuncCellInfoTable(@Param("calculationScript") CalculationScript calculationScript,
                                            @Param("workFlowId") Integer workFlowId);

    int deleteTempRuleTable(@Param("calculationScript") CalculationScript calculationScript,
                            @Param("workFlowId") Integer workFlowId);

    int deleteTempDbFuncInfoTable(@Param("calculationScript") CalculationScript calculationScript,
                                  @Param("workFlowId") Integer workFlowId);

    int deleteTempDbFuncCellInfoTable(@Param("calculationScript") CalculationScript calculationScript,
                                      @Param("workFlowId") Integer workFlowId);

    List<String> queryWorkflowInfo(@Param("workspaceId") Long workspaceId, @Param("workFlowId") Integer workFlowId);

    int deleteWorkflowInfo(@Param("workspaceId") Long workspaceId, @Param("workFlowId") Integer workFlowId);

    List<CalculationRule> queryRulesByScriptAndWorkspaceId(@Param("workspaceId") Long workspaceId,
                                                           @Param("scriptId") Long scriptId);
}
