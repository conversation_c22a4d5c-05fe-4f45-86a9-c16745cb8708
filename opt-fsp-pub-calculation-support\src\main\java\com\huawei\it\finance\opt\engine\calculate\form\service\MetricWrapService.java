/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.engine.calculate.form.service;

import com.huawei.it.common.exception.ApplicationException;
import com.huawei.it.finance.opt.base.entity.AxisModel;
import com.huawei.it.finance.opt.base.entity.Constants;
import com.huawei.it.finance.opt.base.entity.Metric;
import com.huawei.it.finance.opt.base.entity.MetricDataType;
import com.huawei.it.finance.opt.base.exception.CalculationException;
import com.huawei.it.finance.opt.base.service.MetricService;
import com.huawei.it.finance.opt.fsp.pub.application.request.FormCellInfoRequest;
import com.huawei.it.finance.opt.fsp.pub.application.response.BaseResponse;
import com.huawei.it.finance.opt.fsp.pub.application.response.FormDataQueryResponse;
import com.huawei.it.finance.opt.fsp.pub.application.service.IQueryMetricValueApplicationService;
import com.huawei.it.finance.opt.fsp.pub.common.exception.DataSourceException;
import com.huawei.it.finance.opt.fsp.pub.domain.entity.DimInstance;
import com.huawei.it.finance.opt.tech.enums.CellTypeEnum;
import com.huawei.it.finance.opt.tech.exception.PubErrorCode;
import com.huawei.it.finance.opt.wrap.dao.CellDimensionDao;
import com.huawei.it.finance.opt.wrap.entity.CellDimension;
import com.huawei.it.finance.opt.wrap.entity.CellElement;
import com.huawei.it.finance.opt.wrap.service.CellDimensionService;
import com.huawei.it.finance.pub.dimmodel.application.dto.response.DimModelResponse;
import com.huawei.it.finance.pub.dimmodel.application.dto.response.MyDimensionResponse;
import com.huawei.it.finance.pub.dimmodel.application.service.IMyDimMemberApplicationService;
import com.huawei.it.finance.pub.dimmodel.common.exception.CustomException;
import com.huawei.it.finance.pub.dimmodel.domain.entity.MyDimMemberEntity;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
public class MetricWrapService implements MetricService {
    private static final Logger logger = LoggerFactory.getLogger(MetricWrapService.class);

    @Autowired
    private CellDimensionService cellDimensionService;

    @Autowired
    private CellDimensionDao cellDimensionDao;

    @Autowired
    private IQueryMetricValueApplicationService queryMetricValueApplicationService;

    @Autowired
    private IMyDimMemberApplicationService dimMemberApplicationService;

    //TODO：待修改，60000比较极限
    @Value("${calculate.metric-load.size:60000}")
    private int maxQueryParamSize;

    @Override
    public Map<String, Metric> getMetrics(List<Metric> metrics) {
        logger.info("prepare to load metric size:{}", metrics.size());
        // 将节点按模型分批后分批查数
        Map<String, List<Metric>> metricsGroup = groupMetricsByModelId(metrics);
        // 查询Metric的值，把数据库中的value值装配到指标对象Metric中
        Map<Long, Metric> allMetricsInModel = getMetricsInModel(metricsGroup);

        // 处理dbFuncMap
        Map<String, Metric> dbFuncMap = new HashMap<>();
        for (Metric metric : metrics) {
            if (metric.getFuncName() != null) {
                dbFuncMap.put(metric.getFuncKey(), metric);
            }
        }

        // 处理metricMap
        Map<String, Metric> metricMap = new HashMap<>();
        for (Metric metric : metrics) {
            if (metric.getUnitId() != null) {
                Long unitId = metric.getUnitId();
                if (allMetricsInModel.containsKey(unitId)) {
                    copyMetric(allMetricsInModel.get(unitId), metric);
                } else {
                    logger.info("metric not found, unitId:{}", unitId);
                }
                metricMap.put(String.format(Constants.KEY_FORMAT, unitId), metric);
            }
        }

        if (!dbFuncMap.isEmpty()) {
            metricMap.putAll(dbFuncMap);
        }
        return metricMap;
    }

    // TODO: 20250529 修改groupMetricsByModelId方法。避免创建冗余对象CellElement，减少内存消耗
    public Map<String, List<Metric>> groupMetricsByModelIdV1(List<Metric> metrics) {
        if (metrics == null || metrics.isEmpty()) {
            return Collections.emptyMap();
        }

        // Step 1: 构建 unitId 到 Metric 的映射，并去重收集 unitId
        Map<Long, Metric> unitIdToMetricMap = new HashMap<>();
        List<Long> unitIds = new ArrayList<>();

        for (Metric metric : metrics) {
            Long unitId = metric.getUnitId();
            if (unitId != null && !unitIdToMetricMap.containsKey(unitId)) {
                unitIdToMetricMap.put(unitId, metric);
                unitIds.add(unitId);
            }
        }

        // Step 2: 分批查询 modelId.TODO:考虑变成分批异步查询，提供吞吐量。及时手动释放不用的内存 spring batch
        Map<Long, String> unitIdToModelIdMap = new HashMap<>();
        for (List<Long> batch : ListUtils.partition(unitIds, maxQueryParamSize)) {
            Map<Long, String> batchResult = cellDimensionDao.batchFindModelIdByUnitIds(batch);
            unitIdToModelIdMap.putAll(batchResult);
        }

        // Step 3: 按 modelId 分组 Metric
        Map<String, List<Metric>> groupedMetrics = new HashMap<>();

        for (Map.Entry<Long, String> entry : unitIdToModelIdMap.entrySet()) {
            Long unitId = entry.getKey();
            String modelId = entry.getValue();
            Metric metric = unitIdToMetricMap.get(unitId);

            if (metric != null) {
                metric.setGroupId(modelId);
                groupedMetrics.computeIfAbsent(modelId, k -> new ArrayList<>()).add(metric);
            }
        }

        return groupedMetrics;
    }

    @Override
    public Map<String, List<Metric>> groupMetricsByModelId(List<Metric> metrics) {
        // TODO: SQL查询和内存的平衡?
        Map<String, List<Metric>> metricsGroup = new HashMap<>();
        // 问题：对于重复的unitId,会覆盖value
        Map<Long, Metric> unitId2Metric = metrics.stream()
                .collect(Collectors.toMap(Metric::getUnitId, Function.identity(), (o1, o2) -> o1));
        List<CellElement> cellElementList = metrics.stream().map(Metric::getUnitId).map(id -> {
            CellElement cellElement = new CellElement();
            cellElement.setCellElementId(id);
            return cellElement;
        }).collect(Collectors.toList());

        // TODO 字符串拼接，替代Lists.partition
        for (List<CellElement> cellElementSlice : ListUtils.partition(cellElementList, maxQueryParamSize)) {
            // 根据unitId查询modelId.
            List<CellElement> cellElements = cellDimensionDao.batchFindCellElement(cellElementSlice);
            Map<Long, String> unitId2ModelId = cellElements.stream()
                    .collect(Collectors.toMap(CellElement::getCellElementId, CellElement::getModelId, (o1, o2) -> o1));
            unitId2ModelId.forEach((unitId, modelId) -> {
                Metric metric = unitId2Metric.get(unitId);
                metric.setGroupId(modelId);
                metricsGroup.computeIfAbsent(modelId, it -> new ArrayList<>()).add(metric);
            });
        }
        return metricsGroup;
    }

    private Map<Long, Metric> getMetricsInModel(Map<String, List<Metric>> metricsGroup) {
        Map<Long, Metric> metricsFromDataSource = new ConcurrentHashMap<>();
        // TODO 待测试是否开启多线程查询
        metricsGroup.forEach((modelId, metrics) -> {
            try {
                logger.info("metric load for modelId:{}", modelId);
                metricsFromDataSource.putAll(getOneModelMetrics(modelId, metrics));
            } catch (Exception ex) {
                // 线程保护, 直接捕获exception
                logger.error("load one model metrics failed.then exited..", ex);
                throw new CalculationException(ex);
            }
        });
        return metricsFromDataSource;
    }

    // 根据模型和单元格信息，查询底表数据
    private Map<Long, Metric> getOneModelMetrics(String modelId, List<Metric> metrics) throws DataSourceException {
        if (metrics.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<Long, Metric> unitId2Metric = metrics.stream()
                .collect(Collectors.toMap(Metric::getUnitId, Function.identity(), (o1, o2) -> o1));
        // 1.根据modelId查询单元格模板
        CellElement cellElementTemplate = cellDimensionService.buildCellElementTemplate(modelId);
        // 2. 加载模型缓存，使用redis
        DimModelResponse dimModelResponse = cellDimensionService.getDimModelResponse(cellElementTemplate.getModelId());
        Map<String, Map<String, MyDimMemberEntity>> dim2MemberCode2MemberEntity = toDimMembers(dimModelResponse);
        List<Map> cellList = cellDimensionService.batchQueryValueByMetrics(cellElementTemplate, metrics);
        logger.info("size of result:{}, querysize:{}", cellList.size(), metrics.size());
        assembleValue2Metric(unitId2Metric, cellElementTemplate, dim2MemberCode2MemberEntity, cellList);
        return unitId2Metric;
    }

    // 把数据库中的值装配到指标对象中
    private void assembleValue2Metric(Map<Long, Metric> unitId2Metric, CellElement cellElementTemplate,
                                      Map<String, Map<String, MyDimMemberEntity>> dim2MemberCode2MemberEntity, List<Map> cellList) {
        Map<String, Boolean> memberId2IsNumericMap = dimMemberApplicationService.getMemberId2IsNumericMap();
        for (Map cell : cellList) {
            Long unitId = Long.valueOf(cell.get("row_id").toString());
            Metric metric = unitId2Metric.get(unitId);
            if (metric == null) {
                logger.warn("miss metric in unitId:{}", unitId);
                continue;
            }
            CellTypeEnum cellTypeEnum = getCellTypeEnum(cellElementTemplate, dim2MemberCode2MemberEntity, cell,
                    memberId2IsNumericMap);
            if (CellTypeEnum.ATM.equals(cellTypeEnum)) {
                metric.setMetricDataType(MetricDataType.NUMERIC);
                metric.setValue(cell.get("amount"));
            } else {
                metric.setMetricDataType(MetricDataType.CHARACTER);
                metric.setValue(cell.get("origin_expression"));
            }
        }
    }

    private CellTypeEnum getCellTypeEnum(CellElement cellElementTemplate,
                                         Map<String, Map<String, MyDimMemberEntity>> dim2MemberCode2MemberEntity, Map cell,
                                         Map<String, Boolean> memberId2IsNumericMap) {
        CellElement cellElement = new CellElement();
        cellDimensionService.assembleDimension2CellElement(cell, cellElement, cellElementTemplate);
        Set<String> memberIds = new HashSet<>();
        for (CellDimension c : cellElement.getDimensions()) {
            String memberId = Optional.of(dim2MemberCode2MemberEntity).map(m -> m.get(c.getDimensionCode()))
                    .map(m -> m.get(c.getDimensionName())).map(MyDimMemberEntity::getMemberId)
                    .orElse(StringUtils.EMPTY);
            if (StringUtils.EMPTY.equals(memberId)) {
                // 大概率是修改了维度成员没有重新部署, calculation表中的维度成员没有改过来
                logger.error("获取维度成员ID失败, 默认为数字类型, dimCode:" + c.getDimensionCode() + ", dimensionName:"
                        + c.getDimensionName());
                return CellTypeEnum.ATM;
            }
            memberIds.add(memberId);
        }
        boolean isNumeric = true;
        for (String memberId : memberIds) {
            // 如果有一个为文本，则为文本
            isNumeric &= memberId2IsNumericMap.getOrDefault(memberId, true);
        }
        return isNumeric ? CellTypeEnum.ATM : CellTypeEnum.TEXT;
    }

    private Map<Long, FormDataQueryResponse.CellInfo> getCellInfoMapFromDbReference(String modelId,
                                                                                    CellElement cellElementTemplate, List<CellElement> allCellElements)
            throws CustomException, DataSourceException, ApplicationException {
        FormCellInfoRequest formDataRequest = buildFormDataRequest(cellElementTemplate, allCellElements);
        logger.info("prepare to load metric,request form data modelId:{},size:{}", modelId,
                formDataRequest.getFormDataList().size());
        BaseResponse<FormDataQueryResponse> response = queryMetricValueApplicationService
                .queryFormCellValues(formDataRequest);
        logger.info("load form data query response ,status:{}", response.getMessage());
        List<FormDataQueryResponse.CellInfo> cellInfos = response.getData().getFormDataList();
        logger.info("load metric from datasource.modelId:{},size:{}", modelId, cellInfos.size());
        Pattern pattern = Pattern.compile(Constants.KEY_REGEX);
        Map<Long, FormDataQueryResponse.CellInfo> cellInfoMap = new HashMap<>();
        cellInfos.forEach(c -> {
            Matcher matcher = pattern.matcher(c.getCellPosition());
            if (matcher.matches()) {
                cellInfoMap.put(Long.valueOf(matcher.group(1)), c);
            }
        });
        return cellInfoMap;
    }

    private FormCellInfoRequest buildFormDataRequest(CellElement cellElementTemplate,
                                                     List<CellElement> allCellElements) {
        DimModelResponse dimModelResponse = cellDimensionService.getDimModelResponse(cellElementTemplate.getModelId());
        Map<String, Map<String, MyDimMemberEntity>> dim2MemberCode2MemberEntity = toDimMembers(dimModelResponse);
        FormCellInfoRequest formDataRequest = new FormCellInfoRequest();
        formDataRequest.setModelId(cellElementTemplate.getModelId());
        formDataRequest.setFormPovList(Collections.emptyList());
        formDataRequest.setFormDataList(getFormDataByCellElements(allCellElements, dim2MemberCode2MemberEntity));
        return formDataRequest;
    }

    private List<FormCellInfoRequest.CellInfo> getFormDataByCellElements(List<CellElement> allCellElements,
                                                                         Map<String, Map<String, MyDimMemberEntity>> dimMembers) {
        List<FormCellInfoRequest.CellInfo> cellInfos = new ArrayList<>();

        for (CellElement cellElement : allCellElements) {
            FormCellInfoRequest.CellInfo cellInfo = new FormCellInfoRequest.CellInfo();
            cellInfo.setCellPosition(String.format(Constants.KEY_FORMAT, cellElement.getId()));
            try {
                cellInfo.setDimMemberList(cellElement.getDimensions().stream().map(cellDimension -> {
                    DimInstance dimInstance = new DimInstance();
                    String dimensionCode = cellDimension.getDimensionCode();
                    MyDimMemberEntity myDimMemberEntity = dimMembers.get(dimensionCode)
                            .get(cellDimension.getDimensionName());
                    if (myDimMemberEntity == null) {
                        logger.warn("cellId:{},{}dim name:{} code:{} not match when load datasource metric",
                                cellElement.getId(), cellDimension.getDimensionName(),
                                cellDimension.getDimensionCode());
                        throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_DIM_NAME_NOT_MATCH);
                    }
                    dimInstance.setDimId(myDimMemberEntity.getDimId());
                    dimInstance.setMemberId(myDimMemberEntity.getMemberId());
                    dimInstance.setMember(myDimMemberEntity.getMemberCode());
                    return dimInstance;
                }).collect(Collectors.toList()));
                setCellTypeEnum(cellElement.getModelId(), cellInfo);
            } catch (CalculationException e) {
                continue;
            }
            cellInfos.add(cellInfo);
        }
        return cellInfos;
    }

    private void setCellTypeEnum(String modelId, FormCellInfoRequest.CellInfo cellInfo) {
        // TODO 查询全量map，再set
        cellInfo.setCellTypeEnum(cellDimensionService.getCellTypeEnum(modelId, cellInfo));
    }

    private Map<String, Map<String, MyDimMemberEntity>> toDimMembers(DimModelResponse dimModelResponse) {
        Map<String, Map<String, MyDimMemberEntity>> dim2MemberCode2MemberEntity = new HashMap<>();
        List<MyDimensionResponse> dimensionResponses = dimModelResponse.getDimensionList();
        for (MyDimensionResponse myDimensionResponse : dimensionResponses) {
            String dimCode = myDimensionResponse.getDimCode();
            List<MyDimMemberEntity> memberList = myDimensionResponse.getMemberList();
            Map<String, MyDimMemberEntity> memberCode2Entity = new HashMap<>();
            for (MyDimMemberEntity myDimMemberEntity : memberList) {
                if (memberCode2Entity.containsKey(myDimMemberEntity.getMemberCode())) {
                    logger.debug("cover same member code:{} in dimension:{}", myDimMemberEntity.getMemberCode(),
                            dimCode);
                }
                // 取memberCode, name会重复, memberCode重复是代表共享变量
                memberCode2Entity.put(myDimMemberEntity.getMemberCode(), myDimMemberEntity);
            }
            dim2MemberCode2MemberEntity.put(dimCode, memberCode2Entity);
        }
        return dim2MemberCode2MemberEntity;
    }

    private Map<Long, String> getUnitIdModelIdMappings(Long unitId) {
        List<CellElement> globalCellElements = getModelAllCellElementsByUnitId(unitId);
        if (globalCellElements.isEmpty()) {
            return Collections.emptyMap();
        }
        return globalCellElements.stream().collect(Collectors.toMap(e -> e.getCellElementId(), e -> e.getModelId()));
    }

    private List<CellElement> getModelAllCellElementsByUnitId(Long unitId) {
        List<CellElement> globalCellElements = getCellElementsByUnitId(unitId);
        if (globalCellElements.isEmpty()) {
            return globalCellElements;
        }
        CellElement oneCellElement = globalCellElements.get(0);
        String modelId = oneCellElement.getModelId();
        if (StringUtils.isBlank(modelId)) {
            return Collections.emptyList();
        }
        CellElement paramCellElement = new CellElement();
        paramCellElement.setModelId(modelId);
        return cellDimensionService.findSimpleCellElements(paramCellElement);
    }

    private List<CellElement> getCellElementsByUnitId(Long unitId) {
        CellElement cellElement = new CellElement();
        cellElement.setCellElementId(unitId);
        return cellDimensionService.findSimpleCellElements(cellElement);
    }

    private void copyMetric(Metric source, Metric target) {
        target.setMetricGenerateStrategy(source.getMetricGenerateStrategy());
        target.setGroupId(source.getGroupId());
        target.setVersionId(source.getVersionId());
        target.setMetricDataType(source.getMetricDataType());
        target.setValue(source.getValue());
    }

    @Override
    public void saveMetrics(List<Metric> metrics) {
        throw new UnsupportedOperationException("MetricWrapService saveMetrics not supported");
    }

    @Override
    public Map<String, Metric> getMetrics(List<AxisModel> referenceAxisModels, List<Metric> metrics) {
        throw new UnsupportedOperationException("MetricWrapService getMetrics not supported");
    }
}
