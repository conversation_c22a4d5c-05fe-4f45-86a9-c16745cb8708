/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.workflow.deploy.impl;

import com.huawei.it.finance.opt.tech.util.DictConfigUtil;
import com.huawei.it.finance.opt.workflow.Constants;
import com.huawei.it.finance.opt.workflow.deploy.DeployCellActivity;
import com.huawei.it.finance.opt.workflow.deploy.DslDeployWorkflow;
import com.huawei.it.finance.opt.workflow.deploy.dto.DeployCellParam;
import com.huawei.it.finance.opt.workflow.deploy.dto.WorkflowParam;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.RULE_WORKFLOW_DEPLOY_SWITCH;

/**
 * DSL部署工作流实现类
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
@Slf4j
@WorkflowImpl(taskQueues = Constants.PARALLEL_DEPLOY_QUEUE_NAME)
public class DslDeployWorkflowImpl implements DslDeployWorkflow {
    
    private final DeployCellActivity deployCellActivity;

    // TODO： 202504 测试用，待删除。
    private static final String DEPLOY_SWITCH = "*";
    private static final String DEPLOY_SWITCH_DEFAULT_VALUE = "*";

    public DslDeployWorkflowImpl() {
        ActivityOptions activityOptions = ActivityOptions.newBuilder()
                .setStartToCloseTimeout(Duration.ofSeconds(Constants.ACTIVITY_TIMEOUT_SECONDS))
                .setRetryOptions(RetryOptions.newBuilder()
                        .setMaximumAttempts(Constants.MAX_RETRY_ATTEMPTS)
                        .build())
                .build();

        this.deployCellActivity = Workflow.newActivityStub(DeployCellActivity.class, activityOptions);
    }

    @Override
    public int deploy(WorkflowParam workflowParam) {
        int taskCount = workflowParam.getTotalCellCount() / workflowParam.getBatchSize() + 1;
        try {
            /*一、规则部署*/
            /*这里已经做了分批，一批10万*/
            List<Promise<String>> allTasks = new ArrayList<>();
            for (int i = 0; i < taskCount; i++) {
                DeployCellParam param = buildActivityParam(workflowParam, i);
                Promise<String> promise = Async.function(() -> {
                    deployCellActivity.deployCellExecute(param);
                    return "";
                });
                allTasks.add(promise);
            }
            Promise.allOf(allTasks).get();

            /*二、 保存部署结果(rule+增量unit)*/
            Promise<Void> saveResultPromise = Async.procedure(
                    deployCellActivity::deployResultSaveExecute,
                    workflowParam
            );

            // 等待保存任务完成
            saveResultPromise.get();

            log.info("Workflow completed successfully for script: {}", workflowParam.getScriptId());
            return 0;
        } catch (Exception e) {
            log.error("Workflow failed for script: {}", workflowParam.getScriptId(), e);
            return -1;
        }
    }

    // TODO:这里增加一个开关默认值
    public static boolean isWorkflowDeploy(long scriptId) {
        String workflowDeployScriptIds = DictConfigUtil.getProperty(RULE_WORKFLOW_DEPLOY_SWITCH, DEPLOY_SWITCH_DEFAULT_VALUE);
        if (DEPLOY_SWITCH.equals(workflowDeployScriptIds)) {
            return true;
        }
        String scriptIdStr = String.valueOf(scriptId);
        return Arrays.stream(workflowDeployScriptIds.split(","))
                .map(String::trim)
                .anyMatch(s -> s.equals(scriptIdStr));
    }

    public static DeployCellParam buildActivityParam(WorkflowParam workflowParam, int i) {
        int start = i * workflowParam.getBatchSize();
        int end = (i + 1) * workflowParam.getBatchSize();
        return new DeployCellParam(workflowParam, start, end, i);
    }
}
