/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.wrap.service;

import com.huawei.it.common.exception.ApplicationException;
import com.huawei.it.finance.opt.base.entity.Metric;
import com.huawei.it.finance.opt.base.exception.CalculationException;
import com.huawei.it.finance.opt.base.infrastructure.db.repository.impl.BatchInsertRepo;
import com.huawei.it.finance.opt.dsl.complier.area.DimSingleMemberArea;
import com.huawei.it.finance.opt.dsl.complier.dimmodel.DimMemberEntity;
import com.huawei.it.finance.opt.fsp.pub.application.constant.SourceTableConstant;
import com.huawei.it.finance.opt.fsp.pub.application.request.FormCellInfoRequest;
import com.huawei.it.finance.opt.fsp.pub.common.exception.DataSourceException;
import com.huawei.it.finance.opt.fsp.pub.domain.entity.DimInstance;
import com.huawei.it.finance.opt.fsp.pub.domain.entity.SourceTableEntity;
import com.huawei.it.finance.opt.fsp.pub.domain.entity.TranDimToFieldEntity;
import com.huawei.it.finance.opt.fsp.pub.domain.service.ISourceTableDomainService;
import com.huawei.it.finance.opt.fsp.pub.domain.service.ITableModelReferDomainService;
import com.huawei.it.finance.opt.fsp.pub.infrastructure.dataobject.DimFieldMapperDO;
import com.huawei.it.finance.opt.task.entity.QuartzTaskInfo;
import com.huawei.it.finance.opt.task.infrastructure.db.repository.IQuartzTaskInfoRepo;
import com.huawei.it.finance.opt.tech.common.constant.DynamicDataSourceConstant;
import com.huawei.it.finance.opt.tech.enums.CellTypeEnum;
import com.huawei.it.finance.opt.tech.event.NewCellIdEvent;
import com.huawei.it.finance.opt.tech.exception.PubErrorCode;
import com.huawei.it.finance.opt.tech.redisson.IRedissonService;
import com.huawei.it.finance.opt.tech.util.DataSourceUtil;
import com.huawei.it.finance.opt.tech.util.DictConfigUtil;
import com.huawei.it.finance.opt.tech.util.FutureUtil;
import com.huawei.it.finance.opt.tech.util.JsonUtils;
import com.huawei.it.finance.opt.tech.util.ValidateInjectionRisksUtil;
import com.huawei.it.finance.opt.wrap.dao.CellDimensionDao;
import com.huawei.it.finance.opt.wrap.dao.CellElementQueryCondition;
import com.huawei.it.finance.opt.wrap.entity.CellDimension;
import com.huawei.it.finance.opt.wrap.entity.CellElement;
import com.huawei.it.finance.pub.dimmodel.application.dto.response.DimModelResponse;
import com.huawei.it.finance.pub.dimmodel.application.dto.response.MyDimensionResponse;
import com.huawei.it.finance.pub.dimmodel.application.service.IDimModelApplicationService;
import com.huawei.it.finance.pub.dimmodel.application.service.IMyDimMemberApplicationService;
import com.huawei.it.finance.pub.dimmodel.common.advice.TrackExecutionTime;
import com.huawei.it.finance.pub.dimmodel.common.exception.CustomException;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.FALSE;
import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.MANUAL;
import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.RULE_DEPLOY_CLEAR_DIRTY_GLOBAL_CELL;
import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.RULE_DEPLOY_IGNORE_FILTER_SHORT_NAME;
import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.RULE_DEPLOY_NEW_MODELS;
import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.SQL_BUILD_POLICY;
import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.TRUE;


@Service
public class CellDimensionService {

    private static final Logger logger = LoggerFactory.getLogger(CellDimensionService.class);

    private static final String CALCULATION_TABLE_SUFFIX = "_calculation";

    private static final String CALCULATION_WORKSPACE_SUFFIX = "workspace_id:";

    private static final String DIM_FILED_CACHE_KEY_SEPARATOR = "@@";

    private static final Integer BATCH_SIZE = 1000;

    private static final Integer BIG_BATCH_SIZE = 5000;

    private static final Integer MAX_ITERATION_TIMES = 100000;

    private static final String MODEL_DETAIL_KEY_PREFIX = "FSPPUB_dim_model_detail_"; // 模型详情缓存前缀

    private static final String DIM_FIELD_MAPPER_KEY_PREFIX = "FSPPUB_dim_field_mapper_"; // 字段映射缓存前缀

    private static final String SPLIT = "_";

    @Value("${new-deploy-models:}")
    private String newDeployModels;

    @Value("${redisson.ttl.seconds:300}")
    private int ttlSeconds;

    @Value("${usf.service.environment:sit}")
    private String usfServiceEnvironment;

    @Autowired
    private CellDimensionDao cellDimensionDao;

    @Autowired
    private IDimModelApplicationService iDimModelApplicationService;

    @Autowired
    private ISourceTableDomainService iSourceTableDomainService;

    @Autowired
    private ITableModelReferDomainService iTableModelReferDomainService;

    @Autowired
    private TranDimToFieldEntity tranDimToFieldEntity;

    @Autowired
    private IMyDimMemberApplicationService myDimMemberApplicationService;

    @Autowired
    private BatchInsertRepo batchInsertRepo;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private IQuartzTaskInfoRepo iQuartzTaskInfoRepo;

    @Autowired
    private IRedissonService redissonClient;

    @Value("${calculate.metric-load.size:60000}")
    private int maxQueryParamSize;

    @TrackExecutionTime
    public DimModelResponse getDimModelResponseFromCache(String modelId) {
        try {
            final String redisKey = this.getCacheKeyEnv(MODEL_DETAIL_KEY_PREFIX) + modelId;
            RBucket<DimModelResponse> bucket = redissonClient.getBucket(redisKey);
            logger.info("CellDimensionService.getDimModelResponseFromCache : Fetch dim model detail from redis cache, DimModelResponse size is {}", bucket.size());
            // 双重检查锁避免缓存击穿
            if (!bucket.isExists()) {
                synchronized (this) {
                    if (!bucket.isExists()) {
                        DimModelResponse value = loadDimModelDetail(modelId);
                        bucket.set(value, ttlSeconds, TimeUnit.SECONDS);
                        logger.info("CellDimensionService.getDimModelResponseFromCache : Load dim model detail to the Redis, DimModelResponse size is {}", bucket.size());
                    }
                }
            }
            return bucket.get();
        } catch (Exception e) {
            logger.error("CellDimensionService.getDimModelResponseFromCache : Load dim model detail to the Redis Failed", e);
            throw new RuntimeException("CellDimensionService.getDimModelResponseFromCache : Load dim model detail to the Redis Failed, " +  e.getMessage());
        }
    }

    private DimModelResponse loadDimModelDetail(String modelId) {
        try {
            return iDimModelApplicationService.findDimModelDetail(modelId, false);
        } catch (CustomException e) {
            logger.error("Get dim model detail failed. Cause: {}", e.getMessage(), e);
            throw e;
        }
    }

    @TrackExecutionTime
    public Map<String, DimFieldMapperDO> getDimFieldMapperFromCache(String modelId, Long dataSourceId) {
        try {
            final String compositeKey = buildCompositeKey(modelId, dataSourceId);
            final String redisKey = this.getCacheKeyEnv(DIM_FIELD_MAPPER_KEY_PREFIX) + compositeKey;
            RMap<String, DimFieldMapperDO> rMap = redissonClient.getMap(redisKey);
            logger.info("CellDimensionService.getDimFieldMapperFromCache : Fetch dim field mapper from redis cache, DimFieldMapper size is {}", rMap.size());
            // 检查整个 Hash 是否存在（避免部分字段存在误判）
            if (!rMap.isExists()) {
                synchronized (this) {
                    if (!rMap.isExists()) {
                        Map<String, DimFieldMapperDO> value = loadDimFieldMappers(modelId, dataSourceId);
                        rMap.putAll(value);
                        rMap.expire(ttlSeconds, TimeUnit.SECONDS);
                        logger.info("CellDimensionService.getDimFieldMapperFromCache : Load dim field mapper to the Redis, DimFieldMapper size is {}", rMap.size());
                        return value;
                    }
                }
            }
            return rMap.getAll(rMap.keySet());
        } catch (Exception e) {
            logger.error("CellDimensionService.getDimFieldMapperFromCache : Load dim field mapper to the Redis Failed", e);
            throw new RuntimeException("CellDimensionService.getDimFieldMapperFromCache : Load dim field mapper to the Redis Failed, " +  e.getMessage());
        }
    }

    private Map<String, DimFieldMapperDO> loadDimFieldMappers(String modelId, Long dataSourceId) {
        return tranDimToFieldEntity.getDimFieldMappers(modelId, dataSourceId);
    }

    //======================= 工具方法 =======================
    private String buildCompositeKey(String modelId, Long dataSourceId) {
        return modelId + DIM_FILED_CACHE_KEY_SEPARATOR + dataSourceId;
    }

    private String getCacheKeyEnv(String cacheKeyPrefix) {
        String dynamicDBAndWorkSpaceId = null;
        try {
            dynamicDBAndWorkSpaceId = DataSourceUtil.getDynamicDBAndWorkSpaceId();
        } catch (ApplicationException e) {
            logger.error("CellDimensionService : Can not find dynamicDBAndWorkSpaceId from requestContext", e);
        }
        return cacheKeyPrefix + dynamicDBAndWorkSpaceId + SPLIT + usfServiceEnvironment + SPLIT;
    }

    public Map<Long, Long> fillCellIds(Set<DimSingleMemberArea> dimSingleMemberAreas, long workspaceId, QuartzTaskInfo quartzTaskInfo) {
        Map<Long, Long> allDuplicatedCellIdMap = new HashMap<>();
        int size = dimSingleMemberAreas.size();
        logger.info("prepare to fill the cellId of dsl dim member areas,size:{}", size);
        Map<String, List<DimSingleMemberArea>> modelDimSingleMemberAreas = new HashMap<>();
        // 按modelId分片处理
        dimSingleMemberAreas.forEach(d -> {
            String modelId = d.getModelId();
            if (!modelDimSingleMemberAreas.containsKey(modelId)) {
                modelDimSingleMemberAreas.put(modelId, new ArrayList<>());
            }
            modelDimSingleMemberAreas.get(modelId).add(d);
        });
        // 根据拼接的key(通过”字段:维度“拼接)，先过滤已存在的cell信息。
        modelDimSingleMemberAreas.forEach((key, subDimSingleMemberAreas) -> {
            logger.info("sub dim member fragment areas before filter exists,modelId:{},size:{}", key,
                    subDimSingleMemberAreas.size());
            String modelId = subDimSingleMemberAreas.get(0).getModelId();
            CellElement cellElementTemplate = buildCellElementTemplate(modelId);

            // 存在 global_cell_t 数据和 calculation表数据不一致的情况, 需要清理global_cell_t多余数据
            String clearDirtyGlobalCell = DictConfigUtil.getProperty(RULE_DEPLOY_CLEAR_DIRTY_GLOBAL_CELL, FALSE);
            if ("true".equals(clearDirtyGlobalCell)) {
                clearDirtyCell(modelId, cellElementTemplate.getModelTableName());
            }
            subDimSingleMemberAreas = batchFillExistedCellIds(subDimSingleMemberAreas, cellElementTemplate);
            subDimSingleMemberAreas = filterNewMemberAreas(subDimSingleMemberAreas);
            int subSize = subDimSingleMemberAreas.size();
            logger.info("sub dim member fragment areas after filter exists,modelId:{},size:{}", key, subSize);
            List<Long> allGeneratedCellIds = cellDimensionDao.generateNextCellElementIds(subSize);
            logger.info("query sequence list for global cells ,size:{}", subSize);
            List<CellElement> allCellElements = new ArrayList<>(subSize);
            List<Callable<List<CellElement>>> callableCellElements = new ArrayList<>();
            int count = subSize / BATCH_SIZE;
            for (int i = 0; i <= count; i++) {
                int start = i * BATCH_SIZE;
                int end = Math.min(subSize, (i + 1) * BATCH_SIZE);
                List<DimSingleMemberArea> subDimSingleMemberAreaFragments = subDimSingleMemberAreas.subList(start, end);
                List<Long> generatedCellIds = allGeneratedCellIds.subList(start, end);
                Callable callable = () -> batchFillModelDimSingleMemberAreaCellIds(subDimSingleMemberAreaFragments, cellElementTemplate,
                        workspaceId, generatedCellIds);
                callableCellElements.add(callable);
                logger.info("fill the cellId of dsl sub dim member fragment areas,modelId:{},start:{},end:{}", modelId,
                        start, end);
            }
            // 针对DSL部署流程添加心跳功能，在较大量级的单元格入库的场景下，隔一段时间刷一下当前部署任务的心跳时间，为任务扫描器提供数据支撑
            iQuartzTaskInfoRepo.heartBeat(quartzTaskInfo);
            List<List<CellElement>> callableCellElementsList = FutureUtil.submitCallableAndWaitForCompletion(callableCellElements);
            allCellElements.addAll(callableCellElementsList.stream().flatMap(Collection::stream).collect(Collectors.toList()));
            allDuplicatedCellIdMap.putAll(fillDimSingleMemberAreasIds(subDimSingleMemberAreas, allCellElements, cellElementTemplate));
            logger.info("fill the cellId of dsl sub dim member areas successfully,modelId:{},size:{}", modelId,
                    subSize);
        });
        logger.info("fill the cellId of dsl dim member areas successfully,size:{}", size);
        return allDuplicatedCellIdMap;
    }

    public Map<Long, Long> fillCellIdsV2(Set<DimSingleMemberArea> dimSingleMemberAreas, long workspaceId, QuartzTaskInfo quartzTaskInfo) {
        Map<Long, Long> allDuplicatedCellIdMap = new HashMap<>();
        int size = dimSingleMemberAreas.size();
        logger.info("prepare to fill the cellId of dsl dim member areas,size:{}", size);
        Map<String, List<DimSingleMemberArea>> modelDimSingleMemberAreas = new HashMap<>();
        // 按modelId分片处理
        dimSingleMemberAreas.forEach(d -> {
            String modelId = d.getModelId();
            if (!modelDimSingleMemberAreas.containsKey(modelId)) {
                modelDimSingleMemberAreas.put(modelId, new ArrayList<>());
            }
            modelDimSingleMemberAreas.get(modelId).add(d);
        });
        // 根据拼接的key(通过”字段:维度“拼接)，先过滤已存在的cell信息。
        modelDimSingleMemberAreas.forEach((key, subDimSingleMemberAreas) -> {
            logger.info("sub dim member fragment areas before filter exists,modelId:{},size:{}", key,
                    subDimSingleMemberAreas.size());
            String modelId = subDimSingleMemberAreas.get(0).getModelId();
            CellElement cellElementTemplate = buildCellElementTemplate(modelId);
            // 将subDimSingleMemberAreas转换为cellElement
            List<CellElement> allCellElements = new ArrayList<>(subDimSingleMemberAreas.size());
            for (int i = 0; i < subDimSingleMemberAreas.size(); i++) {
                DimSingleMemberArea memberArea = subDimSingleMemberAreas.get(i);
                CellElement cellElement = buildCellElement(memberArea, cellElementTemplate, workspaceId);
                cellElement.setRowId((long) i);
                allCellElements.add(cellElement);
            }

            // 先建临时表，临时表与业务数据表比较，存在的单元格赋值，不存在的单元格过滤出来按初始化的row_id排序
            List<CellElement> newCells = batchInsertRepo.filterExistingCellsAndFillRowId(allCellElements,
                    subDimSingleMemberAreas);
            logger.info("sub dim member fragment areas after filter exists,modelId:{},size:{}", key, newCells.size());
            subDimSingleMemberAreas = filterNewMemberAreas(subDimSingleMemberAreas, newCells);
            // newCells的rowId是按subDimSingleMemberAreas的index构建，在batchSaveCellElements后替换为DB生成的ID
            batchSaveCellElements(newCells);
            sendNewCellEvent(workspaceId, quartzTaskInfo, subDimSingleMemberAreas, modelId);
            allDuplicatedCellIdMap.putAll(fillDimSingleMemberAreasIds(subDimSingleMemberAreas, newCells, cellElementTemplate));
            logger.info("fill the cellId of dsl sub dim member areas successfully,modelId:{},size:{}", modelId,
                    subDimSingleMemberAreas.size());
            // 针对DSL部署流程添加心跳功能，在较大量级的单元格入库的场景下，隔一段时间刷一下当前部署任务的心跳时间，为任务扫描器提供数据支撑
            iQuartzTaskInfoRepo.heartBeat(quartzTaskInfo);
        });
        logger.info("fill the cellId of dsl dim member areas successfully,size:{}", size);
        return allDuplicatedCellIdMap;
    }

    public void fillCellIdsV3(Set<DimSingleMemberArea> dimSingleMemberAreas, long workspaceId,
                              QuartzTaskInfo quartzTaskInfo, int workFlowId, int activityId) {
        int size = dimSingleMemberAreas.size();
        logger.info("prepare to fill the cellId of dsl dim member areas,size:{}", size);
        // 按modelId分片处理
        Map<String, List<DimSingleMemberArea>> modelDimSingleMemberAreas = getDimSingleMemberAreasByModel(dimSingleMemberAreas);
        // 根据拼接的key(通过”字段:维度“拼接)，先过滤已存在的cell信息。
        modelDimSingleMemberAreas.forEach((key, subDimSingleMemberAreas) -> {
            logger.info("sub dim member fragment areas before filter exists,modelId:{},size:{}", key,
                    subDimSingleMemberAreas.size());
            String modelId = subDimSingleMemberAreas.get(0).getModelId();
            CellElement cellElementTemplate = buildCellElementTemplate(modelId);

            List<CellElement> allCellElements = new ArrayList<>(subDimSingleMemberAreas.size());
            for (int i = 0; i < subDimSingleMemberAreas.size(); i++) {
                DimSingleMemberArea memberArea = subDimSingleMemberAreas.get(i);
                CellElement cellElement = buildCellElement(memberArea, cellElementTemplate, workspaceId);
                cellElement.setRowId((long) i);
                allCellElements.add(cellElement);
            }
            // TODO： 20250430 新增对workflow的temp表的判断逻辑，判断单元格是否存在
            List<CellElement> newCells = batchInsertRepo.filterExistingCellsAndFillRowIdInWorkflow(allCellElements,
                    subDimSingleMemberAreas, workFlowId, activityId);

            // 针对DSL部署流程添加心跳功能，在较大量级的单元格入库的场景下，隔一段时间刷一下当前部署任务的心跳时间，为任务扫描器提供数据支撑
            iQuartzTaskInfoRepo.heartBeat(quartzTaskInfo);
        });
        logger.info("fill the cellId of dsl dim member areas successfully,size:{}", size);
    }

    private static @NotNull Map<String, List<DimSingleMemberArea>> getDimSingleMemberAreasByModel(Set<DimSingleMemberArea> dimSingleMemberAreas) {
        Map<String, List<DimSingleMemberArea>> modelDimSingleMemberAreas = new HashMap<>();
        dimSingleMemberAreas.forEach(d -> {
            String modelId = d.getModelId();
            if (!modelDimSingleMemberAreas.containsKey(modelId)) {
                modelDimSingleMemberAreas.put(modelId, new ArrayList<>());
            }
            modelDimSingleMemberAreas.get(modelId).add(d);
        });
        return modelDimSingleMemberAreas;
    }


    private void sendNewCellEvent(long workspaceId, QuartzTaskInfo quartzTaskInfo, List<DimSingleMemberArea> newCells, String modelId) {
        if (newCells.isEmpty()) {
            return;
        }

        Map<String, Set<String>> dimCode2MemberCodes = newCells.stream()
                .flatMap(dimSingleMemberArea -> dimSingleMemberArea.getDimMemberMap().values().stream())
                .collect(Collectors.groupingBy(
                        DimMemberEntity::getDimCode,
                        Collectors.mapping(DimMemberEntity::getMemberCode, Collectors.toSet())
                ));
        // 按照<维度code, List<成员code>>会让触发过期动作的表单变多不精确, 出于内存考虑, 仍采用此结构进行消息通知
        IRequestContext requestContext = RequestContext.getCurrent(true);
        //TODO：20250424 这个没有获取到上下文对象，待修复
        if(requestContext == null){
            requestContext = new RequestContext();
            requestContext.setItem(DynamicDataSourceConstant.DYNAMIC_DB, MDC.get(DynamicDataSourceConstant.DYNAMIC_DB));
            requestContext.setItem(DynamicDataSourceConstant.CURRENT_WORKSPACE_ID, MDC.get(DynamicDataSourceConstant.CURRENT_WORKSPACE_ID));
            requestContext.setItem(DynamicDataSourceConstant.WEB_HEADER_VALUE, MDC.get(DynamicDataSourceConstant.WEB_HEADER_VALUE));

        }else if(requestContext.getItem(DynamicDataSourceConstant.DYNAMIC_DB) == null) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String dynamicDB = (String) request.getSession().getAttribute(DynamicDataSourceConstant.DYNAMIC_DB);
            requestContext.setItem(DynamicDataSourceConstant.DYNAMIC_DB, dynamicDB);
            requestContext.setItem(DynamicDataSourceConstant.CURRENT_WORKSPACE_ID, String.valueOf(workspaceId));
        }
        NewCellIdEvent newCellIdEvent = new NewCellIdEvent(quartzTaskInfo.getJobName(), modelId,
                dimCode2MemberCodes, workspaceId, requestContext);
        eventPublisher.publishEvent(newCellIdEvent);
    }

    // 过滤已存储的重复单元格，并删除
    private Map<Long, Long> fillDimSingleMemberAreasIds(List<DimSingleMemberArea> newMemberAreas,
            List<CellElement> newCellElements, CellElement cellElementTemplate) {
        List<Long> cellIds = newCellElements.stream().map(a->a.getCellElementId()).collect(Collectors.toList());
        Map<String, CellElement> uniqueCells = queryUniqueCellMapByCellIds(cellIds);
        Map<Long, Long> duplicatedCellIdMap = new HashMap<>();
        List<CellElement> duplicateCellElements = new ArrayList<>();
        for (int i = 0; i < newCellElements.size(); i++) {
            CellElement cellElement = newCellElements.get(i);
            String cellKey = cellElement.getCellElementShortName();
            Long currentUnitId = cellElement.getCellElementId();
            Long unitId;
            if (uniqueCells.containsKey(cellKey)
                    && !Objects.equals(currentUnitId, uniqueCells.get(cellKey).getCellElementId())) {
                unitId = uniqueCells.get(cellKey).getCellElementId();
                duplicateCellElements.add(cellElement);
                duplicatedCellIdMap.put(cellElement.getCellElementId(), unitId);
                logger.warn("duplicate cell on fill dsl DimSingleMemberAreas,modelId:{},id1:{} -> id2:{},key:{}",
                        cellElementTemplate.getModelId(), cellElement.getCellElementId(), unitId, cellKey);
            } else {
                unitId = cellElement.getCellElementId();
                uniqueCells.put(cellKey, cellElement);
            }
            newMemberAreas.get(i).setCellId(unitId);
        }
        batchDeleteCellElements(duplicateCellElements);
        logger.info("delete duplicate cell element,modelId:{},size:{}", cellElementTemplate.getModelId(),
                duplicateCellElements.size());
        return duplicatedCellIdMap;
    }

    private Map<String, CellElement> queryUniqueCellMapByCellIds(List<Long> cellIds) {
        if(cellIds.isEmpty()) {
            return new HashMap<>();
        }
        int cellIdsSize = cellIds.size();
        Map<String, CellElement> uniqueCellMap = new HashMap<>();
        List<Callable<List<CellElement>>> callableCellElements = new ArrayList<>();
        int count = cellIdsSize / BATCH_SIZE;
        for (int i = 0; i <= count; i++) {
            int start = i * BATCH_SIZE;
            int end = Math.min(cellIdsSize, (i + 1) * BATCH_SIZE);
            List<Long> subCellIds = cellIds.subList(start, end);
            Callable callable = () -> cellDimensionDao.findGlobalCellElementsById(subCellIds);
            callableCellElements.add(callable);
        }
        List<List<CellElement>> callableCellElementsList = FutureUtil.submitCallableAndWaitForCompletion(callableCellElements);
        List<CellElement> cellElementsInDb = callableCellElementsList.stream().flatMap(Collection::stream).collect(Collectors.toList());
        for (CellElement cellElement : cellElementsInDb) {
            uniqueCellMap.put(cellElement.getCellElementShortName(), cellElement);
        }
        return uniqueCellMap;
    }

    private List<DimSingleMemberArea> batchFillExistedCellIds(List<DimSingleMemberArea> subDimSingleMemberAreas,
                                                              CellElement cellElementTemplate) {
        logger.info("size of subDimSingleMemberAreas:{}", subDimSingleMemberAreas.size());
        Map<String, String> dimName2tableColumnName = cellElementTemplate.getDimensions()
                .stream()
                .collect(
                        Collectors.toMap(CellDimension::getDimensionName, CellDimension::getDimensionColumnName));
        Map<String, Set<String>> dimField2dimValues = new HashMap<>();

        // 构建查询条件的字段和值映射
        for (DimSingleMemberArea area : subDimSingleMemberAreas) {
            for (Map.Entry<String, DimMemberEntity> entry : area.getDimMemberMap().entrySet()) {
                String dimName = entry.getKey();
                DimMemberEntity dimMemberEntity = entry.getValue();
                String tableColumnName = dimName2tableColumnName.get(dimName);
                if (StringUtils.isEmpty(tableColumnName)) {
                    logger.error("fail to map name to column, modelId:{}", cellElementTemplate.getModelId());
                    throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_FORM_MAPPING_FAILED);
                }
                dimField2dimValues.computeIfAbsent(tableColumnName, it -> new HashSet<>())
                        .add(dimMemberEntity.getMemberCode());
            }
        }
        // 查库在当前这里去重数据如果稀疏的话可能会影响效率[done]
        // 构建多维度字符串与 DimSingleMemberArea 的映射
        Map<String, DimSingleMemberArea> multiDimString2SubDimSingleMemberArea = subDimSingleMemberAreas.stream()
                .collect(Collectors.toMap(area -> buildDimKeyWithDimSingleMemberArea(area, cellElementTemplate),
                        Function.identity()));
        CellElementQueryCondition queryCondition = CellElementQueryCondition.builder()
                .dimensionTableColumnTitles(new ArrayList<>(dimField2dimValues.keySet()))
                .modelTableName(cellElementTemplate.getModelTableName())
                .modelId(cellElementTemplate.getModelId())
                .workspaceId(cellElementTemplate.getWorkspaceId())
                .dimField2dimValues(dimField2dimValues)
                .limit(BIG_BATCH_SIZE)
                .build();

        int filledNums = 0;
        int filledElementsNum = 0;
        int currentOffset = 0;
        int taskBatchSize = 10;

        // 每批处理的任务数量，例如10个任务一批，每批可查50000条
        for (int batchStart = 0; batchStart < MAX_ITERATION_TIMES; batchStart += taskBatchSize) {
            List<Callable<List<Map>>> callableList = createCallableList(batchStart, taskBatchSize, currentOffset,
                    queryCondition);
            currentOffset += callableList.size() * BIG_BATCH_SIZE;

            // 执行任务并处理结果
            List<CompletableFuture<List<Map>>> completableFutures = FutureUtil.submit(callableList);
            ResultHolder resultHolder = processBatchResults(completableFutures, filledNums, filledElementsNum,
                    multiDimString2SubDimSingleMemberArea, cellElementTemplate);
            filledNums = resultHolder.getFilledNums();
            filledElementsNum = resultHolder.getFilledElementsNum();

            if (filledNums == multiDimString2SubDimSingleMemberArea.size()) {
                logger.warn("fill all area cellId, breakout..");
                break;
            }
            if (resultHolder.isFinished()) {
                break;
            }
        }

        return new ArrayList<>(multiDimString2SubDimSingleMemberArea.values());
    }

    // 创建批处理任务列表
    private List<Callable<List<Map>>> createCallableList(int batchStart, int batchSize, int currentOffset,
        CellElementQueryCondition queryCondition) {
        List<Callable<List<Map>>> callableList = new ArrayList<>();
        for (int i = 0; i < batchSize && batchStart + i < MAX_ITERATION_TIMES; i++) {
            final int offset = currentOffset;
            // 校验表名，字段名
            ValidateInjectionRisksUtil.validateInjectionRisks(null, queryCondition.getModelTableName(), queryCondition.getDimensionTableColumnTitles());
            Callable<List<Map>> callable = () -> cellDimensionDao.findCellElementsByCondition(queryCondition, offset);
            callableList.add(callable);
            currentOffset += BIG_BATCH_SIZE;
        }

        return callableList;
    }

    private ResultHolder processBatchResults(List<CompletableFuture<List<Map>>> completableFutures, int filledNums,
                                             int filledElementsNum,
                                             Map<String, DimSingleMemberArea> multiDimString2SubDimSingleMemberArea,
                                             CellElement cellElementTemplate) {

        for (CompletableFuture<List<Map>> future : completableFutures) {
            List<Map> existedCellElements = null;
            try {
                existedCellElements = future.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }

            if (existedCellElements == null || existedCellElements.isEmpty()) {
                completableFutures.forEach(d -> d.cancel(true));
                return new ResultHolder(filledNums, filledElementsNum, true);
            }

            filledElementsNum += existedCellElements.size();
            logger.info("size of filled cellElements:{}, filtering exists..", filledElementsNum);
            for (Map existsCellElement : existedCellElements) {
                String existedMultiDimString = buildDimKeyWithHistory(existsCellElement, cellElementTemplate,
                        cellElementTemplate.getWorkspaceId());
                if (multiDimString2SubDimSingleMemberArea.containsKey(existedMultiDimString)) {
                    // 兼容id为int4的情况
                    long id = Long.parseLong(String.valueOf(existsCellElement.get("id")));
                    multiDimString2SubDimSingleMemberArea.get(existedMultiDimString).setCellId(id);
                    filledNums++;
                }
            }

            existedCellElements.clear();
        }

        return new ResultHolder(filledNums, filledElementsNum);
    }

    public List<Long> transferKeyCode2UnitId(List<String> adjustKeyCodes, String modelId) {
        List<Long> resultUnitId = new ArrayList<>(adjustKeyCodes.size());
        DimModelResponse modelResponse = this.getDimModelResponse(modelId);
        List<MyDimensionResponse> dimensionList = modelResponse.getDimensionList();
        List<String> sortedDimensionCodes = dimensionList.stream()
                .sorted(Comparator.comparingInt(MyDimensionResponse::getDimSeqNum))
                .map(MyDimensionResponse::getDimCode)
                .collect(Collectors.toList());
        List<List<String>> sortedMemberCodesList = new ArrayList<>(adjustKeyCodes.size());
        for (String keyCode : adjustKeyCodes) {
            List<String> sortedMemberCodes = Arrays.asList(keyCode.split("#"));
            if (sortedMemberCodes.size() != sortedDimensionCodes.size()) {
                logger.error("keyCode:{}和模型{}的维度个数匹配不上", keyCode, modelId);
                continue;
            }
            sortedMemberCodesList.add(sortedMemberCodes);
        }
        CellElement cellElement = buildCellElementTemplate(modelId);
        Map<String, String> code2ColumnName = cellElement.getDimensions()
                .stream()
                .collect(Collectors.toMap(CellDimension::getDimensionCode, CellDimension::getDimensionColumnName));
        List<String> sortedColumnNames = sortedDimensionCodes.stream()
                .map(code2ColumnName::get)
                .collect(Collectors.toList());

        // TODO:待优化成不分批或者找更优的方式
        for (List<List<String>> sortedMemberCodesSlice : ListUtils.partition(sortedMemberCodesList, BATCH_SIZE)) {
            logger.info("通过keycode转换unitId, modelId:{}, size:{}", modelId, sortedMemberCodesSlice.size());
            // 校验表名
            ValidateInjectionRisksUtil.validateInjectionRisks(cellElement.getModelTableName());
            resultUnitId.addAll(cellDimensionDao.findUnitIdByDim2Members(cellElement, sortedColumnNames,
                    sortedMemberCodesSlice));
        }
        return resultUnitId;
    }

    @Getter
    @AllArgsConstructor
    private class ResultHolder {
        private int filledNums;
        private int filledElementsNum;
        private boolean finished;

        ResultHolder(int filledNums, int filledElementsNum) {
            this.filledNums = filledNums;
            this.filledElementsNum = filledElementsNum;
        }
    }

    private List<DimSingleMemberArea> filterNewMemberAreas(List<DimSingleMemberArea> subDimSingleMemberAreas) {
        return subDimSingleMemberAreas.stream()
                .filter(area -> area.getCellId() == DimSingleMemberArea.INIT_CELL_ID)
                .collect(Collectors.toList());
    }

    private static List<DimSingleMemberArea> filterNewMemberAreas(List<DimSingleMemberArea> subDimSingleMemberAreas,
                                                                  List<CellElement> newCells) {
        List<DimSingleMemberArea> result = new ArrayList<>();
        for (CellElement cellElement : newCells) {
            // cellElement.getRowId为subDimSingleMemberAreas的index
            int index = cellElement.getRowId().intValue();
            DimSingleMemberArea newDimSingleMemberArea = subDimSingleMemberAreas.get(index);
            result.add(newDimSingleMemberArea);
        }
        return result;
    }

    private String buildDimKeyWithDimSingleMemberArea(DimSingleMemberArea dimSingleMemberArea,
            CellElement cellElementTemplate) {
        Map<String, DimMemberEntity> dimMemberMap = dimSingleMemberArea.getDimMemberMap();
        String collect = cellElementTemplate.getDimensions()
                .stream()
                .map(c -> c.getDimensionColumnName() + ":" + (dimMemberMap.containsKey(c.getDimensionName())
                        ? dimMemberMap.get(c.getDimensionName()).getMemberCode()
                        : ""))
                .collect(Collectors.joining("&"));
        long workspaceId = DataSourceUtil.getWorkspaceId();
        return CALCULATION_WORKSPACE_SUFFIX + workspaceId + "&" + collect;
    }

    private String buildDimKeyWithHistory(Map record, CellElement cellElementTemplate, long workspaceId) {
        String collect = cellElementTemplate.getDimensions()
                .stream()
                .map(c -> c.getDimensionColumnName() + ":" + (record.containsKey(c.getDimensionColumnName())
                        ? record.get(c.getDimensionColumnName()).toString().trim()
                        : ""))
                .collect(Collectors.joining("&"));
        return CALCULATION_WORKSPACE_SUFFIX + workspaceId + "&" + collect;
    }

    private List<CellElement> batchFillModelDimSingleMemberAreaCellIds(
            List<DimSingleMemberArea> subDimSingleMemberAreas, CellElement cellElementTemplate,
            long workspaceId, List<Long> generatedCellIds) {
        List<CellElement> cellElements = subDimSingleMemberAreas.stream()
                .map(a -> buildCellElement(a, cellElementTemplate, workspaceId)).collect(Collectors.toList());
        batchSaveCellElements(cellElements, generatedCellIds);
        return cellElements;
    }

    public List<Map> batchQueryValueByMetrics(CellElement cellElementParam, List<Metric> metrics)
            throws DataSourceException {
        if (metrics.isEmpty()) {
            return Collections.emptyList();
        }
        Long dataSourceId = iTableModelReferDomainService.getDataSourceIdByModelId(cellElementParam.getModelId());
        SourceTableEntity sourceTableEntity = iSourceTableDomainService.getSourceTableById(dataSourceId);
        List<Long> toQueryUnitIds = metrics.stream().map(Metric::getUnitId).collect(Collectors.toList());
        List<Map> result = new ArrayList<>(metrics.size());
        // 所有cal表source表都需要加上索引, 优化查询, 但劣化插入
        String modelTableName = cellElementParam.getModelTableName().replace(CALCULATION_TABLE_SUFFIX, "");
        // 因为集成表不带作业空间id, 因此在设计集成表数据查询时不带上作业空间ID
        Long workspaceId = SourceTableConstant.INTEGRATE_TYPE == sourceTableEntity.getTableType()
                ? null
                : DataSourceUtil.getWorkspaceId();
        if (logger.isDebugEnabled()) {
            logger.debug("modelTableName:{}, tableType:{}, workspaceId:{}", modelTableName,
                    sourceTableEntity.getTableType(), workspaceId);
        }
        int maxQueryParamSize = 65000;
        for (List<Long> queryIds: ListUtils.partition(toQueryUnitIds, maxQueryParamSize)) {
            CellElementQueryCondition queryCondition = CellElementQueryCondition.builder()
                    .unitIds(queryIds)
                    // 删了_calculation
                    .modelTableName(modelTableName)
                    .dimensionTableColumnTitles(cellElementParam.getDimensions().stream()
                            .map(CellDimension::getDimensionColumnName).collect(Collectors.toList()))
                    .workspaceId(workspaceId)
                    .build();
            // 校验表名
            ValidateInjectionRisksUtil.validateInjectionRisks(queryCondition.getModelTableName());
            result.addAll(cellDimensionDao.batchQueryValueByMetric(queryCondition));
        }
        return result;
    }

    /**
     * 获取待计算的计算单元格(用带计算指标对象的unitId获取待计算单元格的多维值，准备用于数据源值查询)
     *
     * @param cellElementParam 计算单元格查询参数
     * @param metrics 计算指标列表
     * @return 装配好的计算单元格对象
     */
    public List<CellElement> getCellElementsWithinMetrics(CellElement cellElementParam, List<Metric> metrics) {
        Set<Long> metricUnitIds = new HashSet<>(metrics.size());
        Long minUnitId = Long.MAX_VALUE;
        Long maxUnitId = Long.MIN_VALUE;
        for (Metric metric : metrics) {
            minUnitId = metric.getUnitId() < minUnitId ? metric.getUnitId() : minUnitId;
            maxUnitId = metric.getUnitId() > maxUnitId ? metric.getUnitId() : maxUnitId;
            metricUnitIds.add(metric.getUnitId());
        }
        // unitId在表中是有序的，利用unitId作范围查询缩小结果数据集(加快查询速度&减少内存占用率)
        // TODO: 试验in查询和范围查询两种方式的查询数据量以及时间差距, 找出经验值
        CellElementQueryCondition queryCondition = CellElementQueryCondition.builder().minUnitId(minUnitId)
                .maxUnitId(maxUnitId).modelTableName(cellElementParam.getModelTableName())
                .dimensionTableColumnTitles(cellElementParam.getDimensions().stream()
                        .map(CellDimension::getDimensionColumnName).collect(Collectors.toList()))
                .workspaceId(DataSourceUtil.getWorkspaceId())
                .build();
        // 校验表名,字段名
        ValidateInjectionRisksUtil.validateInjectionRisks(null, queryCondition.getModelTableName(), queryCondition.getDimensionTableColumnTitles());
        List<Map> mapCellElements = cellDimensionDao.findCellElementsByCondition(queryCondition, queryCondition.getOffset());
        List<CellElement> cellElements = new ArrayList<>(metrics.size());
        for (Map mapCellElement : mapCellElements) {
            Long id = Long.valueOf(mapCellElement.get("id").toString());
            if (!metricUnitIds.contains(id)) {
                continue;
            }
            CellElement cellElement = new CellElement();
            cellElement.setId(id);
            cellElement.setModelId(cellElementParam.getModelId());
            cellElement.setModelCode(cellElementParam.getModelCode());
            cellElement.setVersionId(cellElementParam.getVersionId());
            cellElement.setModelTableName(cellElementParam.getModelTableName());
            assembleDimension2CellElement(mapCellElement, cellElement, cellElementParam);
            cellElements.add(cellElement);
        }
        return cellElements;
    }

    public List<CellElement> getCellElementsByUnitIds(List<Long> unitIds, String modelId) {
        List<CellElement> result = new ArrayList<>(unitIds.size());
        CellElement cellElementTemplate = buildCellElementTemplate(modelId);
        CellElementQueryCondition queryCondition = CellElementQueryCondition.builder()
                .modelTableName(cellElementTemplate.getModelTableName())
                .dimensionTableColumnTitles(cellElementTemplate.getDimensions().stream()
                        .map(CellDimension::getDimensionColumnName).collect(Collectors.toList()))
                .workspaceId(DataSourceUtil.getWorkspaceId())
                .build();
        for(List<Long> unitIdSlice: ListUtils.partition(unitIds, BIG_BATCH_SIZE)) {
            queryCondition.setUnitIds(unitIdSlice);
            // 校验表名,字段名
            ValidateInjectionRisksUtil.validateInjectionRisks(null, queryCondition.getModelTableName(), queryCondition.getDimensionTableColumnTitles());
            List<Map> mapCellElements = cellDimensionDao.findCellElementsByCondition(queryCondition,
                    queryCondition.getOffset());
            for (Map mapCellElement : mapCellElements) {
                Long id = Long.valueOf(mapCellElement.get("id").toString());
                CellElement cellElement = new CellElement();
                cellElement.setId(id);
                cellElement.setModelId(cellElementTemplate.getModelId());
                cellElement.setModelCode(cellElementTemplate.getModelCode());
                cellElement.setVersionId(cellElementTemplate.getVersionId());
                cellElement.setModelTableName(cellElementTemplate.getModelTableName());
                assembleDimension2CellElement(mapCellElement, cellElement, cellElementTemplate);
                result.add(cellElement);
            }
        }
        return result;
    }

    public void assembleDimension2CellElement(Map map, CellElement cellElement, CellElement cellElementTemplate) {
        // 提前判断维度组合，如果为空则报错提示
        if (CollectionUtil.isNullOrEmpty(cellElementTemplate.getDimensions())) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_FORM_MAPPING_FAILED);
        }
        // 基于动态宽表返回结果构建cell element
        for (CellDimension dimensionInTemplate : cellElementTemplate.getDimensions()) {
            CellDimension cellDimension = new CellDimension();
            String columnName = dimensionInTemplate.getDimensionColumnName();
            cellDimension.setDimensionColumnName(columnName);
            // dimensionName实际上是该维度对应的值..
            cellDimension.setDimensionName(map.containsKey(columnName) ? String.valueOf(map.get(columnName)) : null);
            cellDimension.setDimensionCode(dimensionInTemplate.getDimensionCode());
            cellElement.getDimensions().add(cellDimension);
        }
    }

    public CellElement buildCellElementTemplate(String modelId) {
        CellElement cellElement = new CellElement();
        cellElement.setModelId(modelId);
        cellElement.setCreationDate(new Timestamp(System.currentTimeMillis()));
        try {
            Map<String, DimFieldMapperDO> dimFieldMappers = getDimFieldMappersAndSetTableName(modelId, cellElement);
            DimModelResponse dimModelResponse = getDimModelResponse(modelId);
            for (MyDimensionResponse myDimensionResponse : dimModelResponse.getDimensionList()) {
                String dimId = myDimensionResponse.getDimId();
                if (!dimFieldMappers.containsKey(dimId)) {
                    logger.warn("dimId {} not found in dimTableFieldMapping", dimId);
                    continue;
                }
                CellDimension cellDimension = new CellDimension();
                cellDimension.setDimensionId(dimId);
                cellDimension.setDimensionName(myDimensionResponse.getDimName());
                cellDimension.setDimensionCode(myDimensionResponse.getDimCode());
                cellDimension.setDimensionColumnName(dimFieldMappers.get(dimId).getFieldName());
                cellElement.getDimensions().add(cellDimension);
            }
            logger.debug("build cell element template successfully.modelId:{}", modelId);
        } catch (CustomException | DataSourceException e) {
            logger.error("build cell element template failed.cause:{}", e.getMessage(), e);
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_FORM_MAPPING_FAILED);
        }
        return cellElement;
    }

    public DimModelResponse getDimModelResponse(String modelId) {
        DimModelResponse dimModelResponse;
        try {
            dimModelResponse = this.getDimModelResponseFromCache(modelId);
        } catch (Exception e) {
            logger.error("Load dim model detail to the Redis Failed, {}", e.getMessage(), e);
            dimModelResponse = this.loadDimModelDetail(modelId);
        }
        return dimModelResponse;
    }

    private Map<String, DimFieldMapperDO> getDimFieldMappersAndSetTableName(String modelId, CellElement cellElement)
            throws CustomException, DataSourceException {
        Long dataSourceId = iTableModelReferDomainService.getDataSourceIdByModelId(modelId);
        SourceTableEntity sourceTableEntity = iSourceTableDomainService.getSourceTableById(dataSourceId);
        cellElement.setModelTableName(sourceTableEntity.getPhyTableName() + CALCULATION_TABLE_SUFFIX);
        return getDimFieldMappers(modelId, dataSourceId);
    }

    private Map<String, DimFieldMapperDO> getDimFieldMappers(String modelId, Long dataSourceId) {
        Map<String, DimFieldMapperDO> dimFieldMapperDOMap;
        try {
            dimFieldMapperDOMap = this.getDimFieldMapperFromCache(modelId, dataSourceId);
        } catch (Exception e) {
            logger.error("Load dim model detail to the Redis Failed, {}", e.getMessage(), e);
            dimFieldMapperDOMap = this.loadDimFieldMappers(modelId, dataSourceId);
        }
        return dimFieldMapperDOMap;
    }

    private CellElement buildCellElement(DimSingleMemberArea dimSingleMemberArea, CellElement template,
            long workspaceId) {
        CellElement cellElement = new CellElement();
        cellElement.setWorkspaceId(workspaceId);
        cellElement.setCellElementName(dimSingleMemberArea.generateBusinessString());
        cellElement.setCellElementShortName(dimSingleMemberArea.generateCodeString());
        cellElement.setModelId(template.getModelId());
        cellElement.setCreationDate(template.getCreationDate());
        cellElement.setModelTableName(template.getModelTableName());
        Map<String, DimMemberEntity> dimMemberMap = dimSingleMemberArea.getDimMemberMap();
        if (dimMemberMap.size() == 0 || dimMemberMap.size() != template.getDimensions().size()) {
            logger.error(
                    "dim members from request not match the dim definition.request dim size:{},definition dim size:{}",
                    dimMemberMap.size(), template.getDimensions().size());
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_DIM_NAME_NOT_MATCH);
        }

        for (CellDimension cellDimensionInTemplate : template.getDimensions()) {
            String dimensionName = cellDimensionInTemplate.getDimensionName();
            String dimensionColumnName = cellDimensionInTemplate.getDimensionColumnName();
            if (!dimMemberMap.containsKey(dimensionName)) {
                logger.warn("model:{},dim:{} not found table field mapping", template.getModelId(), dimensionName);
                throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_DIM_NAME_NOT_MATCH);
            }
            CellDimension cellDimension = new CellDimension();
            cellDimension.setDimensionColumnName(dimensionColumnName);
            cellDimension.setDimensionCode(dimMemberMap.get(dimensionName).getMemberCode());
            cellElement.getDimensions().add(cellDimension);
        }
        return cellElement;
    }

    private void batchSaveCellElements(List<CellElement> cellElements, List<Long> generatedCellIds) {
        if (cellElements.isEmpty()) {
            return;
        }
        batchSetCellElementId(cellElements, generatedCellIds);
        saveGlobalCellsAndCalculationCells(cellElements);
    }

    private void batchSaveCellElements(List<CellElement> cellElements) {
        if (cellElements.isEmpty()) {
            return;
        }
        // FIXME: 异常了会导致rowId不连续, 不会回滚, 性能很差, 4500个生成需要3秒
        List<Long> generatedCellIds = new ArrayList<>(cellElements.size());
        for (List<CellElement> cellElementList : ListUtils.partition(cellElements, BIG_BATCH_SIZE)) {
            generatedCellIds.addAll(cellDimensionDao.generateNextCellElementId(cellElementList));
        }
        batchSetCellElementId(cellElements, generatedCellIds);
        saveGlobalCellsAndCalculationCells(cellElements);
    }

    private void batchSetCellElementId(List<CellElement> cellElements, List<Long> generatedCellIds) {
        if (cellElements.isEmpty()) {
            return;
        }
        int idx = 0;
        for (CellElement cellElement : cellElements) {
            Long cellId = generatedCellIds.get(idx);
            cellElement.setId(cellId);
            cellElement.setRowId(cellId);
            cellElement.setCellElementId(cellId);
            idx++;
        }
    }

    private void batchDeleteCellElements(List<CellElement> cellElements) {
        if (cellElements.isEmpty()) {
            return;
        }
        int size = cellElements.size();
        int count = size / BATCH_SIZE;
        for (int i = 0; i <= count; i++) {
            int start = i * BATCH_SIZE;
            int end = Math.min(size, (i + 1) * BATCH_SIZE);
            List<CellElement> subCellElements = cellElements.subList(start, end);
            // 校验表名
            ValidateInjectionRisksUtil.validateInjectionRisks(subCellElements.get(0).getModelTableName());
            cellDimensionDao.batchDeleteCellElementsById(subCellElements);
            cellDimensionDao.batchDeleteGlobalCellElementsById(subCellElements);
        }
    }

    public void fillOneModelCellElementIdByDimensions(List<CellElement> cellElements) {
        if (cellElements.isEmpty()) {
            return;
        }
        String modelId = cellElements.get(0).getModelId();
        try {
            CellElement param = this.buildCellElementTemplate(modelId);
            Map<String, DimFieldMapperDO> dimFieldMappers = getDimFieldMappersAndSetTableName(modelId, param);
            Map<String, Set<String>> dimField2dimValues = new HashMap<>();
            for (CellElement c : cellElements) {
                for (CellDimension dim : c.getDimensions()) {
                    String fieldName = Optional.of(dim).map(CellDimension::getDimensionId).map(dimFieldMappers::get)
                            .map(DimFieldMapperDO::getFieldName).orElse(StringUtils.EMPTY);
                    if (StringUtils.isBlank(fieldName)) {
                        continue;
                    }
                    dimField2dimValues.computeIfAbsent(fieldName, it -> new HashSet<>()).add(dim.getDimensionName());
                }
            }
            // 校验表名
            ValidateInjectionRisksUtil.validateInjectionRisks(param.getModelTableName());
            // 加载当前模型维度映射关系
            List<Map> results = cellDimensionDao.findSpecifyCellElements(param, dimField2dimValues);
            if (CollectionUtils.isEmpty(results)) {
                logger.warn("empty result when map dimension to unitId, param:{}. dimField2Value:{}",
                        JsonUtils.toJsonString(param), JsonUtils.toJsonString(dimField2dimValues));
            }
            // 基于维度映射关系构建 宽表维度值+列名 与 单元格Id的映射关系
            Map<String, Long> cellElementIds = new HashMap<>();
            for (Map record : results) {
                String key = buildKeyWithDimFieldMappers(record, dimFieldMappers);
                Long value = Long.valueOf(record.get("id") + "");
                cellElementIds.putIfAbsent(key, value);
            }
            // 根据请求中的单元格维度信息，为请求的单元格Id赋值
            for (CellElement c : cellElements) {
                String key = buildCellElementKeyWithDimFieldMappers(c, dimFieldMappers);
                if (cellElementIds.containsKey(key)) {
                    c.setId(cellElementIds.get(key));
                    // --20250212:这一行应该没什么用，取消key引用减少内存: c.setCellElementName(key);
                }
            }
        } catch (CustomException | DataSourceException | IOException e) {
            logger.error("dim field mapping failed,modelId:{}.cause:{}", modelId, e.getMessage(), e);
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_FORM_MAPPING_FAILED);
        }
    }

    private String buildCellElementKeyWithDimFieldMappers(CellElement cellElement,
            Map<String, DimFieldMapperDO> dimFieldMappers) {
        List<CellDimension> dimensions = cellElement.getDimensions();
        return dimFieldMappers.entrySet().stream().map(m -> {
            String key = m.getKey();
            for (CellDimension dimension : dimensions) {
                if (dimension.getDimensionId().equalsIgnoreCase(key)) {
                    return m.getValue().getFieldName() + ":" + dimension.getDimensionName();
                }
            }
            return "_" + key + "_";
        }).collect(Collectors.joining("&"));
    }

    private String buildKeyWithDimFieldMappers(Map record, Map<String, DimFieldMapperDO> dimFieldMappers) {
        return dimFieldMappers.values().stream().map(
                        dimFieldMapperDO -> dimFieldMapperDO.getFieldName() + ":" + record.get(dimFieldMapperDO.getFieldName()))
                .collect(Collectors.joining("&"));
    }

    public List<CellElement> getGlobalCellElements(CellElement cellElement) {
        return cellDimensionDao.findGlobalCellElements(cellElement);
    }

    /**
     * 查询cellElement的unitId以及对应modelId,缩小查询范围
     *
     * @param queryCondition 查询条件
     * @return 指定model对应的element【仅含unitId】
     */
    public List<CellElement> findSimpleCellElements(CellElement queryCondition) {
        return cellDimensionDao.findSimpleCellElements(queryCondition);
    }

    public List<CellElement> getGlobalCellElementsById(List<Long> ids) {
        String policy = DictConfigUtil.getProperty(SQL_BUILD_POLICY, MANUAL);
        if (MANUAL.equals(policy)) {
            return getGlobalCellElementsByIdV2(ids);
        } else {
            return getCellElements(ids);
        }
    }

    private List<CellElement> getCellElements(List<Long> ids) {
        if (ids == null || ids.size() == 0) {
            return Collections.emptyList();
        }
        int size = ids.size();
        int batchSize = Integer.parseInt(DictConfigUtil.getProperty("sql.global_cell_t.query-size", "1000"));
        List<CellElement> cellElements = new ArrayList<>(size);
        int count = size / batchSize;
        for (int i = 0; i <= count; i++) {
            int start = i * batchSize;
            int end = Math.min(size, (i + 1) * batchSize);
            List<Long> subIds = ids.subList(start, end);
            cellElements.addAll(cellDimensionDao.findGlobalCellElementsById(subIds));
        }
        return cellElements;
    }

    private List<CellElement> getGlobalCellElementsByIdV2(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        String inPartSql = String.join(",", ids.stream().map(String::valueOf).toArray(String[]::new));
        return cellDimensionDao.findGlobalCellElementsByIdV2(inPartSql);
    }

    public CellTypeEnum getCellTypeEnum(String modelId, FormCellInfoRequest.CellInfo cellInfo) {
        List<DimInstance> dimMemberList = cellInfo.getDimMemberList();
        List<String> memberIds = dimMemberList.stream().map(DimInstance::getMemberId).collect(Collectors.toList());
        try {
            boolean isNumeric = myDimMemberApplicationService.isNumeric(memberIds, modelId);
            if (isNumeric) {
                return CellTypeEnum.ATM;
            }
        } catch (CustomException e) {
            logger.warn("get cell type failed,then return numeric.modelId:{},cellPosition:{}", modelId,
                    cellInfo.getCellPosition());
        }
        return CellTypeEnum.TEXT;
    }

    public CellTypeEnum getCellTypeEnum(String modelId, List<String> memberIds) {
        try {
            boolean isNumeric = myDimMemberApplicationService.isNumeric(memberIds, modelId);
            if (isNumeric) {
                return CellTypeEnum.ATM;
            }
        } catch (CustomException e) {
            logger.warn("get cell type failed,then return numeric.modelId:{},memberIds:{}", modelId,
                    String.join(",", memberIds));
        }
        return CellTypeEnum.TEXT;
    }

    public void saveGlobalCellsAndCalculationCells(List<CellElement> cellElements) {
        // 优先插入global表，然后保存calculation表，calculation表数据必须要求在global表存在
        String modelId = cellElements.get(0).getModelId();
        newDeployModels = DictConfigUtil.getProperty(RULE_DEPLOY_NEW_MODELS, newDeployModels);
        if("*".equals(newDeployModels) || newDeployModels.contains(modelId)) {
            logger.info("this model {} is using new save cells code.", modelId);
            batchInsertRepo.batchInsertGlobalCells(cellElements);
            batchInsertRepo.batchInsertCalculation(cellElements);
        } else {
            String ignoreFilterShortName = DictConfigUtil.getProperty(RULE_DEPLOY_IGNORE_FILTER_SHORT_NAME, FALSE);
            if (TRUE.equals(ignoreFilterShortName)) {
                cellDimensionDao.batchSaveGlobalCellElements(cellElements);
            } else {
                cellDimensionDao.batchSaveUniqueGlobalCellElements(cellElements);
            }
            // 校验表名
            ValidateInjectionRisksUtil.validateInjectionRisks(cellElements.get(0).getModelTableName());
            cellDimensionDao.batchSaveCalculationCellElements(cellElements);
        }
    }

    private void clearDirtyCell(String modelId, String modelTableName) {
        logger.info("clearDirtyGlobalCell, modelId :{}, modelTableName: {}", modelId, modelTableName);
        try {
            int count = cellDimensionDao.deleteDirtyGlobalCell(modelId, modelTableName);
            logger.info("global cell have been deleted: {}", count);
            // 校验表名
            ValidateInjectionRisksUtil.validateInjectionRisks(modelTableName);
            count = cellDimensionDao.deleteCalculationCell(modelTableName);
            logger.info("calculation cell have been deleted: {}", count);
        } catch (Exception e) {
            logger.error("error, ", e);
        }
    }
}
