/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.workflow.deploy.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基础参数类
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseParam {
    /**
     * 工作空间ID
     */
    private Long workspaceId;

    /**
     * 脚本ID
     */
    private Long scriptId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 模型ID
     */
    private Long modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 动态数据库标识
     */
    private String dynamicDB;

    /**
     * 作业名称
     */
    private String jobName;

    /**
     * 开始索引
     */
    private Integer startIndex;

    /**
     * 唯一随机数
     */
    private Integer uniqueRandomNumber;

    /**
     * 工作流ID
     */
    private Integer workFlowId;

    /**
     * 活动ID
     */
    private Integer activityId;
}
