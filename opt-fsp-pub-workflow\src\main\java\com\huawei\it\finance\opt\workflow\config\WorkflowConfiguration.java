/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.workflow.config;

import com.huawei.it.finance.opt.workflow.Constants;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import io.temporal.serviceclient.WorkflowServiceStubs;
import io.temporal.worker.Worker;
import io.temporal.worker.WorkerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * Workflow配置类
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
@Slf4j
@Configuration
public class WorkflowConfiguration {

    @Value("${temporal.service.address:127.0.0.1:7233}")
    private String temporalServiceAddress;

    @Value("${temporal.namespace:default}")
    private String temporalNamespace;

    /**
     * 创建Temporal服务存根
     */
    @Bean
    public WorkflowServiceStubs workflowServiceStubs() {
        return WorkflowServiceStubs.newLocalServiceStubs();
    }

    /**
     * 创建Workflow客户端
     */
    @Bean
    public WorkflowClient workflowClient(WorkflowServiceStubs serviceStubs) {
        return WorkflowClient.newInstance(serviceStubs);
    }

    /**
     * 创建Worker工厂
     */
    @Bean
    public WorkerFactory workerFactory(WorkflowClient workflowClient) {
        return WorkerFactory.newInstance(workflowClient);
    }

    /**
     * 创建部署队列Worker
     */
    @Bean
    public Worker deployWorker(WorkerFactory workerFactory) {
        Worker worker = workerFactory.newWorker(Constants.PARALLEL_DEPLOY_QUEUE_NAME);
        
        // 注册工作流实现
        worker.registerWorkflowImplementationTypes(
            com.huawei.it.finance.opt.workflow.deploy.impl.DslDeployWorkflowImpl.class
        );
        
        log.info("Deploy worker created for queue: {}", Constants.PARALLEL_DEPLOY_QUEUE_NAME);
        return worker;
    }

    /**
     * 创建默认的工作流选项
     */
    @Bean
    public WorkflowOptions defaultWorkflowOptions() {
        return WorkflowOptions.newBuilder()
                .setWorkflowExecutionTimeout(Duration.ofSeconds(Constants.WORKFLOW_TIMEOUT_SECONDS))
                .setTaskQueue(Constants.PARALLEL_DEPLOY_QUEUE_NAME)
                .build();
    }
}
