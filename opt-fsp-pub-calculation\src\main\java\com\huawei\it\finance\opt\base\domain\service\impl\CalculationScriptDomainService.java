/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.domain.service.impl;

import com.huawei.it.edm.client.entity.DocumentVO;
import com.huawei.it.edm.client.exception.EdmException;
import com.huawei.it.finance.opt.application.dto.response.AlterVariableResponse;
import com.huawei.it.finance.opt.application.service.IAlterVariableAppService;
import com.huawei.it.finance.opt.application.service.IAlterVariableReferAppService;
import com.huawei.it.finance.opt.application.service.IExportTaskAppService;
import com.huawei.it.finance.opt.application.service.IImportTaskAppService;
import com.huawei.it.finance.opt.base.domain.repository.ICalculationRuleRepo;
import com.huawei.it.finance.opt.base.domain.repository.ICalculationScriptDraftRepo;
import com.huawei.it.finance.opt.base.domain.repository.ICalculationScriptRepo;
import com.huawei.it.finance.opt.base.domain.repository.ICalculationToKnowledgeGesRepo;
import com.huawei.it.finance.opt.base.domain.repository.ICalculationToKnowledgeRepo;
import com.huawei.it.finance.opt.base.domain.repository.IScriptFolderRepo;
import com.huawei.it.finance.opt.base.domain.service.ICalculationScriptDomainService;
import com.huawei.it.finance.opt.base.entity.CalculationRule;
import com.huawei.it.finance.opt.base.entity.CalculationScript;
import com.huawei.it.finance.opt.base.entity.CalculationScriptDraft;
import com.huawei.it.finance.opt.base.entity.CalculationScriptModel;
import com.huawei.it.finance.opt.base.entity.CalculationScriptModelListChecker;
import com.huawei.it.finance.opt.base.entity.CalculationScripts;
import com.huawei.it.finance.opt.base.entity.CalculationScriptsImport;
import com.huawei.it.finance.opt.base.entity.RelativeModel;
import com.huawei.it.finance.opt.base.entity.ScriptFolder;
import com.huawei.it.finance.opt.base.entity.ScriptModel;
import com.huawei.it.finance.opt.base.exception.CalculationException;
import com.huawei.it.finance.opt.base.infrastructure.db.repository.impl.BatchInsertRepo;
import com.huawei.it.finance.opt.base.infrastructure.db.repository.impl.DbFunctionRepo;
import com.huawei.it.finance.opt.base.util.CalculationScriptConstants;
import com.huawei.it.finance.opt.base.vo.CalculationScriptRelativeModels;
import com.huawei.it.finance.opt.base.vo.ScriptFolderVO;
import com.huawei.it.finance.opt.base.workflow.deploy.DslDeployContext;
import com.huawei.it.finance.opt.workflow.deploy.dto.DeployCellParam;
import com.huawei.it.finance.opt.common.exception.AlterVariableException;
import com.huawei.it.finance.opt.domain.entity.AlterVariableReferEntity;
import com.huawei.it.finance.opt.domain.entity.ExportTask;
import com.huawei.it.finance.opt.domain.entity.ImportTask;
import com.huawei.it.finance.opt.domain.enums.ExportTaskEnum;
import com.huawei.it.finance.opt.dsl.complier.input.DSLSourceInfo;
import com.huawei.it.finance.opt.dsl.complier.objcode.ObjectCode;
import com.huawei.it.finance.opt.dsl.complier.output.DeployEffect;
import com.huawei.it.finance.opt.dsl.service.CompileService;
import com.huawei.it.finance.opt.fsp.pub.common.exception.DataSourceException;
import com.huawei.it.finance.opt.graph.entity.Graph;
import com.huawei.it.finance.opt.graph.service.IGraphOperationService;
import com.huawei.it.finance.opt.task.entity.QuartzTaskInfo;
import com.huawei.it.finance.opt.task.infrastructure.db.repository.IQuartzTaskInfoRepo;
import com.huawei.it.finance.opt.tech.edm.service.IEdmService;
import com.huawei.it.finance.opt.tech.enums.ModuleEnum;
import com.huawei.it.finance.opt.tech.exception.PubErrorCode;
import com.huawei.it.finance.opt.tech.util.DictConfigUtil;
import com.huawei.it.finance.opt.tech.util.IdUtil;
import com.huawei.it.finance.opt.tech.util.UserUtil;
import com.huawei.it.finance.pub.dimmodel.application.service.IDimModelApplicationService;
import com.huawei.it.finance.pub.dimmodel.common.advice.TrackExecutionTime;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.util.PathUtil;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import com.huawei.us.common.file.UsFileLiteUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.DEFAULT_KG_PLATFORM_MODEL;
import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.GRAPH_WHITE_LIST_WORKSPACE_IDS;
import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.KG_PLATFORM_MODEL;
import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.KG_PLATFORM_MODEL_GES;
/**
 * calculation script domain service
 *
 * <AUTHOR>
 * @since 2024/02/22
 */
@Service
@Slf4j
public class CalculationScriptDomainService implements ICalculationScriptDomainService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CalculationScriptDomainService.class);

    @Autowired
    private ICalculationScriptRepo iCalculationScriptRepo;

    @Autowired
    private IScriptFolderRepo iScriptFolderRepo;

    @Autowired
    private ICalculationScriptDraftRepo iCalculationScriptDraftRepo;

    @Autowired
    private ICalculationRuleRepo iCalculationRuleRepo;

    @Autowired
    private BatchInsertRepo batchInsertRepo;

    @Autowired
    private ICalculationToKnowledgeRepo iCalculationToKnowledgeRepo;

	@Autowired
    private ICalculationToKnowledgeGesRepo iCalculationToKnowledgeGesRepo;

    @Autowired
    private IDimModelApplicationService iDimModelApplicationService;

    @Autowired
    private IImportTaskAppService importTaskAppService;

    @Autowired
    private IExportTaskAppService exportTaskService;

    @Autowired
    private CompileService compileService;

    @Autowired
    private IEdmService edmService;

    @Autowired
    private IAlterVariableReferAppService alterVariableReferAppService;

    @Autowired
    private IAlterVariableAppService alterVariableAppService;

    @Resource(name = "GraphDBOperationService")
    private IGraphOperationService iGraphOperationService;

    @Autowired
    private IRegistryQueryService registryQueryService;

    @Autowired
    private IQuartzTaskInfoRepo iQuartzTaskInfoRepo;

    @Value("${graph.enabled:false}")
    private boolean graphEnabled;

    @Value("${new-deploy-models:}")
    private String newDeployModels;

    /**
     * 根据ID查询计算脚本
     *
     * @param scriptId 脚本ID
     * @return 计算脚本
     */
    @Override
    public CalculationScript getCalculationScriptById(long scriptId) {
        return iCalculationScriptRepo.getCalculationScriptById(scriptId);
    }

    /**
     * 查询规则脚本列表
     *
     * @param calculationScript 计算脚本
     * @return 计算脚本列表
     */
    @Override
    public List<CalculationScript> getCalculationScripts(CalculationScript calculationScript) {
        return iCalculationScriptRepo.getCalculationScripts(calculationScript);
    }

    /**
     * 保存规则脚本
     *
     * @param calculationScript 计算脚本
     */
    @Override
    public CalculationScript saveCalculationScript(CalculationScript calculationScript) {
        checkFolderId(calculationScript.getFolderId());
        checkModelId(calculationScript.getModelId(), calculationScript.getWorkspaceId());
        checkNameExistsInScriptLibrary(calculationScript);
        // 增加对folderCode的处理
        ScriptFolder folder = iScriptFolderRepo.findScriptFolderById(calculationScript.getFolderId());
        calculationScript.setFolderCode(folder.getFolderCode());
        if (calculationScript.getVersionId() == null) {
            calculationScript.setVersionId(1L);
        }
        if (calculationScript.getBusinessVersionId() == null) {
            calculationScript.setBusinessVersionId(1L);
        }
        if (calculationScript.getStatus() == null) {
            calculationScript.setStatus(CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_DRAFT);
        }
        iCalculationScriptRepo.saveCalculationScript(calculationScript);
        return calculationScript;
    }

    /**
     * 移动规则脚本，检查文件夹ID合法性
     *
     * @param calculationScript 计算脚本
     */
    @Override
    public void moveCalculationScript(CalculationScript calculationScript) {
        checkFolderId(calculationScript.getFolderId());
        // 增加对folderCode的处理
        ScriptFolder folder = iScriptFolderRepo.findScriptFolderById(calculationScript.getFolderId());
        calculationScript.setFolderCode(folder.getFolderCode());
        iCalculationScriptRepo.moveCalculationScript(calculationScript);
    }

    /**
     * 重命名规则脚本，检查修改后的规则名称是否在本文件夹内有重名现象
     *
     * @param calculationScript 计算脚本
     */
    @Override
    public void renameCalculationScript(CalculationScript calculationScript) {
        checkNameExistsInScriptLibrary(calculationScript);
        iCalculationScriptRepo.renameCalculationScript(calculationScript);
    }

    /**
     * 检查规则库中，重名规则。
     *
     * @param calculationScript 计算脚本
     */
    private void checkNameExistsInScriptLibrary(CalculationScript calculationScript) {
        CalculationScript copyScript = new CalculationScript();
        copyScript.setScriptName(calculationScript.getScriptName());
        copyScript.setWorkspaceId(calculationScript.getWorkspaceId());
        List<CalculationScript> list = iCalculationScriptRepo.getCalculationScripts(copyScript);
        if (!list.isEmpty()) {
            if (list.size() > 1) {
                throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_DUPLICATE_NAME);
            } else {
                CalculationScript script = list.stream().findFirst().get();
                if (!script.getScriptId().equals(calculationScript.getScriptId())) {
                    throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_DUPLICATE_NAME);
                }
            }
        }
    }

    /**
     * 检查某一个规则文件夹的ID是否为合法ID
     *
     * @param scriptFolderId 文件夹ID
     */
    private void checkFolderId(long scriptFolderId) {
        if (!iScriptFolderRepo.isValidFolderId(scriptFolderId)) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_FOLDER_NOT_FOUND);
        }
    }

    /**
     * 粘贴规则脚本
     *
     * @param calculationScript 计算脚本
     */
    @Override
    public void pasteCalculationScript(CalculationScript calculationScript) {
        checkFolderId(calculationScript.getFolderId());
        checkModelId(calculationScript.getModelId(), calculationScript.getWorkspaceId());
        checkNameExistsInWorkspace(calculationScript);
        // 增加对folderCode的处理
        ScriptFolder folder = iScriptFolderRepo.findScriptFolderById(calculationScript.getFolderId());
        calculationScript.setFolderCode(folder.getFolderCode());
        // 粘贴出一个新规则
        CalculationScript newScript = iCalculationScriptRepo.pasteCalculationScript(calculationScript);
        // 查询这个新规则，找到对应的scriptId
        List<CalculationScript> scriptList = iCalculationScriptRepo.getCalculationScripts(newScript);
        if (scriptList.isEmpty()) {
            return;
        }
        Long newScriptId = scriptList.get(scriptList.size() - 1).getScriptId();
        // 如果存在对应的草稿，也同步粘贴草稿
        iCalculationScriptDraftRepo.pasteCalculationScriptDraft(calculationScript, newScriptId);
    }

    /**
     * 检查规则的模型ID，是否对应一个合法的存在的模型，如果不是合法模型则报错
     *
     * @param modelId 模型ID
     */
    private void checkModelId(String modelId, long workspaceId) {
        if (findRelativeModels(workspaceId, modelId, null).isEmpty()) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_MODEL_NOT_FOUND);
        }
    }

    private List<RelativeModel> findRelativeModels(long workspaceId, String modelId, String modelName) {
        return iDimModelApplicationService.findRelativeModels(workspaceId, modelId, modelName).stream().map(o -> {
            RelativeModel relativeModel = new RelativeModel();
            relativeModel.setModelId(o.getModelId());
            relativeModel.setModelName(o.getModelName());
            return relativeModel;
        }).collect(Collectors.toList());
    }

    /**
     * 删除规则脚本
     *
     * @param scriptId 脚本ID
     */
    @Override
    public void deleteCalculationScript(long scriptId, long workspaceId) {
        CalculationScript calculationScript = new CalculationScript();
        calculationScript.setScriptId(scriptId);
        List<CalculationScript> calculationScripts = getCalculationScripts(calculationScript);
        if (calculationScripts.isEmpty()) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_NOT_FOUND);
        }
        if (isCalculationScriptDeploying(scriptId, workspaceId)) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_IS_DEPLOYING);
        }
        // 同步删除对应的单元格粒度的规则
        iCalculationRuleRepo.deleteAllCalculationRulesByCalculationScript(calculationScript);
        // 删除脚本源码
        iCalculationScriptRepo.deleteCalculationScript(calculationScript);
        // 同步删除对应的草稿
        iCalculationScriptDraftRepo.deleteCalculationScriptDraftByScriptId(scriptId);
    }

    /**
     * 单个规则脚本部署-单规则+批量部署单次循环
     * 执行部署且保存编译结果，生成三元组数据，最后刷新规则脚本的状态，事务控制到本级别。
     */
    @Override
    @TrackExecutionTime
    public int singleDeployCalculationScript(CalculationScript calculationScript, DSLSourceInfo dslSourceInfo,
            QuartzTaskInfo quartzTaskInfo) throws AlterVariableException, DataSourceException {
        StopWatch sw = new StopWatch("1.部署计算脚本: " + quartzTaskInfo.getJobName());
        sw.start("1. 更新脚本草稿");
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, "1. 更新脚本草稿", 1, 15);
        updateDraftWhileDeploying(calculationScript, dslSourceInfo);
        sw.stop();
        sw.start("2. 编译和部署DSL");
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, "2. 编译和部署DSL", 2, 15);
        DeployEffect deployEffect = compileService.compileAndDeployDSL(dslSourceInfo, quartzTaskInfo);
        sw.stop();
        sw.start("3. 批量保存规则和脚本");
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, "3. 批量保存规则和脚本", 9, 15);
        saveCalculationRules(calculationScript, deployEffect, quartzTaskInfo);
        // 如果规则包含了替代变量，那么就刷新替代变量的引用关系
        if (!deployEffect.getSeqVariableList().isEmpty()) {
            saveCalculationScriptVariableRefer(calculationScript, deployEffect.getSeqVariableList());
        }
        sw.stop();
        sw.start("4. 保存规则计算图");
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, "4. 保存规则计算图", 12, 15);
        // 计算节点图刷新, 放在部署脚本的最后一环
        String ruleDeployUseGes = DictConfigUtil.getProperty(KG_PLATFORM_MODEL, DEFAULT_KG_PLATFORM_MODEL);
        if (KG_PLATFORM_MODEL_GES.equals(ruleDeployUseGes)) {
            saveCalculationGraphToGes(calculationScript, deployEffect, quartzTaskInfo);
        } else {
            saveCalculationGraph(calculationScript, deployEffect, quartzTaskInfo);
        }
        finishDeploy(calculationScript, dslSourceInfo, quartzTaskInfo);
        sw.stop();
        LOGGER.info("规则ID：{}, 部署成功", calculationScript.getScriptId());
        LOGGER.info("\n{}", sw.prettyPrint());
        return deployEffect.getRuleOfObjectCode().size();
    }

    @Override
    public void finishDeploy(CalculationScript calculationScript, DSLSourceInfo dslSourceInfo, QuartzTaskInfo quartzTaskInfo) {
        // 更新规则状态为部署成功
        calculationScript.setStatus(CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_DEPLOY_SUCCESS);
        iCalculationScriptRepo.updateStatus(calculationScript);
        // 部署成功后更新规则内容
        iCalculationScriptRepo.updateScriptContent(calculationScript, dslSourceInfo.getDslSourceText());
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, CalculationScriptConstants.EXECUTE_SUCCESS, 15, 15);
    }

    /**
     * 保存规则计算图
     *
     * @param calculationScript 部署script
     * @param deployEffect 部署内容
     */
    public void saveCalculationGraph(CalculationScript calculationScript, DeployEffect deployEffect,
            QuartzTaskInfo quartzTaskInfo) {
        // 可以配置跳过save graph
        if (!graphEnabled || !isInWhiteList(calculationScript.getWorkspaceId())) {
            LOGGER.warn("skip save graph...");
            return;
        }
        StopWatch sw = new StopWatch("4. 保存计算规则图 ");
        sw.start("4.1 构造图结构");
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, "4.1 构造图结构", 12, 15);
        Graph graph = Graph.constructGraphByCellId2Node(deployEffect.getCellId2Node());
        sw.stop();
        sw.start("4.2 数据库删除旧图&创建新图");
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, "4.2 数据库删除旧图&创建新图", 13, 15);
        iGraphOperationService.deleteGraph(calculationScript.getWorkspaceId(), calculationScript.getScriptId());
        iGraphOperationService.createGraph(graph, calculationScript.getWorkspaceId(), calculationScript.getScriptId(),
                calculationScript.getModelId());
        sw.stop();
        sw.start("4.3 插入实体表和关系表");
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, "4.3 插入实体表和关系表", 14, 15);
        // 保存实体关系信息, 依赖createGraph生成kg表
        if ("*".equals(newDeployModels) || newDeployModels.contains(calculationScript.getModelId())) {
            LOGGER.info("Calculation to Knowledge is now using a new way to save , modelId : {}.",
                    calculationScript.getModelId());
            iCalculationToKnowledgeRepo.saveEntitiesAndRelationshipsV2(calculationScript, deployEffect);
        } else {
            iCalculationToKnowledgeRepo.saveEntitiesAndRelationships(calculationScript, deployEffect);
        }
        sw.stop();
        LOGGER.info("\n{}", sw.prettyPrint());
    }

	/**
     * 保存规则计算图GES
     *
     * @param calculationScript 部署script
     * @param deployEffect 部署内容
     */
    public void saveCalculationGraphToGes(CalculationScript calculationScript, DeployEffect deployEffect,
                                          QuartzTaskInfo quartzTaskInfo) {
        // 可以配置跳过save graph
        if (!graphEnabled || !isInWhiteList(calculationScript.getWorkspaceId())) {
            LOGGER.warn("skip save graph...");
            return;
        }
        StopWatch sw = new StopWatch("4. 保存计算规则图 ");
        sw.start("4.1 构造图结构");
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, "4.1 构造图结构", 12, 15);
        sw.stop();
        sw.start("4.2 数据库删除旧图&创建新图");
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, "4.2 数据库删除旧图&创建新图", 13, 15);
        sw.stop();
        sw.start("4.3 插入实体表和关系信息");
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, "4.3 插入实体表和关系信息", 14, 15);
        // 保存实体关系信息, 依赖graph对象生成ges数据
        LOGGER.info("save entity and relationship data to GES , modelId : {}.",
                calculationScript.getModelId());
        iCalculationToKnowledgeGesRepo.saveEntitiesAndRelationships(calculationScript, deployEffect);
        sw.stop();
        LOGGER.info("\n{}", sw.prettyPrint());
    }


    /**
     * 查询数据字典的白名单
     *
     * @return boolean
     */
    private boolean isInWhiteList(long workspaceId) {
        // registry value must below 4000
        String whiteListString = null;
        try {
            whiteListString = registryQueryService.findValueByPath(GRAPH_WHITE_LIST_WORKSPACE_IDS, false);
            LOGGER.info("whiteListString: {}", whiteListString);
        } catch (ApplicationException e) {
            LOGGER.error("error:", e);
            return false;
        }
        if (StringUtils.isBlank(whiteListString)) {
            return false;
        }
        String[] workspaceWhiteList = whiteListString.split(",");
        if (workspaceWhiteList.length == 0) {
            return false;
        }
        Set<String> workspaceWhiteSet = new HashSet<>(Arrays.asList(workspaceWhiteList));
        return workspaceWhiteSet.contains(String.valueOf(workspaceId));
    }

    public void updateDraftWhileDeploying(CalculationScript calculationScript, DSLSourceInfo dslSourceInfo) {
        // 更新DSL的部署时间、部署人
        iCalculationScriptRepo.updateCalculationScript(calculationScript);
        CalculationScriptDraft draft = new CalculationScriptDraft();
        draft.setScriptId(calculationScript.getScriptId());
        // 此时calculationScript的content字段为空，草稿的内容需要用dslSourceInfo的内容
        draft.setContent(dslSourceInfo.getDslSourceText());
        draft.setWorkspaceId(calculationScript.getWorkspaceId());
        draft.setModelId(calculationScript.getModelId());
        iCalculationScriptDraftRepo.saveOrUpdateCalculationScriptDraft(draft);
    }

    public void saveCalculationScriptVariableRefer(CalculationScript calculationScript, List<String> seqVariableList)
            throws AlterVariableException {
        if (seqVariableList.isEmpty()) {
            return;
        }
        // 去重
        List<String> uniqueVariableList = seqVariableList.stream().distinct().collect(Collectors.toList());
        List<Long> variableIdList = new ArrayList<>();
        for (String seqVariable : uniqueVariableList) {
            String variableName = seqVariable.replaceAll("\'", "");
            AlterVariableResponse alterVariableResponse = alterVariableAppService.findAlterVariable(variableName);
            variableIdList.add(alterVariableResponse.getVariableId());
        }
        String referObjectId = String.valueOf(calculationScript.getScriptId());
        // 软删除当前规则的替代变量引用关系，如果有的话
        alterVariableReferAppService.deleteByReferObjectId(referObjectId, ModuleEnum.RULE.getCode());
        // 把当前规则所用到的替代变量，都构造引用关系进行保存
        for (Long variableId : variableIdList) {
            AlterVariableReferEntity alterVariableReferEntity = new AlterVariableReferEntity();
            alterVariableReferEntity.setVariableId(variableId);
            alterVariableReferEntity.setReferObjectId(referObjectId);
            alterVariableReferEntity.setReferType(ModuleEnum.RULE.getCode());
            alterVariableReferAppService.saveVariableRefer(alterVariableReferEntity);
        }
    }

    /**
     * 规则修改状态
     */
    @Override
    public int updateStatus(CalculationScript calculationScript) {
        return iCalculationScriptRepo.updateStatus(calculationScript);
    }

    @Override
    public void saveCalculationGraphToGesForTemporal(CalculationScript calculationScript, DslDeployContext context){
        // 可以配置跳过save graph
        if (!graphEnabled || !isInWhiteList(calculationScript.getWorkspaceId())) {
            LOGGER.warn("skip save graph...");
            return;
        }

        // 保存实体关系信息, 依赖graph对象生成ges数据
        LOGGER.info("save entity and relationship data to GES , modelId : {}.", calculationScript.getModelId());
        iCalculationToKnowledgeGesRepo.saveEntitiesAndRelationshipsForTemporal(calculationScript, context);
    }
    /**
     * 根据DSL引擎解析的结果，将解析后的规则保存。
     * 批量完成编译结果插入后，需要根据ID更新calculationScript的字段content
     *
     * @param calculationScript 规则脚本
     * @param deployEffect 编译结果
     */
    @Override
    public void saveCalculationRules(CalculationScript calculationScript, DeployEffect deployEffect,
                                     QuartzTaskInfo quartzTaskInfo) throws DataSourceException {
        StopWatch sw = new StopWatch("3. 批量保存规则和脚本 ");
        Map<Long, CalculationRule> ruleMap = new HashMap<>();
        List<ObjectCode> objectCodes = deployEffect.getRuleOfObjectCode();
        for (ObjectCode obj : objectCodes) {
            Long cellId = (obj.getEffectiveCellId() == null ? obj.getCellId() : obj.getEffectiveCellId());
            CalculationRule calculationRule = CalculationRule.builder().unitId(cellId)
                    .unitName(obj.getCellName()).groupId(calculationScript.getModelId())
                    .businessVersionId(CalculationScriptConstants.CALCULATION_SCRIPT_DEFAULT_BUSINESS_VERSION)
                    .versionId(CalculationScriptConstants.CALCULATION_SCRIPT_DEFAULT_VERSION)
                    .sourceType(CalculationScriptConstants.SOURCE_TYPE_DSL).scriptId(calculationScript.getScriptId())
                    .rowNumber(obj.getColumn()).lineNumber(obj.getLine()).priority(1).content(obj.getAviatorCode())
                    .dbFuncDimsCombMap(obj.getDbFuncInfos()).richContent(obj.getBusinessRule())
                    .unitCode(obj.getCellCode()).build();
            calculationRule.setWorkspaceId(calculationScript.getWorkspaceId());
            calculationRule.setRuleId(IdUtil.getUUID());
            ruleMap.put(obj.getCellId(), calculationRule);
        }
        sw.start("3.1 数据库删除单元格级别旧规则");
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, "3.1 数据库删除单元格级别旧规则", 10, 15);
        deleteHistoryDbFuncRules(calculationScript, quartzTaskInfo);
        sw.stop();
        sw.start("3.2 插入单元格级别新规则");
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, "3.2 插入单元格级别新规则", 11, 15);
        iCalculationRuleRepo.batchInsertCalculationRules(ruleMap);
        // 如果存在特殊场景(sum/average)，保存dbFunc函数信息和对应左侧区域的信息
        if (!deployEffect.getDbFuncEntities().isEmpty()) {
            // 3.2.1 先写入函数记录 TODO:还要加运维字段。
            DbFunctionRepo.saveDbFuncInfos(deployEffect.getDbFuncEntities(), null);
            // 3.2.2 再写入函数关联的单元格信息 TODO:还要加运维字段。
            iCalculationRuleRepo.batchInsertDbFuncDimsCombs(ruleMap, null);
        }
        sw.stop();
        LOGGER.info("\n{}", sw.prettyPrint());
    }


    // TODO：2025-4-27 先保存到临时表，汇总后合入主库
    // 涉及表 ： dm_fsppub_calculation_rule_t || dm_fsppub_calculation_dbfunc_cell_info_t
    // || DB函数 || dm_fsppub_calculation_dbfunc_info_t
    @Override
    public void saveCalculationRulesForTemporal(CalculationScript calculationScript, DeployEffect deployEffect,
                                                DslDeployContext context, DeployCellParam param) throws DataSourceException {
        Map<Long, CalculationRule> ruleMap = new HashMap<>();
        List<ObjectCode> objectCodes = deployEffect.getRuleOfObjectCode();
        for (ObjectCode obj : objectCodes) {
            CalculationRule calculationRule = CalculationRule.builder().unitId(obj.getCellId())
                    .unitName(obj.getCellName()).groupId(calculationScript.getModelId())
                    .businessVersionId(CalculationScriptConstants.CALCULATION_SCRIPT_DEFAULT_BUSINESS_VERSION)
                    .versionId(CalculationScriptConstants.CALCULATION_SCRIPT_DEFAULT_VERSION)
                    .sourceType(CalculationScriptConstants.SOURCE_TYPE_DSL).scriptId(calculationScript.getScriptId())
                    .rowNumber(obj.getColumn()).lineNumber(obj.getLine()).priority(1).content(obj.getAviatorCode())
                    .dbFuncDimsCombMap(obj.getDbFuncInfos()).richContent(obj.getBusinessRule())
                    .unitCode(obj.getCellCode()).build();
            calculationRule.setWorkspaceId(calculationScript.getWorkspaceId());
            calculationRule.setRuleId(IdUtil.getUUID());
            ruleMap.put(obj.getCellId(), calculationRule);
        }
        saveTempRules(deployEffect, context, ruleMap, param);
    }

    private void saveTempRules(DeployEffect deployEffect,
                               DslDeployContext context, Map<Long, CalculationRule> ruleMap,
                               DeployCellParam param)
            throws DataSourceException {
        context.executeStep(9, "【workflow】3.2 插入单元格级别新规则和DB函数信息到缓存表");
        // 202504 这里引入缓存表dm_fsppub_calculation_rule_temporal_t。
        iCalculationRuleRepo.batchInsertCalculationRulesForTemporal(ruleMap, param);
        // 如果存在特殊场景(sum/average)，保存dbFunc函数信息和对应左侧区域的信息
        if (!deployEffect.getDbFuncEntities().isEmpty()) {
            // 3.2.1 先写入函数记录
            // TODO: 202504 这里需要建临时表dm_fsppub_calculation_dbfunc_info_temporal_t。
            DbFunctionRepo.saveDbFuncInfos(deployEffect.getDbFuncEntities(), param);
            // TODO: 202504 这里需要建临时表dm_fsppub_calculation_dbfunc_cell_info_temporal_t。
            // 3.2.2 再写入函数关联的单元格信息。
            iCalculationRuleRepo.batchInsertDbFuncDimsCombs(ruleMap, param);
        }
    }

    @Override
    public void deleteHistoryDbFuncRules(CalculationScript calculationScript, QuartzTaskInfo quartzTaskInfo) {
        //TODO
        // CalculationScriptDomainService：修改saveCalculationRules方法，分批部署时不能删除当前script id的旧规则，
        // 需要在全部DeployCellActivityImpl成功后，在DeployGraphActivityImpl步骤里删除
        // 同时在使用CalculationRules的地方，过滤部分部署成功的任务生成的CalculationRules数据

        // TODO: 202504 这里只需要 scriptId即可。
        iCalculationRuleRepo.deleteAllCalculationRulesByCalculationScript(calculationScript);
        // 3.1.2删除历史DB函数和脚本，只要重新部署规则，历史函数就作废了。
        List<Long> scriptIds = new ArrayList<>();
        // TODO: 202504 这里每次执行只有一条scriptId,不需要scriptId列表，后续改进。
        scriptIds.add(calculationScript.getScriptId());
        // ******* 先删除函数关联的单元格信息
        iCalculationRuleRepo.doBatchDeleteDbFuncDimsCombs(scriptIds);
        // ******* 再删除DB函数本身
        List<String> dbFuncNames = iCalculationRuleRepo.queryDbFuncNamesByScriptIds(scriptIds);
        if (dbFuncNames != null && !dbFuncNames.isEmpty()) {
            try {
                DbFunctionRepo.dropDbFunctionsByFuncNames(dbFuncNames);
            }catch (DataSourceException e){
                LOGGER.error("drop db function fail : scriptIds{}, dbFuncNames{}", scriptIds, dbFuncNames, e);
            }
        }
        // ******* 最后删除函数信息表的数据
        iCalculationRuleRepo.doBatchDeleteDbFunc(scriptIds);
    }

    @Override
    public void migrateDataToRuleTable(CalculationScript calculationScript, Integer workFlowId) {
        // TODO: 202504 将临时表dm_fsppub_calculation_rule_temporal_t导入。
        iCalculationRuleRepo.migrateTempTableToRuleTable(calculationScript, workFlowId);
        // TODO: 202504 将临时表dm_fsppub_calculation_dbfunc_info_temporal_t导入。
        iCalculationRuleRepo.migrateTempTableDbFuncInfoTable(calculationScript, workFlowId);
        // TODO: 202504 将临时表dm_fsppub_calculation_dbfunc_cell_info_temporal_t导入。
        iCalculationRuleRepo.migrateTempTableDbFuncCellInfoTable(calculationScript, workFlowId);
    }

    @Override
    public List<String> queryWorkflowInfo(Long workspaceId, Integer workFlowId) {
        return iCalculationRuleRepo.queryWorkflowInfo(workspaceId, workFlowId);
    }

    @Override
    public int deleteWorkflowInfo(Long workspaceId, Integer workFlowId) {
        return iCalculationRuleRepo.deleteWorkflowInfo(workspaceId, workFlowId);
    }


    @Override
    public void migrateDataToCalculationTable(List<String> temporalTableName, Integer workFlowId) {
        batchInsertRepo.migrateDataToCalculationTable(temporalTableName, workFlowId);
    }

    @Override
    public void migrateDataToGlobalTable(Integer workFlowId) {
        batchInsertRepo.migrateDataToGlobalTable(workFlowId);
    }

    @Override
    public int queryRulesNumByScirptIdAndWorkspaceId(Long workspaceId, Long scriptId) {
        CalculationRule calculationRule = new CalculationRule();
        calculationRule.setScriptId(scriptId);
        calculationRule.setWorkspaceId(workspaceId);
        List<CalculationRule> calculationRules = iCalculationRuleRepo.getCalculationRules(calculationRule);
        return calculationRules.size();
    }

    /**
     * 回滚规则，增加对状态的处理
     *
     * @param latestVersion 计算脚本的最新版本
     */
    @Override
    public CalculationScript rollbackCalculationScript(CalculationScript latestVersion) {
        if (isCalculationScriptDeploying(latestVersion.getScriptId(), latestVersion.getWorkspaceId())) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_IS_DEPLOYING);
        }
        // 将已部署脚本的内容更新到草稿，并塞入latestVersion，同时更新规则脚本的状态
        CalculationScriptDraft draft = initCalculationScriptDraft(latestVersion);
        iCalculationScriptDraftRepo.updateCalculationScriptDraft(draft);
        if (!CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_DRAFT.equals(latestVersion.getStatus())) {
            // 更新状态至【已部署成功】
            latestVersion.setStatus(CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_DEPLOY_SUCCESS);
            iCalculationScriptRepo.updateStatus(latestVersion);
        }
        latestVersion.setDraft(draft);
        return latestVersion;
    }

    private CalculationScriptDraft initCalculationScriptDraft(CalculationScript calculationScript) {
        return CalculationScriptDraft.builder().scriptId(calculationScript.getScriptId())
                .modelId(calculationScript.getModelId()).content(calculationScript.getContent()).build();
    }

    /**
     * 部署规则后，再次修改DSL内容，规则修改为待部署状态
     *
     * @param calculationScript 计算脚本
     */
    @Override
    public void updateCalculationScriptAfterDeployment(CalculationScript calculationScript) {
        if (CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_DEPLOY_SUCCESS.equals(calculationScript.getStatus())) {
            // 规则修改为【待部署状态】
            calculationScript.setStatus(CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_UPDATED);
            iCalculationScriptRepo.updateStatus(calculationScript);
        }
    }

    /**
     * 规则脚本下载模板
     *
     * @param response HTTP响应
     */
    @Override
    public void downloadCalculationScriptTemplate(HttpServletResponse response) {
        importTaskAppService.downloadTemplateFile(response,
                CalculationScriptConstants.CALCULATION_SCRIPT_TEMPLATE_DISPLAY_NAME,
                CalculationScriptConstants.CALCULATION_SCRIPT_TEMPLATE_NAME);
    }

    /**
     * 规则导出
     *
     * @param calculationScripts 多个计算脚本
     */
    @Override
    public void exportCalculationScripts(CalculationScripts calculationScripts) {
        String xmlFilePath = null;
        ExportTask exportTask = null;
        String pathByDay = PathUtil.getPathByDay(CalculationScriptConstants.RULE);
        String userId = UserUtil.getUser(CalculationScriptConstants.USERID);
        String logPath = String.format(Locale.ROOT, "%s%s%s%s%s", pathByDay, File.separator, userId, File.separator,
                CalculationScriptConstants.CALCULATION_SCRIPT_EXPORT_LOG_PATH);
        try {
            String basePath = pathByDay + File.separator + userId;
            PathUtil.makeDirs(basePath);
            DateTimeFormatter formatter = DateTimeFormatter
                    .ofPattern(CalculationScriptConstants.CALCULATION_SCRIPT_EXPORT_FORMAT);
            String xmlFileName = String.format(Locale.ROOT, "%s%s%s",
                    CalculationScriptConstants.CALCULATION_SCRIPT_FILE_NAME, LocalDateTime.now().format(formatter),
                    CalculationScriptConstants.CALCULATION_SCRIPT_IMPORT_XML_FORMAT);
            xmlFilePath = basePath + File.separator + xmlFileName;
            // 创建导出任务
            exportTask = createExportTask(xmlFileName);
            if (CollectionUtils.isEmpty(calculationScripts.getCalculationScripts())) {
                printExceptionLog(exportTask, logPath,
                        new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPTS_NULL));
                return;
            }
            List<CalculationScriptModel> scriptModelList = iCalculationScriptRepo
                    .getCalculationScriptsByList(calculationScripts.getCalculationScripts());
            if (CollectionUtils.isEmpty(scriptModelList)) {
                printExceptionLog(exportTask, logPath,
                        new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPTS_NULL));
                return;
            }
            Document document = createXml(scriptModelList);
            writeDocumentToFile(document, xmlFilePath);
            // 上传EDM，并根据上传结果更新任务状态
            DocumentVO documentVO = edmService.edmUploadFile(xmlFilePath);
            if (documentVO != null && StringUtils.isNotBlank(documentVO.getDocId())) {
                setExportTask(exportTask, documentVO, scriptModelList.size(), ExportTaskEnum.SUCCESS.getCode());
            } else {
                setExportTask(exportTask, null, 0, ExportTaskEnum.FAIL.getCode());
            }
            // 导出完成后获取URL并更新任务
            exportTaskService.updateExportTask(exportTask);
        } catch (Throwable ex) {
            LOGGER.error("error: ", ex);
            printExceptionLog(exportTask, logPath, ex);
        } finally {
            deleteTempFile(xmlFilePath);
        }
    }

    private ExportTask createExportTask(String filePathName) {
        ExportTask exportTask = new ExportTask();
        exportTask.setStatus(ExportTaskEnum.RUN.getCode());
        exportTask.setFileName(filePathName);
        exportTask.setCreatedBy(UserUtil.getUser(CalculationScriptConstants.USERID));
        exportTask.setModuleName(ModuleEnum.RULE.getMessage());
        exportTask.setRunStartTime(LocalDateTime.now());
        exportTaskService.createExportTask(exportTask);
        return exportTask;
    }

    private void setExportTask(ExportTask exportTask, DocumentVO documentVO, int records, String status) {
        if (documentVO != null) {
            exportTask.setDocId(documentVO.getDocId());
            String docSize = documentVO.getDocSize();
            exportTask.setFileSize(StringUtils.isNotBlank(docSize) ? Long.parseLong(docSize) : 0);
        }
        exportTask.setRecords(records);
        exportTask.setStatus(status);
        exportTask.setRunEndTime(LocalDateTime.now());
    }

    private Document createXml(List<CalculationScriptModel> calculationScriptModels) {
        List<ScriptFolderVO> scriptFolderVoList = iScriptFolderRepo
                .findScriptFolderVoListByCodeList(calculationScriptModels);
        Map<String, ScriptFolderVO> scriptFolderMap = scriptFolderVoList.stream()
                .collect(Collectors.toMap(ScriptFolderVO::getFolderCode, each -> each, (value1, value2) -> value1));
        Document document = DocumentHelper.createDocument();
        Element scripts = document.addElement(CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPTS);
        for (CalculationScriptModel calculationScriptModel : calculationScriptModels) {
            Element script = scripts.addElement(CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPT);
            script.addElement(CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPT_NAME)
                    .addText(calculationScriptModel.getScriptName());
            ScriptFolderVO scriptFolderVO = scriptFolderMap.get(calculationScriptModel.getFolderCode());
            String pathAndName = stringIsEmpty(scriptFolderVO.getFolderPath())
                    ? scriptFolderVO.getFolderName()
                    : scriptFolderVO.getFolderPath() + scriptFolderVO.getFolderName();
            script.addElement(CalculationScriptConstants.CALCULATION_SCRIPT_XML_FOLDER_NAME).addText(pathAndName);
            script.addElement(CalculationScriptConstants.CALCULATION_SCRIPT_XML_MODEL_NAME)
                    .addText(calculationScriptModel.getModelName());
            script.addElement(CalculationScriptConstants.CALCULATION_SCRIPT_XML_BUSINESS_VERSION_ID)
                    .addText(parseVersion(calculationScriptModel.getBusinessVersionId()));
            script.addElement(CalculationScriptConstants.CALCULATION_SCRIPT_XML_VERSION_ID)
                    .addText(parseVersion(calculationScriptModel.getVersionId()));
            script.addElement(CalculationScriptConstants.CALCULATION_SCRIPT_XML_DEPLOYED_DATE)
                    .addText(dateToString(calculationScriptModel.getDeployedDate()));
            script.addElement(CalculationScriptConstants.CALCULATION_SCRIPT_XML_CONTENT)
                    .addCDATA(calculationScriptModel.getContent());
        }
        return document;
    }

    /**
     * 创建格式化类、xml输出流、生成xml文件
     * 如果发生异常，需要删除临时文件，避免docker容器积攒垃圾文件
     *
     * @param document document
     * @param fileAbsoluteFile fileAbsoluteFile
     */
    private void writeDocumentToFile(Document document, String fileAbsoluteFile) {
        FileOutputStream fos = null;
        XMLWriter writer = null;
        try {
            OutputFormat format = OutputFormat.createPrettyPrint();
            format.setEncoding("UTF-8");
            fos = new FileOutputStream(fileAbsoluteFile);
            writer = new XMLWriter(fos, format);
            writer.write(document);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        } finally {
            try {
                if (writer != null) {
                    writer.close();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException ioe) {
                LOGGER.error(ioe.getMessage());
            }
            deleteTempFile(fileAbsoluteFile);
        }
    }

    /**
     * 当导入规则XML文件过程发生异常时，执行本方法，将异常信息，写入到txt文件并上传到EDM中，展示到个人中心
     *
     * @param exportTask exportTask
     * @param logPath logPath
     * @param ex ex
     */
    private void printExceptionLog(ExportTask exportTask, String logPath, Throwable ex) {
        PathUtil.makeDirs(logPath);
        String logAbsolutePath = String.format(Locale.ROOT, "%s%s%s%s", logPath, File.separator,
                System.currentTimeMillis(), CalculationScriptConstants.CALCULATION_SCRIPT_EXPORT_LOG_FORMAT);
        File logFile = UsFileLiteUtils.getFile(logAbsolutePath);
        DocumentVO documentVO = uploadLogFileToEdm(logFile, ex, null);
        if (documentVO != null && StringUtils.isNotBlank(documentVO.getDocId())) {
            setExportTask(exportTask, documentVO, 1, ExportTaskEnum.FAIL.getCode());
        } else {
            setExportTask(exportTask, null, 0, ExportTaskEnum.FAIL.getCode());
        }
        exportTaskService.updateExportTask(exportTask);
    }

    private void deleteTempFile(String filePath) {
        File tmpFile = UsFileLiteUtils.getFile(filePath);
        deleteTempFile(tmpFile);
    }

    private void deleteTempFile(File tmpFile) {
        if (tmpFile.exists()) {
            tmpFile.deleteOnExit();
        }
    }

    private String parseVersion(Long versionId) {
        return (versionId == null ? "" : versionId.toString());
    }

    /**
     * 规则脚本导入功能
     * 1、文件写入指定路径 transferTo
     * 2、根据XML文件的内容，进行解析 parseXmlFileToCalculationScriptModels
     * 3、根据名称信息查询ID等信息，并且同时进行校验，如无错误则数据补齐 checkCalculationScriptModels
     * ，如果有错误终止并上传报错信息
     * 4、执行入库
     * 5、最终需要删除上传的临时文件
     * 6. 暂不用异步方式
     *
     * @param file file
     * @param importTask importTask
     * @param calculationScriptsImport calculationScriptsImport
     * @param workspaceId 作业空间ID
     */
    @Override
    public void importCalculationScripts(MultipartFile file, ImportTask importTask,
            CalculationScriptsImport calculationScriptsImport, long workspaceId) {
        PathUtil.makeDirs(calculationScriptsImport.getXmlBasePath());
        File dest = UsFileLiteUtils.getFile(
                calculationScriptsImport.getXmlBasePath() + File.separator + calculationScriptsImport.getXmlFileName());
        try {
            file.transferTo(dest);
            CalculationScriptModelListChecker calculationScriptModelListChecker = parseXmlFileToCalculationScriptModels(
                    dest, workspaceId);
            // 校验
            if (!checkCalculationScriptModelList(calculationScriptModelListChecker, importTask,
                    calculationScriptsImport, workspaceId)) {
                return;
            }
            List<CalculationScriptModel> calculationScriptModels = calculationScriptModelListChecker
                    .getCalculationScriptModels();
            RequestContext.getCurrent(true).setItem(CalculationScriptConstants.OBJECT_NAME, calculationScriptModels
                    .stream().map(CalculationScript::getScriptName).collect(Collectors.toList()));
            iCalculationScriptRepo.saveCalculationScriptModels(calculationScriptModels);
            // 上传文件到个人中心
            DocumentVO documentVO = edmService.edmUploadFile(dest.getCanonicalPath());
            // 更新任务状态
            updateImportTask(importTask, documentVO.getDocId(), ExportTaskEnum.SUCCESS.getCode(), file.getSize(),
                    calculationScriptModels.size());
        } catch (IOException | DocumentException | EdmException | SAXException | ParseException e) {
            LOGGER.error("An exception occurred when importing calculation scripts .", e);
            printImportLogAndUpdateTask(importTask, calculationScriptsImport, e, null);
        } finally {
            deleteTempFile(dest);
        }
    }

    private boolean checkCalculationScriptModelList(CalculationScriptModelListChecker calculationScriptModelListChecker,
            ImportTask importTask, CalculationScriptsImport calculationScriptsImport, long workspaceId) {
        // 校验xml各个tag是否完整
        if (calculationScriptModelListChecker.getMissingTag() != null) {
            String extraInfo = String.format(Locale.ROOT, "%s%s%s",
                    CalculationScriptConstants.CALCULATION_SCRIPT_XML_TAG_MISSING, ":",
                    calculationScriptModelListChecker.getMissingTag());
            printImportLogAndUpdateTask(importTask, calculationScriptsImport,
                    new CalculationException(PubErrorCode.PUB_CAL_ERROR_UPLOAD_XML_PARSE_FAIL), extraInfo);
            return false;
        }
        // 校验folderName标签是否有非法字符、长度是否过大
        if (calculationScriptModelListChecker.getInvalidFolderName() != null) {
            String extraInfo = String.format(Locale.ROOT, "%s%s%s",
                    CalculationScriptConstants.CALCULATION_SCRIPT_XML_FOLDER_NAME_INVALID, ":",
                    calculationScriptModelListChecker.getInvalidFolderName());
            printImportLogAndUpdateTask(importTask, calculationScriptsImport,
                    new CalculationException(PubErrorCode.PUB_CAL_ERROR_UPLOAD_XML_PARSE_FAIL), extraInfo);
            return false;
        }
        // 校验scriptName标签是否有非法字符、长度是否过大
        if (calculationScriptModelListChecker.getInvalidScriptName() != null) {
            String extraInfo = String.format(Locale.ROOT, "%s%s%s",
                    CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPT_NAME_INVALID, ":",
                    calculationScriptModelListChecker.getInvalidScriptName());
            printImportLogAndUpdateTask(importTask, calculationScriptsImport,
                    new CalculationException(PubErrorCode.PUB_CAL_ERROR_UPLOAD_XML_PARSE_FAIL), extraInfo);
            return false;
        }
        // 校验模型是否存在，规则名称是否有重复
        List<CalculationScriptModel> calculationScriptModels = calculationScriptModelListChecker
                .getCalculationScriptModels();
        if (!checkCalculationScriptModels(calculationScriptModels, importTask, calculationScriptsImport, workspaceId)) {
            return false;
        }
        return true;
    }

    private CalculationScriptModelListChecker parseXmlFileToCalculationScriptModels(File xmlFile, long workspaceId)
            throws FileNotFoundException, DocumentException, SAXException, ParseException {
        CalculationScriptModelListChecker checker = new CalculationScriptModelListChecker();
        List<CalculationScriptModel> calculationScriptModels = new ArrayList<>();
        SAXReader saxReader = initSAXReaderTemplate();
        Document document = saxReader.read(xmlFile);
        Element rootElement = document.getRootElement();
        if (!CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPTS
                .equals(rootElement.getName().toLowerCase(Locale.ROOT))) {
            checker.setMissingTag(CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPTS);
            return checker;
        }
        List<Element> scriptList = rootElement.elements();
        for (Element script : scriptList) {
            CalculationScriptModelListChecker xmlChecker = checkXmlScriptElement(script);
            if (xmlChecker != null) {
                return xmlChecker;
            }
            calculationScriptModels.add(buildCalculationScriptModel(script, workspaceId));
        }
        checker.setCalculationScriptModels(calculationScriptModels);
        return checker;
    }

    private CalculationScriptModel buildCalculationScriptModel(Element script, long workspaceId) throws ParseException {
        CalculationScriptModel model = new CalculationScriptModel();
        model.setScriptName(script.element(CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPT_NAME).getText());
        String folderName = script.element(CalculationScriptConstants.CALCULATION_SCRIPT_XML_FOLDER_NAME).getText();
        model.setFolderName(folderName);
        model.setModelName(script.element(CalculationScriptConstants.CALCULATION_SCRIPT_XML_MODEL_NAME).getText());
        String businessVersionId = script.element(CalculationScriptConstants.CALCULATION_SCRIPT_XML_BUSINESS_VERSION_ID)
                .getText();
        if (!StringUtils.isEmpty(businessVersionId)) {
            model.setBusinessVersionId(Long.parseLong(businessVersionId));
        } else {
            model.setBusinessVersionId(CalculationScriptConstants.CALCULATION_SCRIPT_DEFAULT_BUSINESS_VERSION);
        }
        String versionId = script.element(CalculationScriptConstants.CALCULATION_SCRIPT_XML_VERSION_ID).getText();
        if (!StringUtils.isEmpty(versionId)) {
            model.setVersionId(Long.parseLong(versionId));
        } else {
            model.setVersionId(CalculationScriptConstants.CALCULATION_SCRIPT_DEFAULT_VERSION);
        }
        if (script.element(CalculationScriptConstants.CALCULATION_SCRIPT_XML_DEPLOYED_DATE) != null) {
            String deployedDate = script.element(CalculationScriptConstants.CALCULATION_SCRIPT_XML_DEPLOYED_DATE)
                    .getText();
            if (!StringUtils.isEmpty(deployedDate)) {
                model.setDeployedDate(stringToDate(deployedDate));
            }
        }
        model.setContent(script.element(CalculationScriptConstants.CALCULATION_SCRIPT_XML_CONTENT).getText());
        model.setWorkspaceId(workspaceId);
        return model;
    }

    private CalculationScriptModelListChecker checkScriptName(String scriptName) {
        CalculationScriptModelListChecker checker = new CalculationScriptModelListChecker();
        if (isScriptNameInvalidate(scriptName)) {
            checker.setInvalidScriptName(scriptName);
            return checker;
        }
        return null;
    }

    /**
     * 新增文件夹名称校验，按照A//B//C来进行校验。
     */
    private CalculationScriptModelListChecker checkFolderName(String folderName) {
        CalculationScriptModelListChecker checker = new CalculationScriptModelListChecker();
        if (!folderName.contains(CalculationScriptConstants.SCRIPT_FOLDER_PATH_SPLIT)
                && isFolderNameInvalidate(folderName)) {
            checker.setInvalidFolderName(folderName);
            return checker;
        }
        if (folderName.contains(CalculationScriptConstants.SCRIPT_FOLDER_PATH_SPLIT)) {
            String[] folderNames = folderName.split(CalculationScriptConstants.SCRIPT_FOLDER_PATH_SPLIT);
            if (folderNames.length > CalculationScriptConstants.SCRIPT_FOLDER_MAX_LEVEL) {
                checker.setInvalidFolderName(folderName);
                return checker;
            }
            for (String name : folderNames) {
                if (isFolderNameInvalidate(name)) {
                    checker.setInvalidFolderName(folderName);
                    return checker;
                }
            }
        }
        return null;
    }

    /**
     * 文件夹名称不能超过30字符，且不允许有正反斜杠
     */
    private boolean isFolderNameInvalidate(String folderName) {
        return folderName.length() > CalculationScriptConstants.SCRIPT_FOLDER_NAME_MAX_LENGTH
                || folderName.contains(CalculationScriptConstants.SCRIPT_FOLDER_FORWARD_SLASH)
                || folderName.contains(CalculationScriptConstants.SCRIPT_FOLDER_BACK_SLASH);
    }

    private boolean isScriptNameInvalidate(String scriptName) {
        return scriptName.length() > CalculationScriptConstants.CALCULATION_SCRIPT_NAME_MAX_LENGTH
                || scriptName.contains(CalculationScriptConstants.SCRIPT_FOLDER_FORWARD_SLASH)
                || scriptName.contains(CalculationScriptConstants.SCRIPT_FOLDER_BACK_SLASH);
    }

    private ScriptFolder createFolderWhenFolderNameNotExists(String folderName, long workspaceId, String parentCode) {
        ScriptFolder folder = new ScriptFolder();
        folder.setFolderCnName(folderName);
        folder.setWorkspaceId(workspaceId);
        List<ScriptFolder> folders = iScriptFolderRepo.findScriptFoldersByName(folder);
        if (!folders.isEmpty()) {
            // 如果根据名称可以查出不止一个，根据parentCode对比，寻找出适合的对象返回
            for (ScriptFolder subFolder : folders) {
                if (subFolder.getParentCode().equals(parentCode)) {
                    return subFolder;
                }
            }
        }
        // 否则创建一个新的文件夹对象
        return createFolder(folder, parentCode);
    }

    private ScriptFolder createFolder(ScriptFolder folder, String parentCode) {
        folder.setFolderCode(IdUtil.getUUID());
        folder.setParentCode(parentCode);
        iScriptFolderRepo.saveScriptFolder(folder);
        List<ScriptFolder> folders = iScriptFolderRepo.findScriptFoldersByName(folder);
        return folders.stream().findFirst().get();
    }

    private SAXReader initSAXReaderTemplate() throws SAXException {
        SAXReader saxReader = new SAXReader();
        saxReader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        saxReader.setFeature("http://xml.org/sax/features/external-general-entities", false);
        saxReader.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
        return saxReader;
    }

    private CalculationScriptModelListChecker checkXmlScriptElement(Element script) {
        CalculationScriptModelListChecker checker = new CalculationScriptModelListChecker();
        if (!CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPT
                .equals(script.getName().toLowerCase(Locale.ROOT))) {
            checker.setMissingTag(CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPT);
            return checker;
        }
        if (isTagNullOrTextNull(script, CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPT_NAME)) {
            checker.setMissingTag(CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPT_NAME);
            return checker;
        }
        if (isTagNullOrTextNull(script, CalculationScriptConstants.CALCULATION_SCRIPT_XML_FOLDER_NAME)) {
            checker.setMissingTag(CalculationScriptConstants.CALCULATION_SCRIPT_XML_FOLDER_NAME);
            return checker;
        }
        if (isTagNullOrTextNull(script, CalculationScriptConstants.CALCULATION_SCRIPT_XML_MODEL_NAME)) {
            checker.setMissingTag(CalculationScriptConstants.CALCULATION_SCRIPT_XML_MODEL_NAME);
            return checker;
        }
        if (isTagNull(script, CalculationScriptConstants.CALCULATION_SCRIPT_XML_BUSINESS_VERSION_ID)) {
            checker.setMissingTag(CalculationScriptConstants.CALCULATION_SCRIPT_XML_BUSINESS_VERSION_ID);
            return checker;
        }
        if (isTagNull(script, CalculationScriptConstants.CALCULATION_SCRIPT_XML_VERSION_ID)) {
            checker.setMissingTag(CalculationScriptConstants.CALCULATION_SCRIPT_XML_VERSION_ID);
            return checker;
        }
        if (isTagNull(script, CalculationScriptConstants.CALCULATION_SCRIPT_XML_CONTENT)) {
            checker.setMissingTag(CalculationScriptConstants.CALCULATION_SCRIPT_XML_CONTENT);
            return checker;
        }
        String folderName = script.element(CalculationScriptConstants.CALCULATION_SCRIPT_XML_FOLDER_NAME).getText();
        CalculationScriptModelListChecker folderNameChecker = checkFolderName(folderName);
        if (folderNameChecker != null) {
            return folderNameChecker;
        }
        String scriptName = script.element(CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPT_NAME).getText();
        CalculationScriptModelListChecker scriptNameChecker = checkScriptName(scriptName);
        if (scriptNameChecker != null) {
            return scriptNameChecker;
        }
        return null;
    }

    private boolean isTagNull(Element script, String elementName) {
        if (script.element(elementName) == null) {
            return true;
        }
        return false;
    }

    private boolean isTagNullOrTextNull(Element script, String elementName) {
        if (isTagNull(script, elementName)) {
            return true;
        }
        String text = script.element(elementName).getText();
        return text == null || (text.trim().isEmpty());
    }

    /**
     * 校验规则：
     * 1、模型名称是否存在，如果不存在，抛出异常。如果存在，取其ID
     * 2、文件夹名称是否存在，如果不存在，则进行自动创建文件夹逻辑。
     * 3、规则名称不存在也没关系。如果存在，因为规则名称需全局唯一，所以需要判断这个规则是否隶属于同一个文件夹，
     * 如果是同文件夹内的重名规则，则覆盖内容，否则，提示规则重名，在xx文件夹已存在
     *
     * @param calculationScriptModels calculationScriptModels
     * @param importTask importTask
     * @param calculationScriptsImport calculationScriptsImport
     * @return boolean
     */
    private boolean checkCalculationScriptModels(List<CalculationScriptModel> calculationScriptModels,
            ImportTask importTask, CalculationScriptsImport calculationScriptsImport, long workspaceId) {
        String duplicateScriptName = checkDuplicateScriptNameInXml(calculationScriptModels);
        if (duplicateScriptName != null) {
            String extraInfo = String.format(Locale.ROOT, "%s%s%s",
                    CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPT_NAME, ":", duplicateScriptName);
            printImportLogAndUpdateTask(importTask, calculationScriptsImport,
                    new CalculationException(PubErrorCode.PUB_CAL_ERROR_UPLOAD_XML_NAME_DUPLICATE), extraInfo);
            return false;
        }
        for (CalculationScriptModel calculationScriptModel : calculationScriptModels) {
            // 校验模型名称
            String modelName = calculationScriptModel.getModelName();
            List<RelativeModel> allRelativeModels = findRelativeModels(workspaceId, null,
                    modelName);
            if (allRelativeModels.size() != 1) {
                String extraInfo = String.format(Locale.ROOT, "%s%s%s",
                        CalculationScriptConstants.CALCULATION_SCRIPT_XML_MODEL_NAME, ":", modelName);
                printImportLogAndUpdateTask(importTask, calculationScriptsImport,
                        new CalculationException(PubErrorCode.PUB_CAL_ERROR_MODEL_NAME_ILLEGAL), extraInfo);
                return false;
            }
            calculationScriptModel.setModelId(allRelativeModels.stream().findFirst().get().getModelId());
            // 校验文件夹名称，若不存在自动创建文件夹
            String folderName = calculationScriptModel.getFolderName();
            setCalculationScriptModelFolderInfo(folderName, calculationScriptModel, workspaceId);
            String scriptName = calculationScriptModel.getScriptName();
            String extraInfo = String.format(Locale.ROOT, "%s%s%s",
                    CalculationScriptConstants.CALCULATION_SCRIPT_XML_SCRIPT_NAME, ":", scriptName);
            // 校验规则名称长度
            if (scriptName.length() > CalculationScriptConstants.CALCULATION_SCRIPT_NAME_MAX_LENGTH) {
                printImportLogAndUpdateTask(importTask, calculationScriptsImport,
                        new CalculationException(PubErrorCode.PUB_CAL_ERROR_CALCULATION_SCRIPT_NAME_TOO_LONG),
                        extraInfo);
                return false;
            }
            // 查询规则名称
            CalculationScript script = new CalculationScript();
            script.setScriptName(scriptName);
            script.setWorkspaceId(calculationScriptModel.getWorkspaceId());
            List<CalculationScript> calculationScripts = iCalculationScriptRepo.getCalculationScripts(script);
            if (calculationScripts.size() > 1) {
                printImportLogAndUpdateTask(importTask, calculationScriptsImport,
                        new CalculationException(PubErrorCode.PUB_CAL_ERROR_UPLOAD_XML_NAME_DUPLICATE), extraInfo);
                return false;
            }
            if (calculationScripts.size() == 1) {
                CalculationScript sameNameScript = calculationScripts.stream().findFirst().get();
                if (calculationScriptModel.getFolderCode().equals(sameNameScript.getFolderCode())) {
                    calculationScriptModel.setScriptId(calculationScripts.stream().findFirst().get().getScriptId());
                } else {
                    printImportLogAndUpdateTask(importTask, calculationScriptsImport,
                            new CalculationException(PubErrorCode.PUB_CAL_ERROR_UPLOAD_XML_NAME_DUPLICATE), extraInfo);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * folderName 有里面为1-3级的文件夹，以//分割，
     * 注意判断:该作业空间下，是否存在同名的文件夹，如果不存在则创建，依次将整个路径各个文件夹校验完毕，
     * 并将对应的文件夹code给calculationScriptModel
     *
     * @param folderName
     * @param calculationScriptModel
     * @param workspaceId
     */
    private void setCalculationScriptModelFolderInfo(String folderName, CalculationScriptModel calculationScriptModel,
            long workspaceId) {
        // 无分隔符的直接设置文件夹信息，否则逐层查询和创建对应文件夹，直至最后一层
        if (!folderName.contains(CalculationScriptConstants.SCRIPT_FOLDER_PATH_SPLIT)) {
            ScriptFolder scriptFolder = createFolderWhenFolderNameNotExists(folderName, workspaceId,
                    CalculationScriptConstants.DEFAULT_PARENT_CODE);
            calculationScriptModel.setFolderCode(scriptFolder.getFolderCode());
            calculationScriptModel.setFolderId(scriptFolder.getFolderId());
        } else {
            String[] folderNames = folderName.split(CalculationScriptConstants.SCRIPT_FOLDER_PATH_SPLIT);
            List<ScriptFolder> folders = new ArrayList<>();
            ScriptFolder lastFolder = new ScriptFolder();
            for (int i = 0; i < folderNames.length; i++) {
                String parentCode = (i == 0
                        ? CalculationScriptConstants.DEFAULT_PARENT_CODE
                        : folders.get(i - 1).getFolderCode());
                ScriptFolder folder = createFolderWhenFolderNameNotExists(folderNames[i], workspaceId, parentCode);
                folders.add(folder);
                if (i == folderNames.length - 1) {
                    lastFolder = folder;
                }
            }
            calculationScriptModel.setFolderCode(lastFolder.getFolderCode());
            calculationScriptModel.setFolderId(lastFolder.getFolderId());
        }
    }

    private String checkDuplicateScriptNameInXml(List<CalculationScriptModel> calculationScriptModels) {
        Set<String> folderScriptNameSet = new HashSet<>();
        for (CalculationScriptModel calculationScriptModel : calculationScriptModels) {
            String scriptName = calculationScriptModel.getScriptName();
            if (folderScriptNameSet.contains(scriptName)) {
                // 查询到同一个XML文件内，存在两个重复名称的规则，需要提示错误
                return scriptName;
            } else {
                folderScriptNameSet.add(scriptName);
            }
        }
        return null;
    }

    private void updateImportTask(ImportTask importTask, String docId, String status, long size, int records) {
        importTask.setDocId(docId);
        importTask.setFileSize(size);
        importTask.setStatus(status);
        importTask.setRunEndTime(LocalDateTime.now());
        importTask.setRecords(records);
        importTaskAppService.updateImportTask(importTask);
    }

    /**
     * 规则导入导出打印错误日志功能
     *
     * @param importTask 导入任务
     * @param calculationScriptsImport 导入参数实体
     * @param ex 异常信息
     * @param extraInfo 异常额外信息，用于定位错误数据的位置
     */
    @Override
    public void printImportLogAndUpdateTask(ImportTask importTask, CalculationScriptsImport calculationScriptsImport,
            Throwable ex, String extraInfo) {
        LOGGER.error("A Throwable exception occurred .", ex);
        PathUtil.makeDirs(calculationScriptsImport.getLogPath());
        String logAbsolutePath = String.format(Locale.ROOT, "%s%s%s", calculationScriptsImport.getLogPath(),
                File.separator, calculationScriptsImport.getLogFileName());
        File logFile = UsFileLiteUtils.getFile(logAbsolutePath);
        DocumentVO documentVO = uploadLogFileToEdm(logFile, ex, extraInfo);
        if (documentVO != null && StringUtils.isNotBlank(documentVO.getDocId())) {
            updateImportTask(importTask, documentVO.getDocId(), ExportTaskEnum.FAIL.getCode(), documentVO.getFileSize(),
                    1);
        } else {
            updateImportTask(importTask, null, ExportTaskEnum.FAIL.getCode(), 0, 0);
        }
    }

    /**
     * 把错误日志上传edm功能
     *
     * @param logFile 日志文件
     * @param ex 异常信息
     * @param extraInfo 异常额外信息，用于定位错误数据的位置
     * @return DocumentVO
     */
    private DocumentVO uploadLogFileToEdm(File logFile, Throwable ex, String extraInfo) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(logFile, true))) {
            if (ex.getClass().equals(CalculationException.class)) {
                PubErrorCode errorCode = ((CalculationException) ex).getCalculationErrorCode();
                writer.println(errorCode.getErrorMessage());
            } else {
                writer.println(PubErrorCode.PUB_ERROR_INTERNAL_ERROR.getErrorMessage());
            }
            if (extraInfo != null) {
                writer.println(System.lineSeparator());
                writer.println(extraInfo);
            }
            writer.flush();
            return edmService.edmUploadFile(logFile.getCanonicalPath());
        } catch (IOException | EdmException e) {
            LOGGER.error("An exception occurred when printing logs.", e);
        } finally {
            deleteTempFile(logFile);
        }
        return null;
    }

    /**
     * 根据多个规则ID，批量查询对应的多个脚本信息及关联的模型、文件夹等信息。
     */
    @Override
    public List<CalculationScriptModel> getCalculationScriptsByList(List<CalculationScript> calculationScripts) {
        return iCalculationScriptRepo.getCalculationScriptsByList(calculationScripts);
    }

    @Override
    public boolean isCalculationScriptDeploying(long scriptId, long workspaceId) {
        CalculationScript calculationScript = new CalculationScript();
        calculationScript.setScriptId(scriptId);
        calculationScript.setWorkspaceId(workspaceId);
        String status = iCalculationScriptRepo.queryScriptStatus(calculationScript);
        return CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_DEPLOYING.equals(status);
    }

    private String dateToString(Date date) {
        if (date == null) {
            return "";
        }
        DateFormat dateFormat = new SimpleDateFormat(CalculationScriptConstants.CALCULATION_SCRIPT_EXPORT_TIME_FORMAT);
        dateFormat.setTimeZone(CalculationScriptConstants.CALCULATION_SCRIPT_EXPORT_TIME_ZONE);
        return dateFormat.format(date);
    }

    private Date stringToDate(String str) throws ParseException {
        DateFormat dateFormat = new SimpleDateFormat(CalculationScriptConstants.CALCULATION_SCRIPT_EXPORT_TIME_FORMAT);
        dateFormat.setTimeZone(CalculationScriptConstants.CALCULATION_SCRIPT_EXPORT_TIME_ZONE);
        return dateFormat.parse(str);
    }

    /**
     * 查询某个作业空间下，与某些模型相关联的规则列表
     */
    @Override
    public List<CalculationScriptModel> getCalculationScriptsByModelIds(List<String> modelIds, long workspaceId) {
        return iCalculationScriptRepo.getCalculationScriptsByModelIds(modelIds, workspaceId);
    }

    /**
     * 查询某个作业空间下，所有的规则列表及相关跨模型列表
     */
    @Override
    public List<CalculationScriptRelativeModels> findALlScriptsWithRelativeModelsByWorkspaceId(long workspaceId) {
        List<CalculationScriptModel> allScripts = getCalculationScriptsByModelIds(Collections.EMPTY_LIST, workspaceId);
        List<RelativeModel> allRelativeModels = findRelativeModels(workspaceId, null, null);
        List<CalculationScriptRelativeModels> allScriptsWithRelativeModels = new ArrayList<>();
        for (CalculationScriptModel script : allScripts) {
            String content = script.getContent();
            if (content != null
                    && content.toLowerCase(Locale.ROOT).contains(CalculationScriptConstants.MODEL_FUNCTION_PREFIX)) {
                // 说明这个规则包含了跨模型的关键信息， 需要从内容中解析出相关的模型列表
                List<RelativeModel> relativeModels = getRelativeModelsByReadingContent(content, allRelativeModels);
                allScriptsWithRelativeModels.add(new CalculationScriptRelativeModels(script, relativeModels));
            } else {
                allScriptsWithRelativeModels.add(new CalculationScriptRelativeModels(script, Collections.EMPTY_LIST));
            }
        }
        return allScriptsWithRelativeModels;
    }

    private List<RelativeModel> getRelativeModelsByReadingContent(String content,
            List<RelativeModel> allRelativeModels) {
        List<RelativeModel> subRelativeModels = new ArrayList<>();
        Map<String, String> uniqueMap = new HashMap<>();
        String[] subContents = content.split(CalculationScriptConstants.MODEL_FUNCTION_KEYWORDS);
        for (int i = 1; i < subContents.length; i++) {
            String sub = subContents[i];
            String modelName = "";
            int startPosition = -1;
            int endPosition = -1;
            if (sub != null && sub.length() > 0) {
                for (int j = 0; j < sub.length(); j++) {
                    char currentChar = sub.charAt(j);
                    if ('\'' == currentChar) {
                        startPosition = j;
                        break;
                    }
                }
                for (int j = startPosition + 1; j < sub.length(); j++) {
                    char currentChar = sub.charAt(j);
                    if (',' == currentChar) {
                        endPosition = j;
                        break;
                    }
                }
                modelName = sub.substring(startPosition + 1, endPosition - 1);
            }
            if (!uniqueMap.containsKey(modelName)) {
                uniqueMap.put(modelName, CalculationScriptConstants.UNIQUE_MARKER);
                for (RelativeModel relativeModel : allRelativeModels) {
                    if (relativeModel.getModelName().equals(modelName)) {
                        subRelativeModels.add(relativeModel);
                    }
                }
            }
        }
        return subRelativeModels;
    }

    /**
     * 查询某个作业空间下，所有指定模型的规则列表
     * 如果规则内容包含了跨模型信息，且所选模型列表没有，则返回值不包含该规则
     *
     * @param calculationScript 计算脚本
     * @return List<CalculationScript>
     */
    @Override
    public List<CalculationScript> getCalculationScripts(CalculationScript calculationScript, List<String> modelIds) {
        List<CalculationScript> filteredScripts = new ArrayList<>();
        List<CalculationScriptModel> allScripts = getCalculationScriptsByModelIds(modelIds,
                calculationScript.getWorkspaceId());
        List<RelativeModel> allRelativeModels = findRelativeModels(calculationScript.getWorkspaceId(), null, null);
        for (CalculationScriptModel script : allScripts) {
            String content = script.getContent();
            if (content != null
                    && content.toLowerCase(Locale.ROOT).contains(CalculationScriptConstants.MODEL_FUNCTION_PREFIX)) {
                // 说明这个规则包含了跨模型的关键信息， 需要从内容中解析出相关的模型列表
                List<RelativeModel> relativeModels = getRelativeModelsByReadingContent(content, allRelativeModels);
                if (relativeModels.isEmpty()) {
                    filteredScripts.add(script);
                } else if (scriptRelativeModelsBelongToSelectModels(relativeModels, modelIds)) {
                    // 如果relativeModels 是所选择模型列表的一个子集，则添加到filteredScripts中
                    filteredScripts.add(script);
                }
            } else {
                filteredScripts.add(script);
            }
        }
        return filteredScripts;
    }

    private boolean scriptRelativeModelsBelongToSelectModels(List<RelativeModel> relativeModels,
            List<String> modelIds) {
        for (RelativeModel relativeModel : relativeModels) {
            boolean contains = false;
            for (String modelId : modelIds) {
                if (modelId.equals(relativeModel.getModelId())) {
                    contains = true;
                    break;
                }
            }
            if (!contains) {
                return false;
            }
        }
        return true;
    }

    /**
     * 部署空规则时，按照作业空间id和规则脚本ID，删除对应的计算对象，包括：
     * 1、CalculationRules
     * 2、Entities
     * 3、Relationships
     *
     * @param scriptId
     * @param workspaceId
     */
    @Override
    public void deleteCalculationObjectsWhileDeployNullScript(long scriptId, long workspaceId) {
        iCalculationRuleRepo.deleteCalculationRulesByScriptId(scriptId, workspaceId);
        CalculationScript calculationScript = new CalculationScript();
        calculationScript.setScriptId(scriptId);
        calculationScript.setWorkspaceId(workspaceId);
        iCalculationToKnowledgeRepo.deleteEntitiesAndRelationships(calculationScript);
    }

    /**
     * 根据多个规则ID，批量查询对应的多个脚本信息及关联的模型、文件夹等信息。
     */
    @Override
    public List<CalculationScriptModel> getSortedCalculationScriptsByList(List<CalculationScript> calculationScripts,
            long workspaceId) {
        return iCalculationScriptRepo.getSortedCalculationScriptsByList(calculationScripts, workspaceId);
    }

    @Override
    public void updateCalculationScriptAndDraft(CalculationScript script) {
        CalculationScriptDraft calculationScriptDraft = new CalculationScriptDraft();
        calculationScriptDraft.setScriptId(script.getScriptId());
        calculationScriptDraft.setWorkspaceId(script.getWorkspaceId());
        List<CalculationScriptDraft> drafts = iCalculationScriptDraftRepo
                .findCalculationScriptDrafts(calculationScriptDraft);
        if (drafts.size() == 1) {
            CalculationScriptDraft draft = drafts.get(0);
            draft.setContent(script.getContent());
            iCalculationScriptRepo.updateCalculationScriptAndDraft(script, draft);
        } else {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_DRAFT_NOT_FOUND);
        }
    }

    /**
     * 解析出DSL规则源码中，包含了关键字 Model('模型名称') SUM('模型名称') 中涉及到的关联模型名称。
     * 因为在同一个作业空间下，模型名称不重复，所以只需要解析出不重名的名称即可
     *
     * @param scriptModel
     * @return
     */
    @Override
    public Set<String> analysisRelatedModels(ScriptModel scriptModel) {
        Set<String> relatedModels = new HashSet<>();
        String modelName = scriptModel.getModelName();
        relatedModels.add(modelName);
        String dslSourceText = scriptModel.getCalculationScript().getContent().toLowerCase(Locale.ROOT);
        if (dslSourceText.contains(CalculationScriptConstants.CALCULATION_ANALYSIS_KEYWORD_MODEL)) {
            relatedModels.addAll(analysisDslByKeyWord(dslSourceText,
                    CalculationScriptConstants.CALCULATION_ANALYSIS_KEYWORD_MODEL_SPLIT));
        }
        if (dslSourceText.contains(CalculationScriptConstants.CALCULATION_ANALYSIS_KEYWORD_SUM)) {
            relatedModels.addAll(analysisDslByKeyWord(dslSourceText,
                    CalculationScriptConstants.CALCULATION_ANALYSIS_KEYWORD_SUM_SPLIT));
        }
        return relatedModels;
    }

    private Set<String> analysisDslByKeyWord(String dslSourceText, String keyword) {
        Set<String> relatedModels = new HashSet<>();
        String[] subString = dslSourceText.split(keyword);
        for (int i = 1; i < subString.length; i++) {
            String modelName = getModelNameBetweenSingleQuotes(subString[i]);
            if (!modelName.equals("")) {
                relatedModels.add(modelName);
            }
        }
        return relatedModels;
    }

    /**
     * 获取一段DSL代码片段中，引用的模型名称
     *
     * @param codeSnippet
     * @return
     */
    private String getModelNameBetweenSingleQuotes(String codeSnippet) {
        if (!StringUtils.isEmpty(codeSnippet)) {
            int firstSingleQuoteIndex = -1;
            int secondSingleQuoteIndex = -1;
            for (int i = 0; i < codeSnippet.length(); i++) {
                char c = codeSnippet.charAt(i);
                if (c == '\'') {
                    if (firstSingleQuoteIndex == -1) {
                        firstSingleQuoteIndex = i;
                    } else if (secondSingleQuoteIndex == -1) {
                        secondSingleQuoteIndex = i;
                    }
                }
                if (firstSingleQuoteIndex >= 0 && secondSingleQuoteIndex > 0
                        && firstSingleQuoteIndex < secondSingleQuoteIndex) {
                    return codeSnippet.substring(firstSingleQuoteIndex + 1, secondSingleQuoteIndex);
                }
            }
        }
        return "";
    }

    /**
     * 刷新任务进度
     *
     * @param quartzTaskInfo
     * @param taskStepDescription
     * @param roughStep
     * @param totalSteps
     */
    @Override
    public void updateTaskProgress(QuartzTaskInfo quartzTaskInfo, String taskStepDescription, int roughStep,
            int totalSteps) {
        iQuartzTaskInfoRepo.updateTaskProgress(quartzTaskInfo, taskStepDescription, roughStep, totalSteps);
    }

    /**
     * 检查作业空间规则名称唯一性。
     *
     * @param calculationScript
     */
    private void checkNameExistsInWorkspace(CalculationScript calculationScript) {
        CalculationScript copyScript = new CalculationScript();
        copyScript.setScriptName(calculationScript.getScriptName());
        copyScript.setWorkspaceId(calculationScript.getWorkspaceId());
        List<CalculationScript> list = iCalculationScriptRepo.getCalculationScripts(copyScript);
        if (!list.isEmpty() && list.size() > 0) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_DUPLICATE_NAME);
        }
    }

    private boolean stringIsEmpty(String s) {
        return (s == null) ? true : (s.trim().isEmpty());
    }
}