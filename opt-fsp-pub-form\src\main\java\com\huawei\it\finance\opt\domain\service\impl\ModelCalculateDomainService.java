/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.domain.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.huawei.it.common.exception.ApplicationException;
import com.huawei.it.finance.opt.application.dto.request.ModelCalculationRequest;
import com.huawei.it.finance.opt.application.dto.response.FormBaseResponse;
import com.huawei.it.finance.opt.base.entity.CalculationRule;
import com.huawei.it.finance.opt.base.entity.Constants;
import com.huawei.it.finance.opt.base.entity.Metric;
import com.huawei.it.finance.opt.base.entity.MetricDataType;
import com.huawei.it.finance.opt.base.entity.MetricGenerateStrategy;
import com.huawei.it.finance.opt.base.exception.CalculationException;
import com.huawei.it.finance.opt.base.service.CalculationRuleService;
import com.huawei.it.finance.opt.base.service.MetricService;
import com.huawei.it.finance.opt.common.enums.FormAppExceptionEnum;
import com.huawei.it.finance.opt.common.exception.FormApplicationException;
import com.huawei.it.finance.opt.domain.repository.IFormDimMemberPropertyRep;
import com.huawei.it.finance.opt.domain.service.IModelCalculateDomainService;
import com.huawei.it.finance.opt.engine.calculate.AviatorCalculationExecutor;
import com.huawei.it.finance.opt.engine.calculate.CalculationContext;
import com.huawei.it.finance.opt.engine.calculate.CalculationExecutor;
import com.huawei.it.finance.opt.engine.calculate.CalculationScope;
import com.huawei.it.finance.opt.engine.calculate.Node;
import com.huawei.it.finance.opt.engine.calculate.NodeBuffer;
import com.huawei.it.finance.opt.engine.calculate.handler.MetricLoadHandler;
import com.huawei.it.finance.opt.fsp.pub.application.request.FormCellInfoRequest;
import com.huawei.it.finance.opt.fsp.pub.application.service.IDataStorageApplicationService;
import com.huawei.it.finance.opt.fsp.pub.common.exception.DataSourceException;
import com.huawei.it.finance.opt.fsp.pub.domain.entity.DimInstance;
import com.huawei.it.finance.opt.task.entity.QuartzTaskInfo;
import com.huawei.it.finance.opt.task.infrastructure.db.repository.IQuartzTaskInfoRepo;
import com.huawei.it.finance.opt.tech.enums.CellTypeEnum;
import com.huawei.it.finance.opt.tech.exception.PubErrorCode;
import com.huawei.it.finance.opt.wrap.entity.CellElement;
import com.huawei.it.finance.opt.wrap.service.CellDimensionService;
import com.huawei.it.finance.pub.dimmodel.application.dto.response.DimModelResponse;
import com.huawei.it.finance.pub.dimmodel.application.dto.response.MyDimensionResponse;
import com.huawei.it.finance.pub.dimmodel.common.util.MapUtil;
import com.huawei.it.finance.pub.dimmodel.domain.entity.MyDimMemberEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 模型计算实现类
 *
 * <AUTHOR>
 * @since 2024-07-16 11:35
 */
@Service
@Slf4j
@DS("#mysession.dynamicDB")
public class ModelCalculateDomainService implements IModelCalculateDomainService {

    @Value("${model.calculation.partition-size:4000}")
    private static final int MERGE_PARTITION_SIZE = 4000;

    @Autowired
    private CalculationRuleService calculationRuleService;

    @Autowired
    private CalculationExecutor calculationExecutor;

    @Autowired
    private MetricLoadHandler metricLoadHandler;

    @Autowired
    private AviatorCalculationExecutor aviatorCalculationExecutor;

    @Autowired
    private IDataStorageApplicationService dataStorageApplicationService;

    @Autowired
    private CellDimensionService cellDimensionService;

    @Autowired
    private MetricService metricService;

    @Autowired
    private IFormDimMemberPropertyRep iFormDimMemberPropertyRep;

    @Autowired
    private IQuartzTaskInfoRepo iQuartzTaskInfoRepo;

    @Override
    public FormBaseResponse calculate(ModelCalculationRequest modelCalculationRequest)
            throws ApplicationException, DataSourceException {
        // 1、通过模型id查询当前模型下的所有规则
        String modelId = modelCalculationRequest.getModelId();
        Set<String> limitModels = modelCalculationRequest.getLimitModelIds();
        QuartzTaskInfo taskInfo = modelCalculationRequest.getTaskInfo();
        DimModelResponse toCalModel = cellDimensionService.getDimModelResponse(modelId);
        log.info("开始执行模型计算, 模型ID:{}, 模型名称:{}, 限制模型范围:{}, 归属作业空间ID:{}", modelId, toCalModel.getModelName(), limitModels,
                toCalModel.getWorkspaceId());
        long costTime = System.currentTimeMillis();
        // 超大模型可能有内存溢出问题,待优化
        iQuartzTaskInfoRepo.updateTaskProgress(taskInfo, "1.查询规则", 1, 8);
        List<CalculationRule> calculationRules = getCalculationRulesByModelId(modelId);
        if (CollectionUtils.isEmpty(calculationRules)) {
            log.info("modelId:{}, 未获取到计算公式!", modelId);
            iQuartzTaskInfoRepo.updateTaskProgress(taskInfo, "无规则", 1, 1);
            return FormBaseResponse.success();
        }
        log.info("查询规则耗时：{}s, 当前模型的计算规则的size:{}", (System.currentTimeMillis() - costTime) / 1000,
                calculationRules.size());
        // 获取context,获取计算节点的unit_id
        if (limitModels != null) {
            limitModels.add(modelId);
        }
        log.info("根据模型下的全量规则构建计算DAG中..");
        iQuartzTaskInfoRepo.updateTaskProgress(taskInfo, "2.构建DAG", 2, 8);
        NodeBuffer nodeBuffer = getCompletedNodeBuffer(calculationRules, new HashSet<>(), limitModels,
                new NodeBuffer());
        // 查询叶子节点的数据
        log.info("查询子节点数据中..");
        iQuartzTaskInfoRepo.updateTaskProgress(taskInfo, "3.查询节点数据", 3, 8);
        List<Metric> toQueryMetric = metricLoadHandler.getMetricsByLeafNodes(nodeBuffer.getLeafNodes(),
                Collections.emptyMap());
        List<Metric> continueMetric = metricLoadHandler.getMetricByToQueryNodes(nodeBuffer.getContinueUnitIds(),
                Collections.emptyMap());
        log.info("包含continue节点的数据size:{}", continueMetric.size());
        toQueryMetric.addAll(continueMetric);
        Map<String, Metric> filledMetricMapper = metricService.getMetrics(toQueryMetric);
        log.info("计算子节点的数据size:{}", filledMetricMapper.size());
        // 通过unitIds查询子节点的数据
        log.info("准备计算参数..");
        iQuartzTaskInfoRepo.updateTaskProgress(taskInfo, "4.区分Adjust节点", 4, 8);
        Map<String, Metric> metricMap = buildMetricParam(nodeBuffer, filledMetricMapper);
        // 调用计算--中途会创建路径节点的metric
        log.info("执行计算..");
        iQuartzTaskInfoRepo.updateTaskProgress(taskInfo, "5.执行计算", 5, 8);
        calculateParam(nodeBuffer, metricMap);
        log.info("执行指标分组..");
        iQuartzTaskInfoRepo.updateTaskProgress(taskInfo, "6.落库指标分组", 6, 8);
        // todo:不需要再查一次, 在前面就直接分好组?
        Map<String, List<Metric>> group2Metrics = groupNonLeafMetricByModel(nodeBuffer, metricMap);
        iQuartzTaskInfoRepo.updateTaskProgress(taskInfo, "7.数据落库", 7, 8);
        mergeCalculatedData(toCalModel, metricMap, group2Metrics);
        iQuartzTaskInfoRepo.updateTaskProgress(taskInfo, "8.结束", 8, 8);
        log.info("计算整个模型:{} 耗时：{}s", toCalModel.getModelName(), (System.currentTimeMillis() - costTime) / 1000);
        return FormBaseResponse.success();
    }

    private Map<String, List<Metric>> groupNonLeafMetricByModel(NodeBuffer nodeBuffer, Map<String, Metric> metricMap) {
        Set<String> leafUnitIdStr = nodeBuffer.getLeafNodes().values().stream().map(Node::getKey)
                .collect(Collectors.toSet());
        List<Metric> toGroupModelMetrics = new ArrayList<>();
        for (Map.Entry<String, Metric> entry : metricMap.entrySet()) {
            Metric metric = entry.getValue();
            // adjust不需要保存数据
            if (MetricGenerateStrategy.ADJUST.equals(metric.getMetricGenerateStrategy())) {
                continue;
            }
            // 叶子节点不需要保存数据, 以免覆盖了计算过程中保存的数据
            if (!leafUnitIdStr.contains(entry.getKey())) {
                toGroupModelMetrics.add(metric);
            }
        }
        return metricService.groupMetricsByModelId(toGroupModelMetrics);
    }

    private void mergeCalculatedData(DimModelResponse toCalModel, Map<String, Metric> metricMap,
            Map<String, List<Metric>> group2Metrics) throws DataSourceException, ApplicationException {
        for (Map.Entry<String, List<Metric>> entry : group2Metrics.entrySet()) {
            String toSaveModelId = entry.getKey();
            DimModelResponse workingModel = cellDimensionService.getDimModelResponse(toSaveModelId);
            List<Metric> toSaveMetric = entry.getValue();
            for (List<Metric> toSaveMetricSlice : ListUtils.partition(toSaveMetric, MERGE_PARTITION_SIZE)) {
                FormCellInfoRequest formCellInfoRequest = buildFormSaveData(toSaveModelId, metricMap,
                        toSaveMetricSlice);
                log.info("计算的model:{}, 保存计算结果数据中, model:{}, size:{}", toCalModel.getModelName(),
                        workingModel.getModelName(), Optional.ofNullable(formCellInfoRequest)
                                .map(FormCellInfoRequest::getFormDataList).map(List::size).orElse(0));
                if (formCellInfoRequest == null) {
                    continue;
                }
                dataStorageApplicationService.mergeCellData(formCellInfoRequest);
            }
        }
    }

    private NodeBuffer getCompletedNodeBuffer(List<CalculationRule> calculationRules, Set<Long> noRuleUnitIds,
            Set<String> limitModels, NodeBuffer nodeBuffer) {
        calculationExecutor.appendDag(nodeBuffer, calculationRules);
        Map<String, Node> leafNodes = nodeBuffer.getLeafNodes();
        // 构建下一层规则的查询条件：
        // 当前规则树的叶子节点-已确定没有规则(减少查询)-导致成环的规则(?)-adjust单元格
        List<CalculationRule> ruleParam = leafNodes.values().stream()
                .filter(node -> !noRuleUnitIds.contains(node.getUnitId())).map(node -> {
                    CalculationRule rule = new CalculationRule();
                    rule.setUnitId(node.getUnitId());
                    return rule;
                }).collect(Collectors.toList());
        List<CalculationRule> deeperRules = calculationRuleService.batchQueryCalculationRules(ruleParam,
                CalculationScope.BUSINESS);
        Set<Long> lastUnitId = calculationRules.stream().map(CalculationRule::getUnitId).collect(Collectors.toSet());
        List<CalculationRule> acceptedDeeperRules = limitModels == null
                ? deeperRules
                : deeperRules.stream().filter(rule -> limitModels.contains(rule.getGroupId()))
                        .filter(rule -> !lastUnitId.contains(rule.getUnitId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(acceptedDeeperRules)) {
            return nodeBuffer;
        }
        // TODO:20250526 在这里判断 图是否存在环
        log.info("下一层规则个数:{}", acceptedDeeperRules.size());
        if (log.isDebugEnabled()) {
            log.debug("下一层unitId:{}", acceptedDeeperRules.stream().map(rule -> String.valueOf(rule.getUnitId()))
                    .collect(Collectors.joining(",")));
        }
        Set<Long> queryUnitIds = ruleParam.stream().map(CalculationRule::getUnitId).collect(Collectors.toSet());
        Set<Long> havingRuleUnitIds = acceptedDeeperRules.stream().map(CalculationRule::getUnitId)
                .collect(Collectors.toSet());
        noRuleUnitIds.addAll(
                queryUnitIds.stream().filter(id -> !havingRuleUnitIds.contains(id)).collect(Collectors.toSet()));
        return getCompletedNodeBuffer(acceptedDeeperRules, noRuleUnitIds, limitModels, nodeBuffer);
    }

    private void calculateParam(NodeBuffer nodeBuffer, Map<String, Metric> metricMap) {
        Map<String, Node> rootNodes = nodeBuffer.getRootNodes();
        Iterator<Map.Entry<String, Node>> it = rootNodes.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, Node> entry = it.next();
            try {
                calculationExecutor.checkCircle(entry.getValue(), new CalculationContext());
                aviatorCalculationExecutor.nodeExecute(entry.getValue(), metricMap);
            } catch (CalculationException e) {
                it.remove();
                log.error("root node[{}] exists circle reference", entry.getValue().getKey());
            }
        }
    }

    private Map<String, Metric> buildMetricParam(NodeBuffer nodeBuffer, Map<String, Metric> filledMetricMapper) {
        Map<String, Metric> metricMap = new HashMap<>(
                MapUtil.initSize(nodeBuffer.getNonLeafNodes().size() + filledMetricMapper.size()));
        Set<String> relatedModels = new HashSet<>();
        nodeBuffer.getNonLeafNodes().forEach((unitIdStr, node) -> {
            Metric metric = new Metric();
            metric.setUnitId(node.getUnitId());
            metric.setGroupId(node.getGroupId());
            // 只需要关心中间节点的auto/adjust,叶子节点不受影响
            relatedModels.add(node.getGroupId());
            // 单元格要求是默认空字符串的情况下,并且计算过程有异常时,会显示有问题【修复需要每一个id都查找好单元格类型】
            metricMap.put(unitIdStr, metric);
        });
        metricMap.putAll(filledMetricMapper);
        processAdjustMetric(metricMap, relatedModels);
        metricMap.values().forEach(metric -> {
            if (metric.getValue() == null) {
                metric.setValue(MetricDataType.NUMERIC.equals(metric.getMetricDataType()) ? BigDecimal.ZERO : "");
            }
        });
        return metricMap;
    }

    private void processAdjustMetric(Map<String, Metric> metricMap, Set<String> relatedModels) {
        // TODO： 加开关，按作业岛判断是否走adjust判断
        if(true){
            return;
        }
        // 方式一：按模型把adjust全部拿出来, 转换keyCode为unitId,
        // 得到都是adjust的unitId【keyCode->List<Dim>->unitId】
        // 利润分布场景下，adjust的个数不会超过1000个
        log.info("查询单元格类型(auto/adjust)中, relatedModels:{}", relatedModels);
        List<Metric> adjustMetric = new ArrayList<>();
        for (String modelId : relatedModels) {
            // TODO： 这一步有问题,比较慢 -- 先查adjust的keycode->再根据keycode查询id
            List<String> adjustKeyCodes = iFormDimMemberPropertyRep.findAdjustKeyCodes(modelId);
            // 拿维度排序，走calculation表拿id
            List<Long> unitIds = cellDimensionService.transferKeyCode2UnitId(adjustKeyCodes, modelId);
            for (Long unitId : unitIds) {
                String unitIdStr = String.format(Constants.KEY_FORMAT, unitId);
                if (!metricMap.containsKey(unitIdStr)) {
                    log.debug("adjust属性的unit{}不在metricMap中", unitIdStr);
                    continue;
                }
                Metric metric = metricMap.get(unitIdStr);
                metric.setMetricGenerateStrategy(MetricGenerateStrategy.ADJUST);
                adjustMetric.add(metric);
            }
        }
        // adjust需要额外查询值出来
        log.info("查询adjust预置的值..");
        Map<String, Metric> unitId2MetricWithValue = metricService.getMetrics(adjustMetric);
        if (log.isDebugEnabled()) {
            log.debug("adjust的unitID：{}", adjustMetric.stream().map(m -> m.getUnitId() + ":" + m.getValue())
                    .collect(Collectors.joining(",")));
        }
        metricMap.putAll(unitId2MetricWithValue);
        // 方式二：把列表中所有unit(全模型计算场景下量会很多,暂不使用)拼接keyCode,
        // 批量去数据库查找【unitId->List<Dim>->keyCode】
    }

    private FormCellInfoRequest buildFormSaveData(String modelId, Map<String, Metric> metricMap, List<Metric> metrics) {
        DimModelResponse dimModelResponse = cellDimensionService.getDimModelResponse(modelId);
        Map<String, Map<String, MyDimMemberEntity>> dimMembers = toDimMembers(dimModelResponse);
        List<CellElement> cellElements = cellDimensionService.getCellElementsByUnitIds(
                metrics.stream().map(Metric::getUnitId).collect(Collectors.toList()), modelId);
        List<FormCellInfoRequest.CellInfo> formDataList = new ArrayList<>();
        for (CellElement cellElement : cellElements) {
            Metric metric = metricMap.get(String.format(Constants.KEY_FORMAT, cellElement.getId()));
            if (StringUtils.isNotBlank(metric.getWarningMessage())) {
                continue;
            }
            FormCellInfoRequest.CellInfo cellInfo = getCellInfo(dimMembers, cellElement, metric);
            formDataList.add(cellInfo);
        }
        if (CollectionUtils.isEmpty(formDataList)) {
            return null;
        }
        FormCellInfoRequest formCellInfoRequest = new FormCellInfoRequest();
        formCellInfoRequest.setModelId(modelId);
        formCellInfoRequest.setFormDataList(formDataList);
        formCellInfoRequest.setFormPovList(new ArrayList<>());
        return formCellInfoRequest;
    }

    private FormCellInfoRequest.@NotNull CellInfo getCellInfo(Map<String, Map<String, MyDimMemberEntity>> dimMembers,
            CellElement cellElement, Metric metric) {
        FormCellInfoRequest.CellInfo cellInfo = new FormCellInfoRequest.CellInfo();
        cellInfo.setDimMemberList(cellElement.getDimensions().stream().map(cellDimension -> {
            String dimensionCode = cellDimension.getDimensionCode();
            MyDimMemberEntity myDimMemberEntity = dimMembers.get(dimensionCode).get(cellDimension.getDimensionName());
            if (myDimMemberEntity == null) {
                log.warn("cellId:{}, dim name:{}, code:{} not match when load datasource metric", cellElement.getId(),
                        cellDimension.getDimensionName(), cellDimension.getDimensionCode());
                throw new FormApplicationException(PubErrorCode.PUB_CAL_ERROR_DIM_NAME_NOT_MATCH);
            }
            DimInstance dimInstance = new DimInstance();
            dimInstance.setDimId(myDimMemberEntity.getDimId());
            dimInstance.setMemberId(myDimMemberEntity.getMemberId());
            dimInstance.setMember(myDimMemberEntity.getMemberCode());
            return dimInstance;
        }).collect(Collectors.toList()));
        if (MetricDataType.NUMERIC.equals(metric.getMetricDataType())) {
            try {
                Double.valueOf(String.valueOf(metric.getValue()));
                cellInfo.setCellTypeEnum(CellTypeEnum.ATM);
            } catch (NumberFormatException e) {
                log.error("类型错误规避:数组->文本, unitId:{}, value:{}", cellElement.getId(), metric.getValue());
                cellInfo.setCellTypeEnum(CellTypeEnum.TEXT);
            }
        } else {
            cellInfo.setCellTypeEnum(CellTypeEnum.TEXT);
        }
        cellInfo.setCellValue(metric.getValue());
        return cellInfo;
    }

    private Map<String, Map<String, MyDimMemberEntity>> toDimMembers(DimModelResponse dimModelResponse) {
        Map<String, Map<String, MyDimMemberEntity>> dimMembers = new HashMap<>();
        List<MyDimensionResponse> dimensionResponses = dimModelResponse.getDimensionList();
        for (MyDimensionResponse myDimensionResponse : dimensionResponses) {
            String dimCode = myDimensionResponse.getDimCode();
            List<MyDimMemberEntity> memberList = myDimensionResponse.getMemberList();
            Map<String, MyDimMemberEntity> members = new HashMap<>();
            for (MyDimMemberEntity myDimMemberEntity : memberList) {
                members.put(myDimMemberEntity.getMemberCode(), myDimMemberEntity);
            }
            dimMembers.put(dimCode, members);
        }
        return dimMembers;
    }

    private List<CalculationRule> getCalculationRulesByModelId(String modelId) {
        CalculationRule calculationRule = new CalculationRule();
        calculationRule.setGroupId(modelId);
        try {
            return calculationRuleService.getHighestPriorityUnitCalculationRules(calculationRule);
        } catch (Exception exception) {
            log.error("get calculation  rules error:", exception);
            throw new FormApplicationException(FormAppExceptionEnum.GET_CALCULATION_RULES_ERROR.getMessage(),
                    PubErrorCode.PUB_ERROR_INTERNAL_ERROR);
        }
    }
}
