/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.workflow.deploy.impl;

import com.huawei.it.finance.opt.base.workflow.Constants;
import com.huawei.it.finance.opt.base.workflow.deploy.DeployCellActivity;
import com.huawei.it.finance.opt.base.workflow.deploy.DeployGraphActivity;
import com.huawei.it.finance.opt.base.workflow.deploy.DslDeployWorkflow;
import com.huawei.it.finance.opt.base.workflow.deploy.dto.DeployCellParam;
import com.huawei.it.finance.opt.base.workflow.deploy.dto.DeployGraphParam;
import com.huawei.it.finance.opt.base.workflow.deploy.dto.WorkflowParam;
import com.huawei.it.finance.opt.tech.util.DictConfigUtil;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.RULE_WORKFLOW_DEPLOY_SWITCH;

@Slf4j
@WorkflowImpl(taskQueues = Constants.PARALLEL_DEPLOY_QUEUE_NAME)
public class DslDeployWorkflowImpl implements DslDeployWorkflow {
    private final DeployCellActivity deployCellActivity;
    private final DeployGraphActivity deployGraphActivity;

    // TODO： 202504 测试用，待删除。
    private static final String DeploySwitch = "*";
    private static final String DeploySwitchDefaultValue = "*";


    public DslDeployWorkflowImpl() {
        ActivityOptions options = ActivityOptions.newBuilder()
                .setStartToCloseTimeout(Duration.ofMinutes(10L))//活动的总超时时间,当前为10分钟
                .setRetryOptions(RetryOptions.newBuilder()
                        .setMaximumAttempts(1) // 设置最大尝试次数为1，即不进行重试
                        .build())
                .build();
        deployCellActivity = Workflow.newActivityStub(DeployCellActivity.class, options);
        deployGraphActivity = Workflow.newActivityStub(DeployGraphActivity.class, options);
    }

    @Override
    public int deploy(WorkflowParam workflowParam) {
        int taskCount = workflowParam.getTotalCellCount() / workflowParam.getBatchSize() + 1;
        try {
            /*一、规则部署*/
            /*这里已经做了分批，一批10万*/
            List<Promise<String>> allTasks = new ArrayList<>();
            for (int i = 0; i < taskCount; i++) {
                DeployCellParam param = buildActivityParam(workflowParam, i);
                Promise<String> promise = Async.function(() -> {
                    deployCellActivity.deployCellExecute(param);
                    return "";
                });
                allTasks.add(promise);
            }
            Promise.allOf(allTasks).get();

            /*二、 保存部署结果(rule+增量unit)*/
            Promise<Void> saveResultPromise = Async.procedure(
                    deployCellActivity::deployResultSaveExecute,
                    workflowParam
            );
            Promise.allOf(saveResultPromise).get();

            /*三、三元组保存*/
            DeployGraphParam param = new DeployGraphParam(workflowParam);

            Promise<Void> deployGraphPromise = Async.procedure(
                    deployGraphActivity::deployGraphExecute,
                    param
            );
            Promise.allOf(deployGraphPromise).get();
            return 0;
        } catch (Exception e) {
            // 记录错误日志
            log.error("规则部署失败：", e);
            throw new RuntimeException("Deployment process failed", e);
        }
    }

    // TODO:这里增加一个开关默认值
    public static boolean isWorkflowDeploy(long scriptId) {
        String workflowDeployScriptIds = DictConfigUtil.getProperty(RULE_WORKFLOW_DEPLOY_SWITCH, DeploySwitchDefaultValue);
        if (DeploySwitch.equals(workflowDeployScriptIds)) {
            return true;
        }
        String scriptIdStr = String.valueOf(scriptId);
        return Arrays.stream(workflowDeployScriptIds.split(","))
                .map(String::trim)
                .anyMatch(s -> s.equals(scriptIdStr));
    }

    public static DeployCellParam buildActivityParam(WorkflowParam workflowParam, int i) {
        int start = i * workflowParam.getBatchSize();
        int end = (i + 1) * workflowParam.getBatchSize();
        return new DeployCellParam(workflowParam, start, end, i);
    }
}
