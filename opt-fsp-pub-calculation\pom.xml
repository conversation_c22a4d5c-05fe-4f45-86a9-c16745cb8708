<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.huawei.it.finance.opt</groupId>
        <artifactId>opt-fsp-pub-service-parent</artifactId>
        <version>1.0.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>opt-fsp-pub-calculation</artifactId>

    <dependencies>
        <!-- 添加workflow模块依赖 -->
        <dependency>
            <groupId>com.huawei.it.finance.opt</groupId>
            <artifactId>opt-fsp-pub-workflow</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>io.temporal</groupId>
            <artifactId>temporal-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>io.temporal</groupId>
            <artifactId>temporal-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.it.finance.opt</groupId>
            <artifactId>opt-fsp-pub-task</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.it.finance.opt</groupId>
            <artifactId>opt-fsp-pub-appisland</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4</artifactId>
        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huawei.wisecloud.secure</groupId>
            <artifactId>SessionManagement</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>bcprov-jdk18on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>
</project>