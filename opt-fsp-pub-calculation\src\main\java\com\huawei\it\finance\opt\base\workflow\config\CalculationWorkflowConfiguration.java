/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.workflow.config;

import com.huawei.it.finance.opt.base.workflow.activity.impl.DeployCellActivityImpl;
import com.huawei.it.finance.opt.workflow.Constants;
import io.temporal.worker.Worker;
import io.temporal.worker.WorkerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Calculation模块的Workflow配置类
 * 负责注册Activity实现到Temporal Worker
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
@Slf4j
@Configuration
public class CalculationWorkflowConfiguration {

    @Autowired
    private DeployCellActivityImpl deployCellActivity;

    /**
     * 配置部署队列Worker，注册Activity实现
     */
    @Bean
    public Worker calculationDeployWorker(WorkerFactory workerFactory) {
        Worker worker = workerFactory.newWorker(Constants.PARALLEL_DEPLOY_QUEUE_NAME);
        
        // 注册Activity实现
        worker.registerActivitiesImplementations(deployCellActivity);
        
        log.info("Registered DeployCellActivity implementation to worker for queue: {}", 
                Constants.PARALLEL_DEPLOY_QUEUE_NAME);
        return worker;
    }
}
