/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.workflow.service;

import com.huawei.it.finance.opt.workflow.deploy.dto.WorkflowParam;

/**
 * 工作流服务接口
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
public interface WorkflowService {
    
    /**
     * 启动DSL部署工作流
     *
     * @param workflowParam 工作流参数
     * @return 工作流执行ID
     */
    String startDslDeployWorkflow(WorkflowParam workflowParam);

    /**
     * 查询工作流状态
     *
     * @param workflowId 工作流ID
     * @return 工作流状态
     */
    String getWorkflowStatus(String workflowId);

    /**
     * 取消工作流
     *
     * @param workflowId 工作流ID
     */
    void cancelWorkflow(String workflowId);

    /**
     * 等待工作流完成
     *
     * @param workflowId 工作流ID
     * @return 工作流执行结果
     */
    Integer waitForWorkflowCompletion(String workflowId);
}
