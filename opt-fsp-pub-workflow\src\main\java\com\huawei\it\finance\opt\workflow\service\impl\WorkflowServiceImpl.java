/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.workflow.service.impl;

import com.huawei.it.finance.opt.workflow.deploy.DslDeployWorkflow;
import com.huawei.it.finance.opt.workflow.deploy.dto.WorkflowParam;
import com.huawei.it.finance.opt.workflow.service.WorkflowService;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import io.temporal.client.WorkflowStub;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 工作流服务实现类
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
@Slf4j
@Service
public class WorkflowServiceImpl implements WorkflowService {

    @Autowired
    private WorkflowClient workflowClient;

    @Autowired
    private WorkflowOptions defaultWorkflowOptions;

    @Override
    public String startDslDeployWorkflow(WorkflowParam workflowParam) {
        try {
            // 生成唯一的工作流ID
            String workflowId = "dsl-deploy-" + workflowParam.getScriptId() + "-" + UUID.randomUUID().toString();
            
            // 创建工作流选项
            WorkflowOptions options = WorkflowOptions.newBuilder(defaultWorkflowOptions)
                    .setWorkflowId(workflowId)
                    .build();

            // 创建工作流存根
            DslDeployWorkflow workflow = workflowClient.newWorkflowStub(DslDeployWorkflow.class, options);

            // 异步启动工作流
            WorkflowClient.start(workflow::deploy, workflowParam);

            log.info("Started DSL deploy workflow with ID: {} for script: {}", workflowId, workflowParam.getScriptId());
            return workflowId;
        } catch (Exception e) {
            log.error("Failed to start DSL deploy workflow for script: {}", workflowParam.getScriptId(), e);
            throw new RuntimeException("Failed to start workflow", e);
        }
    }

    @Override
    public String getWorkflowStatus(String workflowId) {
        try {
            WorkflowStub workflowStub = workflowClient.newUntypedWorkflowStub(workflowId);
            return workflowStub.getExecution().getWorkflowId();
        } catch (Exception e) {
            log.error("Failed to get workflow status for ID: {}", workflowId, e);
            return "UNKNOWN";
        }
    }

    @Override
    public void cancelWorkflow(String workflowId) {
        try {
            WorkflowStub workflowStub = workflowClient.newUntypedWorkflowStub(workflowId);
            workflowStub.cancel();
            log.info("Cancelled workflow with ID: {}", workflowId);
        } catch (Exception e) {
            log.error("Failed to cancel workflow with ID: {}", workflowId, e);
            throw new RuntimeException("Failed to cancel workflow", e);
        }
    }

    @Override
    public Integer waitForWorkflowCompletion(String workflowId) {
        try {
            DslDeployWorkflow workflow = workflowClient.newWorkflowStub(DslDeployWorkflow.class, workflowId);
            Integer result = workflow.deploy(null); // 这里传null是因为工作流已经启动
            log.info("Workflow completed with result: {} for ID: {}", result, workflowId);
            return result;
        } catch (Exception e) {
            log.error("Failed to wait for workflow completion for ID: {}", workflowId, e);
            throw new RuntimeException("Failed to wait for workflow completion", e);
        }
    }
}
