<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.finance.opt.wrap.dao.CellDimensionDao">

    <select id="findCellElementsByCondition" resultType="java.util.Map">
        select
        dy.row_id as id,
        <foreach collection="condition.dimensionTableColumnTitles" item="dimensionTableColumnTitle" separator="," >
            dy.${dimensionTableColumnTitle}
        </foreach>
        from ${condition.modelTableName} dy
        inner join dm_fsppub_calculation_global_cell_t gc on gc.cell_element_id = dy.row_id
        <trim prefix="where" prefixOverrides="and ">
            <if test='condition.modelId != null'>
                and gc.model_id = #{condition.modelId}
            </if>
            <if test='condition.minUnitId != null'>
                and gc.cell_element_id &gt;= #{condition.minUnitId}
            </if>
            <if test='condition.maxUnitId != null'>
                and gc.cell_element_id &lt;= #{condition.maxUnitId}
            </if>
            <if test='condition.workspaceId != null'>
                and gc.workspace_id = #{condition.workspaceId}
            </if>
            <if test="condition.unitIds != null and condition.unitIds.size() > 0">
                and gc.cell_element_id in
                <foreach collection="condition.unitIds" item="unitId" open="(" separator="," close=")">
                    #{unitId}
                </foreach>
            </if>
            <if test="condition.dimField2dimValues != null and condition.dimField2dimValues.size() > 0">
                and
                <foreach collection="condition.dimField2dimValues" index="dimField" item="dimValues" open="" separator="and" close="">
                    ${dimField}
                    in
                    <foreach collection="dimValues" item="dimValue" open="(" separator="," close=")">
                        #{dimValue}
                    </foreach>
                </foreach>
            </if>
        </trim>
        order by dy.row_id
        <if test="condition.limit != null and offset != null">
            limit #{condition.limit} offset #{offset}
        </if>
    </select>

    <select id="findSpecifyCellElements" resultType="java.util.Map">
        select
        dy.row_id as id,
        <foreach collection="cellElement.dimensions" item="dimension" separator="," >
            dy.${dimension.dimensionColumnName}
        </foreach>
        from ${cellElement.modelTableName} dy
        inner join dm_fsppub_calculation_global_cell_t gc on gc.cell_element_id = dy.row_id
        <trim prefix="where" prefixOverrides="and ">
            <if test='cellElement.id != null'>
                and dy.row_id = #{cellElement.id}
            </if>
            <if test='cellElement.businessVersionId != null'>
                and gc.business_version_id = #{cellElement.businessVersionId}
            </if>
            <if test='cellElement.versionId != null'>
                and gc.version_id = #{cellElement.versionId}
            </if>
            <if test='cellElement.cellElementId != null'>
                and gc.cell_element_Id = #{cellElement.cellElementId}
            </if>
            <if test='cellElement.modelId != null'>
                and gc.model_id = #{cellElement.modelId}
            </if>
            <if test='cellElement.modelCode != null'>
                and gc.model_code = #{cellElement.modelCode}
            </if>
            <if test='cellElement.workspaceId != null'>
                and gc.workspace_id = #{cellElement.workspaceId}
            </if>
            <if test="dimField2dimValues != null and dimField2dimValues.size() > 0">
                and
                <foreach collection="dimField2dimValues" index="dimField" item="dimValues" open="" separator="and" close="">
                    ${dimField}
                    in
                    <foreach collection="dimValues" item="dimValue" open="(" separator="," close=")">
                        #{dimValue}
                    </foreach>
                </foreach>
            </if>
        </trim>
    </select>

    <select id="batchFindCellElement" resultType="com.huawei.it.finance.opt.wrap.entity.CellElement">
        select
        cell_element_id as cellElementId,
        model_id as modelId
        from dm_fsppub_calculation_global_cell_t
        where cell_element_id in
        <foreach collection="cellElementList" item="cellElement" separator="," open="(" close=")">
            #{cellElement.cellElementId}
        </foreach>
    </select>

    <select id="batchFindModelIdByUnitIds" resultType="map">
        SELECT cell_element_id AS key,
        model_id AS value
        FROM dm_fsppub_calculation_global_cell_t
        WHERE cell_element_id IN
        <foreach collection="unitIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="findSimpleCellElements" resultType="com.huawei.it.finance.opt.wrap.entity.CellElement">
        select
        cell_element_id as cellElementId,
        model_id as modelId
        from dm_fsppub_calculation_global_cell_t
        <trim prefix="where" prefixOverrides="and ">
            <if test='cellElementId != null'>
                and cell_element_id = #{cellElementId}
            </if>
            <if test='businessVersionId != null'>
                and business_version_id = #{businessVersionId}
            </if>
            <if test='versionId != null'>
                and version_id = #{versionId}
            </if>
            <if test='modelId != null'>
                and model_id = #{modelId}
            </if>
            <if test='modelCode != null'>
                and model_code = #{modelCode}
            </if>
            <if test='workspaceId != null'>
                and workspace_id = #{workspaceId}
            </if>
        </trim>
    </select>

    <select id="findGlobalCellElements"  resultType="com.huawei.it.finance.opt.wrap.entity.CellElement">
        select
            cell_element_id as cellElementId,
            business_version_id as businessVersionId,
            version_id as versionId,
            model_id as modelId,
            model_code as modelCode,
            creation_date as creationDate,
            created_by as createdBy,
            last_update_date as lastUpdateDate,
            last_updated_by as lastUpdatedBy,
            cell_element_name as cellElementName,
            cell_element_short_name as cellElementShortName
        from dm_fsppub_calculation_global_cell_t
        <trim prefix="where" prefixOverrides="and ">
            <if test='cellElementId != null'>
                and cell_element_id = #{cellElementId}
            </if>
            <if test='businessVersionId != null'>
                and business_version_id = #{businessVersionId}
            </if>
            <if test='versionId != null'>
                and version_id = #{versionId}
            </if>
            <if test='modelId != null'>
                and model_id = #{modelId}
            </if>
            <if test='modelCode != null'>
                and model_code = #{modelCode}
            </if>
        </trim>
    </select>

    <delete id="deleteGlobalCellElements">
        delete from dm_fsppub_calculation_global_cell_t
        <trim prefix="where" prefixOverrides="and ">
            <if test='cellElementId != null'>
                and cell_element_id = #{cellElementId}
            </if>
            <if test='businessVersionId != null'>
                and business_version_id = #{businessVersionId}
            </if>
            <if test='versionId != null'>
                and version_id = #{versionId}
            </if>
            <if test='modelId != null'>
                and model_id = #{modelId}
            </if>
            <if test='modelCode != null'>
                and model_code = #{modelCode}
            </if>
        </trim>
    </delete>


    <insert id="saveWorkFlowOperateInfo" parameterType="com.huawei.it.finance.opt.base.workflow.deploy.entity.WorkFlowOperator">
        INSERT INTO dm_fsppub_calculation_workflow_info_t
        (temporal_table_name, workflow_id, workspace_id, '@@INSERT_COLUMNS_AUDITS@@')
        values (#{temporalTableName},
                #{workflowId},
                #{workspaceId},
                '@@INSERT_VALUES_AUDITS@@')
    </insert>

    <insert id="batchSaveGlobalCellElements">
        insert into dm_fsppub_calculation_global_cell_t
        (
            row_id,
            cell_element_id,
            business_version_id,
            version_id,
            model_id,
            model_code,
            creation_date,
            created_by,
            last_update_date,
            last_updated_by,
            cell_element_name,
            cell_element_short_name,
            workspace_id
        )
        values
        <foreach collection="globalCellElements" item="item" separator=",">
            (
            #{item.rowId},
            #{item.cellElementId},
            #{item.businessVersionId},
            #{item.versionId},
            #{item.modelId},
            #{item.modelCode},
            #{item.creationDate},
            #{item.createdBy},
            #{item.lastUpdateDate},
            #{item.lastUpdatedBy},
            #{item.cellElementName},
            #{item.cellElementShortName},
            #{item.workspaceId}
            )
        </foreach>
    </insert>

    <delete id="batchDeleteGlobalCellElementsById">
        delete from  dm_fsppub_calculation_global_cell_t where
        cell_element_id in (
            <foreach collection="globalCellElements" item="item" separator=",">
                #{item.id}
            </foreach>
        )
        '@@WHERE_CONDITIONS_WORKSPACE@@'
    </delete>

    <delete id="batchDeleteCellElementsById">
        delete from  ${cellElements[0].modelTableName} where
        row_id in (
            <foreach collection="cellElements" item="item" separator=",">
                #{item.id}
            </foreach>
        )
    </delete>

    <delete id="deleteDirtyGlobalCell">
        delete from dm_fsppub_calculation_global_cell_t d
        where d.model_id = #{modelId}
          and not exists (
            select 1
            from ${modelTableName} e
            where e.row_id = d.cell_element_id
        );
    </delete>

    <delete id="deleteCalculationCell">
        delete
            ${modelTableName} t1
        where not exists (
            select 1
            from dm_fsppub_calculation_global_cell_t t2
            where t1.row_id = t2.cell_element_id
        );
    </delete>

    <delete id="deleteDuplicatedGlobalCells">
        DELETE
        FROM
        dm_fsppub_calculation_global_cell_t gc
        WHERE
        gc.ctid != (
            SELECT MIN ( gc1.ctid ) FROM dm_fsppub_calculation_global_cell_t gc1
            WHERE gc.cell_element_short_name = gc1.cell_element_short_name
            AND gc1.workspace_id=#{workspaceId}
            AND gc1.model_id=#{modelId}
        )
          AND workspace_id =#{workspaceId} AND model_id=#{modelId};
    </delete>

    <delete id="deleteDuplicatedCalculationCells">
        DELETE FROM ${calculationTableName} t1
        WHERE NOT EXISTS (
            SELECT 1
            FROM dm_fsppub_calculation_global_cell_t t2
            WHERE t1.row_id=t2.cell_element_id
        )
    </delete>

    <select id="findGlobalCellElementsById" resultType="com.huawei.it.finance.opt.wrap.entity.CellElement">
        select
                cell_element_id as cellElementId,
                business_version_id as businessVersionId,
                version_id as versionId,
                model_id as modelId,
                cell_element_name as cellElementName,
                cell_element_short_name as cellElementShortName
        from  dm_fsppub_calculation_global_cell_t
        where
            cell_element_id in (
                <foreach collection="cellElementIds" item="item" separator=",">
                    #{item}
                </foreach>
            )
        '@@WHERE_CONDITIONS_WORKSPACE@@'
    </select>

    <select id="findGlobalCellElementsByIdV2" resultType="com.huawei.it.finance.opt.wrap.entity.CellElement">
        select
        cell_element_id as cellElementId,
        business_version_id as businessVersionId,
        version_id as versionId,
        model_id as modelId,
        cell_element_name as cellElementName,
        cell_element_short_name as cellElementShortName
        from  dm_fsppub_calculation_global_cell_t
        where
        cell_element_id in (${idStr})
        '@@WHERE_CONDITIONS_WORKSPACE@@'
    </select>

    <select id="generateNextCellElementId" resultType="java.lang.Long">
        select a.cellId
        from (
        <foreach collection="cellElements" index="cellElement" separator="union">
            select nextval('calculation_global_cell_id_seq') as cellId
        </foreach>
        ) a order by a.cellId
    </select>

    <select id="generateNextCellElementIds" resultType="java.lang.Long">
        SELECT nextval('calculation_global_cell_id_seq')
        FROM generate_series(1, #{cellElementSize});
    </select>

    <sql id="globalCellFields">
        row_id,cell_element_id,business_version_id,version_id,model_id,model_code,creation_date,created_by,last_update_date,last_updated_by,cell_element_name,cell_element_short_name,workspace_id
    </sql>

    <!-- only insert cell elements with unique value on fields (workspace_id,cell_element_short_name) -->
    <insert id="batchSaveUniqueGlobalCellElements">
        insert into dm_fsppub_calculation_global_cell_t(<include refid="globalCellFields"/>)
        select <include refid="globalCellFields"/>
        from (
            values
            <foreach collection="globalCellElements" item="item" separator=",">
                (
                #{item.rowId},
                #{item.cellElementId},
                #{item.businessVersionId},
                #{item.versionId},
                #{item.modelId},
                #{item.modelCode},
                #{item.creationDate},
                #{item.createdBy},
                #{item.lastUpdateDate},
                #{item.lastUpdatedBy},
                #{item.cellElementName},
                #{item.cellElementShortName},
                #{item.workspaceId}
                )
            </foreach>
        ) t1(<include refid="globalCellFields"/>)
        where not exists (
            select 1
            from dm_fsppub_calculation_global_cell_t t2
            where t1.cell_element_short_name = t2.cell_element_short_name
              and t1.workspace_id = t2.workspace_id
        )
    </insert>

    <!-- only insert cell elements into calculation tables when the cellId exists in global table -->
    <insert id="batchSaveCalculationCellElements" keyProperty="id" keyColumn="row_id" useGeneratedKeys="true">
        insert into ${cellElements[0].modelTableName}
        (
        row_id,
        <foreach collection="cellElements[0].dimensions" item="dimension" separator="," >
            ${dimension.dimensionColumnName}
        </foreach>,
        creation_date,
        created_by,
        last_update_date,
        last_updated_by,
        workspace_id
        )
        select
        row_id,
        <foreach collection="cellElements[0].dimensions" item="dimension" separator="," >
            ${dimension.dimensionColumnName}
        </foreach>,
        creation_date,
        created_by,
        last_update_date,
        last_updated_by,
        workspace_id
        from (
        values
        <foreach collection="cellElements" item="item" separator=",">
            (
            #{item.rowId},
            <foreach collection="item.dimensions" item="dimension" separator="," >
                #{dimension.dimensionCode}
            </foreach>
            ,
            #{item.creationDate},
            #{item.createdBy},
            #{item.lastUpdateDate},
            #{item.lastUpdatedBy},
            #{item.workspaceId}
            )
        </foreach>
        ) t1(
        row_id,
        <foreach collection="cellElements[0].dimensions" item="dimension" separator="," >
            ${dimension.dimensionColumnName}
        </foreach>,
        creation_date,
        created_by,
        last_update_date,
        last_updated_by,
        workspace_id
        )
        where exists (
        select 1
        from dm_fsppub_calculation_global_cell_t t2
        where t1.row_id = t2.row_id
        )
    </insert>

    <select id="batchQueryValueByMetric" resultType="java.util.Map">
        select c.row_id as row_id, t.amount as amount, t.origin_expression as origin_expression,
        <foreach collection="condition.dimensionTableColumnTitles" item="dimensionTableColumnTitle" separator="," >
            c.${dimensionTableColumnTitle}
        </foreach>
        from ${condition.modelTableName}_calculation c left join ${condition.modelTableName} t on
        <foreach collection="condition.dimensionTableColumnTitles" item="dimensionTableColumnTitle" separator="and">
            c.${dimensionTableColumnTitle} = t.${dimensionTableColumnTitle}
        </foreach>
        where c.row_id in
        <foreach collection="condition.unitIds" item="unitId" open="(" separator="," close=")">
            #{unitId}
        </foreach>
        <if test="condition.workspaceId != null">
            and t.workspace_id=#{condition.workspaceId}
        </if>
    </select>

    <select id="findUnitIdByDim2Members" resultType="java.lang.Long">
        select row_id from
        ${cellElement.modelTableName}
        where
        <foreach collection="columnNames" item="columnName" separator="," open="(" close=")">
            ${columnName}
        </foreach>
        in
        <foreach collection="memberCodesList" item="memberCodes" separator="," open="(" close=")">
            <foreach collection="memberCodes" item="memberCode" separator="," open="(" close=")">
                #{memberCode}
            </foreach>
        </foreach>
    </select>

    <delete id="dropCalculationTable">
        DROP TABLE IF EXISTS ${calculationTableName}
    </delete>

    <delete id="deleteGlobalCellElementsFromCalculationTable">
        DO $$
        BEGIN
        IF EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_schema = current_schema()
            AND table_name = '||${calculationTableName}||'
            AND column_name = 'row_id'
        )
        THEN
            DELETE FROM dm_fsppub_calculation_global_cell_t
            WHERE cell_element_id IN (
                SELECT row_id FROM ${calculationTableName}
            );
        END IF;
        END $$;
    </delete>
</mapper>