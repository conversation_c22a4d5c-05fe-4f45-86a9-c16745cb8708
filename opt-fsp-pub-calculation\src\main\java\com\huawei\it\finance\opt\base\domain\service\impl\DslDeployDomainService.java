/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.domain.service.impl;

import com.huawei.it.finance.opt.base.entity.CalculationScript;
import com.huawei.it.finance.opt.base.util.CalculationScriptConstants;
import com.huawei.it.finance.opt.base.workflow.deploy.DslDeployContext;
import com.huawei.it.finance.opt.workflow.Constants;
import com.huawei.it.finance.opt.workflow.deploy.DslDeployWorkflow;
import com.huawei.it.finance.opt.workflow.deploy.dto.WorkflowParam;
import com.huawei.it.finance.opt.dsl.complier.input.DSLSourceInfo;
import com.huawei.it.finance.opt.dsl.service.CompileService;
import com.huawei.it.finance.opt.fsp.pub.common.exception.DataSourceException;
import com.huawei.it.finance.opt.task.entity.QuartzTaskInfo;
import com.huawei.it.finance.opt.task.infrastructure.db.repository.IQuartzTaskInfoRepo;
import com.huawei.it.finance.opt.task.util.JobStatusEnum;
import com.huawei.it.finance.opt.tech.util.DictConfigUtil;
import com.huawei.it.finance.pub.dimmodel.common.advice.TrackExecutionTime;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import io.temporal.common.RetryOptions;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.Duration;

import static com.huawei.it.finance.opt.base.util.CalculationScriptConstants.EXECUTE_FAIL;
import static com.huawei.it.finance.opt.tech.common.constant.CommonConstant.RULE_WORKFLOW_DEPLOY_BATCH_SIZE;


/**
 * 使用Workflow的方式执行规则部署
 *
 * <AUTHOR>
 * @since 2025/03/20
 */
@Service
@Slf4j
@AllArgsConstructor
public class DslDeployDomainService {
    private final IQuartzTaskInfoRepo iQuartzTaskInfoRepo;
    private final CalculationScriptDomainService deployService;
    private final CompileService compileService;
    private final WorkflowClient workflowClient;

    @TrackExecutionTime
    public void singleDeployCalculationScript(CalculationScript calculationScript,
                                              DSLSourceInfo dslSourceInfo,
                                              QuartzTaskInfo quartzTaskInfo) throws DataSourceException {
        try {
            DslDeployContext deployContext = new DslDeployContext(iQuartzTaskInfoRepo, quartzTaskInfo,
                    dslSourceInfo, calculationScript.getWorkspaceId(),null ,null);
            deployContext.executeStep(1, "1. 预编译+更新脚本草稿");
            log.info("1. 预编译+更新脚本草稿");
            deployService.updateDraftWhileDeploying(calculationScript, dslSourceInfo);
            compileService.compileDSL(deployContext);
            startDeployWorkflow(deployContext, calculationScript);
            // TODO:这里有问题，不能直接返回成功的结果
            deployService.finishDeploy(calculationScript, dslSourceInfo, quartzTaskInfo);
        } catch (DataSourceException e) {
            log.error(CalculationScriptConstants.EXECUTE_FAIL, e);
            saveException(quartzTaskInfo, e);
        }
    }

    private void saveException(QuartzTaskInfo taskInfo, Exception e) {
        // 如果部署过程发生了异常，记录异常信息
        taskInfo.setStatus(JobStatusEnum.STOP.getCode());
        taskInfo.setExecuteResult(EXECUTE_FAIL + e.getMessage());
        iQuartzTaskInfoRepo.updateQuartzTaskStatusOrExecuteResult(taskInfo);
    }


    private void startDeployWorkflow(DslDeployContext deployContext, CalculationScript calculationScript) {
        WorkflowOptions workflowOptions = WorkflowOptions.newBuilder()
                .setWorkflowId(Constants.PARALLEL_DEPLOY_WORKFLOW_ID + "_" + calculationScript.getScriptId())
                .setTaskQueue(Constants.PARALLEL_DEPLOY_QUEUE_NAME)
                .setRetryOptions(RetryOptions.newBuilder().build())
                .setWorkflowExecutionTimeout(Duration.ofDays(1)) //整个工作流执行的最大时间
                .setWorkflowRunTimeout(Duration.ofDays(1))//单个工作流运行实例的最大时间
                .setWorkflowTaskTimeout(Duration.ofHours(10)) // 每个任务执行的最大时间
                .build();
        DslDeployWorkflow workflow = workflowClient.newWorkflowStub(DslDeployWorkflow.class, workflowOptions);
        WorkflowParam param = buildWorkflowParam(deployContext, calculationScript);
        workflow.deploy(param);
    }

    private static WorkflowParam buildWorkflowParam(DslDeployContext deployContext, CalculationScript calculationScript) {
        Integer batchSize = getParallelSize();
        Integer workFlowId = generateWorkFlowId();
        return new WorkflowParam(calculationScript.getWorkspaceId(),
                calculationScript.getScriptId(),
                deployContext.getTaskInfo().getRowId(),
                calculationScript.getModelId(),
                deployContext.getDslSourceInfo().getModelName(),
                calculationScript.getDbCode(),
                deployContext.getTaskInfo().getJobName(),
                deployContext.getComputeRules().size(), batchSize, workFlowId);
    }

    private static Integer generateWorkFlowId() {
        // 创建 SecureRandom 对象
        SecureRandom secureRandom = new SecureRandom();
        // 生成一个唯一的随机数，用于
        return secureRandom.nextInt();
    }

    private static Integer getParallelSize() {
        String strValue = DictConfigUtil.getProperty(RULE_WORKFLOW_DEPLOY_BATCH_SIZE, "1000000");
        return Integer.valueOf(strValue);
    }

}