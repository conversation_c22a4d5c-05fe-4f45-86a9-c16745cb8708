/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.workflow.deploy.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 工作流参数类
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class WorkflowParam extends BaseParam {
    /**
     * 总单元格数量
     */
    private Integer totalCellCount;

    /**
     * 批次大小
     */
    private Integer batchSize;

    /**
     * 构造函数
     *
     * @param workspaceId 工作空间ID
     * @param scriptId 脚本ID
     * @param taskId 任务ID
     * @param modelId 模型ID
     * @param modelName 模型名称
     * @param dynamicDB 动态数据库标识
     * @param jobName 作业名称
     * @param totalCellCount 总单元格数量
     * @param batchSize 批次大小
     * @param uniqueRandomNumber 唯一随机数
     * @param workFlowId 工作流ID
     */
    public WorkflowParam(Long workspaceId, Long scriptId, Long taskId, Long modelId, String modelName,
                        String dynamicDB, String jobName, Integer totalCellCount, Integer batchSize,
                        Integer uniqueRandomNumber, Integer workFlowId) {
        super(workspaceId, scriptId, taskId, modelId, modelName, dynamicDB, jobName, null, uniqueRandomNumber, workFlowId, null);
        this.totalCellCount = totalCellCount;
        this.batchSize = batchSize;
    }
}
