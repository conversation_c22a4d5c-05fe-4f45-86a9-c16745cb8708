/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.engine.calculate;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.Expression;
import com.googlecode.aviator.Options;
import com.googlecode.aviator.exception.ExpressionRuntimeException;
import com.googlecode.aviator.exception.ExpressionSyntaxErrorException;
import com.huawei.it.finance.opt.base.entity.CalculationRule;
import com.huawei.it.finance.opt.base.entity.Constants;
import com.huawei.it.finance.opt.base.entity.Metric;
import com.huawei.it.finance.opt.base.entity.MetricDataType;
import com.huawei.it.finance.opt.base.entity.MetricGenerateStrategy;
import com.huawei.it.finance.opt.base.exception.CalculationException;
import com.huawei.it.finance.opt.tech.exception.PubErrorCode;
import com.huawei.it.finance.opt.tech.util.SpringContextUtil;
import com.huawei.it.jalor5.core.util.StringUtil;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class AviatorCalculationExecutor implements CalculationExecutor<List<CalculationRule>> {
    private static final String CONTINUE_KEYWORD = "><continue><";

    private static final String DBFUNC_KEYWORD = "DBFUNC";

    private static final String CONTINUE_VALUE = "continue";

    private static final String CONTINUE_VALUE_WITH_QUOT = "\"" + "continue" + "\"";

    private static final String CONTINUE_IN_CHINESE = "无规则";

    private static final Logger logger = LoggerFactory.getLogger(AviatorCalculationExecutor.class);

    private AviatorExecutorProperties aviatorExecutorProperties;

    private AviatorEvaluatorInstance evaluator;

    private ExecutorService compileExecutors;

    private Expression emptyExpression;

    private static final int DETECT_VALUE_HOLDER = 1;

    private final static String DBFUNC_PATTERN = "__DBFUNC\\((\\w+)\\)__";

    private final Pattern dbFuncPattern = Pattern.compile(DBFUNC_PATTERN);

    public AviatorCalculationExecutor() {
        this(new AviatorExecutorProperties());
    }

    public AviatorCalculationExecutor(AviatorExecutorProperties aviatorExecutorProperties) {
        this.aviatorExecutorProperties = aviatorExecutorProperties;
        this.evaluator = AviatorEvaluator.getInstance();
        this.evaluator.setOption(Options.ALLOWED_CLASS_SET, Collections.emptySet());
        this.evaluator.setCachedExpressionByDefault(aviatorExecutorProperties.isCachedExpressionEnabled());
        this.compileExecutors = buildCompileExecutorService();
        this.emptyExpression = buildEmptyExpressSion();
    }

    private ExecutorService buildCompileExecutorService() {
        BlockingQueue workQueue;
        int queueSize = aviatorExecutorProperties.getCompileQueueSize();
        if (queueSize == 0) {
            workQueue = new SynchronousQueue();
        } else {
            workQueue = new ArrayBlockingQueue(queueSize);
        }
        return new ThreadPoolExecutor(aviatorExecutorProperties.getCompileCorePoolSize(),
                aviatorExecutorProperties.getCompileMaximumPoolSize(),
                aviatorExecutorProperties.getCompileKeepAliveTimeMs(), TimeUnit.MILLISECONDS, workQueue,
                buildCompileThreadFactory());
    }

    private ThreadFactory buildCompileThreadFactory() {
        ThreadFactoryBuilder builder = new ThreadFactoryBuilder();
        builder.setNameFormat("opt-aviator-compile-%d");
        builder.setUncaughtExceptionHandler((t, e) -> {
            // 针对线程抛出的异常进行记录
            logger.error("Aviator Compile Uncaught Exception:", e);
        });
        return builder.build();
    }

    @Override
    public void execute(Node node, CalculationContext context) {
        Map<String, Metric> metrics = context.getAttribute(CalculationContext.CALCULATION_METRICS);
        if (node.getNodeType().equals(NodeType.ROOT)) {
            Map<String, Node> subNodes = node.getSubNodes();
            for (Map.Entry<String, Node> entry : subNodes.entrySet()) {
                // 追溯当前正在计算节点的上一层节点是什么
                entry.getValue().setUpperLevelNode(node);
                nodeExecute(entry.getValue(), metrics);
            }
        } else {
            nodeExecute(node, metrics);
        }
    }

    public void nodeExecuteV1(Node node, Map<String, Metric> metrics) {
        String key = node.getKey();
        if (node.isCalculated()) return;

        // Step 1: 获取或创建 Metric 实例
        Metric metric = metrics.computeIfAbsent(key, k -> {
            logger.info("allocate new metric, unitId:{}", key);
            return new Metric();
        });

        if (metric.getUnitId() == null) {
            metric.setUnitId(node.getUnitId());
        }

        // Step 2: 处理无子节点的情况
        if (node.getSubNodes().isEmpty()) {
            handleLeafNode(node, metric);
            return;
        }

        // Step 3: 处理有子节点的情况
        Map<String, Object> subNodeResults = processSubNodes(node, metrics);

        // Step 4: 根据生成策略决定是否执行 Aviator 表达式
        if (metric.getMetricGenerateStrategy() == MetricGenerateStrategy.AUTO) {
            executeWithAviator(node, subNodeResults, metric);
        } else {
            node.setCalculated(true);
        }

        // Step 5: 设置结果值和策略
        setExecutionResult(node, metric);
    }

    private void handleLeafNode(Node node, Metric metric) {
        if (node.getExpressionWrapper() == null && metric.getValue() == null) {
            metric.setWarningMessage("no rule and no value");
        }

        if (metric.getMetricGenerateStrategy() == MetricGenerateStrategy.AUTO) {
            executeWithAviator(node, Collections.emptyMap(), metric);
        }

        setExecutionResult(node, metric);
    }

    private Map<String, Object> processSubNodes(Node node, Map<String, Metric> metrics) {
        Map<String, Node> subNodes = node.getSubNodes();
        int size = subNodes.size();
        Map<String, Object> results = new ConcurrentHashMap<>(size); // 线程安全写入
        List<CompletableFuture<Void>> futures = new ArrayList<>(size);
        Executor asyncExecutor = SpringContextUtil.getBean("asyncExecutor");

        for (Map.Entry<String, Node> entry : subNodes.entrySet()) {
            Node subNode = entry.getValue();
            String key = entry.getKey();

            if (subNode.getNodeType() == NodeType.DBFUNC) {
                // 同步处理 DBFUNC 类型节点
                dbFuncNodeExecute(node, metrics, results, entry, subNode);
            } else {
                // 异步执行其他子节点处理
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    nodeExecuteV1(subNode, metrics);
                    results.put(key, subNode.getValue());
                }, asyncExecutor).exceptionally(ex -> {
                    logger.error("异步执行计算失败: 计算子节点key={}", key, ex);
                    return null;
                });
                futures.add(future);
            }
        }

        // 等待所有异步任务完成
        CompletableFuture<Void> allDone = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        try {
            allDone.join(); // 使用 join() 捕获异常更方便（适用于非 throws 场景）
        } catch (Exception ex) {
            logger.error("部分子节点计算过程中发生异常", ex);
        }

        return results;
    }

    private void setExecutionResult(Node node, Metric metric) {
        if(logger.isDebugEnabled()){
            logger.debug("unitId:{}, type:{}, value:{}", node.getUnitId(), metric.getMetricGenerateStrategy(), metric.getValue());
        }
        node.setMetricGenerateStrategy(metric.getMetricGenerateStrategy());
        node.setValue(metric.getValue());
    }

    public void nodeExecute(Node node, Map<String, Metric> metrics) {
        String key = node.getKey();
        if (node.isCalculated()) {
            return;
        }

        Metric metric;
        if (metrics.containsKey(key)) {
            metric = metrics.get(key);
        } else {
            metric = new Metric();
            metrics.put(key, metric);
            logger.info("allocate new metric, unitId:{}", key);
        }
        if (metric.getUnitId() == null) {
            metric.setUnitId(node.getUnitId());
        }

        // 场景1：无子节点时的计算，直接取值，
        // DB函数场景确实没有子节点，但不在这里处理，在Mertic构造出来后就查出结果了。
        if (node.getSubNodes().isEmpty()) {
            if (node.getExpressionWrapper() == null) {
                if (metric.getValue() == null) {
                    metric.setWarningMessage("no rule and no value");
                }
            } else {
                // 如果是无依赖，有公式(常量赋值)，并且不是调整值，则进行计算：常量赋值
                if (metric.getMetricGenerateStrategy().equals(MetricGenerateStrategy.AUTO)) {
                    executeWithAviator(node, Collections.emptyMap(), metric);
                }
            }
            logger.debug("unitId:{}, type:{}, value:{}", node.getUnitId(), metric.getMetricGenerateStrategy(),
                    metric.getValue());
            node.setMetricGenerateStrategy(metric.getMetricGenerateStrategy());
            // 20250311-mertic处理完把值塞回当前节点node
            node.setValue(metric.getValue());
            return;
        }
        // 场景2：有子节点时的计算，先计算子节点，
        Map<String, Node> subNodes = node.getSubNodes();
        Map<String, Object> parameters = new HashMap<>();
        // 子节点一定进行计算，无论父节点是否有值/是否调整
        for (Map.Entry<String, Node> entry : subNodes.entrySet()) {
            Node subNode = entry.getValue();
            if (subNode.getNodeType() == NodeType.DBFUNC) {
                dbFuncNodeExecute(node, metrics, parameters, entry, subNode);
            } else {
                nodeExecute(subNode, metrics);
                // parameter中的key一定要是"__xxx__"的形式，xxx不一定是数值
                parameters.put(entry.getKey(), subNode.getValue());
            }
        }
        // 如果不是调整值，则进行计算
        if (metric.getMetricGenerateStrategy().equals(MetricGenerateStrategy.AUTO)) {
            executeWithAviator(node, parameters, metric);
        } else {
            node.setCalculated(true);
        }
        logger.debug("unitId:{}, type:{}, value:{}", node.getUnitId(), metric.getMetricGenerateStrategy(),
                metric.getValue());
        node.setMetricGenerateStrategy(metric.getMetricGenerateStrategy());
        node.setValue(metric.getValue());
    }

    private void dbFuncNodeExecute(Node node, Map<String, Metric> metrics, Map<String, Object> parameters,
                                   Map.Entry<String, Node> entry, Node subNode) {
        Metric dbFuncMetric = metrics.get(subNode.getKey());
        if (dbFuncMetric != null) {
            Map<Integer, BigDecimal> resultMap = (Map<Integer, BigDecimal>) dbFuncMetric.getValue();
            int unitId = node.getUnitId().intValue();
            Double amount = resultMap.get(unitId) != null ? ((Number) resultMap.get(unitId)).doubleValue() : 0.0;
            if (resultMap.get(unitId) == null) {
                logger.warn("db function {} result is null,unitId:{}, then return 0.0", entry.getKey(), unitId);
            }
            parameters.put(entry.getKey(), amount);
        }
    }

    @Override
    public NodeBuffer buildDag(List<CalculationRule> calculationRules) {
        Map<String, Expression> originExpressions = compile(calculationRules);
        NodeBuffer nodeBuffer = new NodeBuffer();
        assembleDag2NodeBuffer(originExpressions, nodeBuffer);
        if (nodeBuffer.getRootNodes().isEmpty() || nodeBuffer.getLeafNodes().isEmpty()) {
            logger.error("no root node or no leaf node in dag");
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_NODE_NOT_FOUND);
        }
        return nodeBuffer;
    }

    private void assembleDag2NodeBuffer(Map<String, Expression> originExpressions, NodeBuffer nodeBuffer) {
        Pattern unitIdPattern = Pattern.compile(Constants.KEY_REGEX);
        for (Map.Entry<String, Expression> entry : originExpressions.entrySet()) {
            String key = entry.getKey();
            ExpressionWrapper expression = (ExpressionWrapper) entry.getValue();
            Node node;
            if (nodeBuffer.getAllNodes().containsKey(key)) {
                node = nodeBuffer.getAllNodes().get(key); // as another node‘s sub node
            } else {
                node = new Node();
                node.setKey(key);
            }
            node.setGroupId(expression.getGroupId());
            node.setVersionId(expression.getVersionId());
            node.setUnitId(expression.getUnitId());
            node.setExpressionWrapper(expression);
            nodeBuffer.getAllNodes().put(key, node);// 这里nodeBuffer.getAllNodes()先插入等式左边的unitId
            appendSubNodes(expression, node, nodeBuffer, unitIdPattern);
            if (expression.isContainContinue()) {
                nodeBuffer.getContinueUnitIds().add(key);
            }
        }
    }

    @Override
    public void checkCircle(Node root, CalculationContext context) {
        // TODO 错误登记要平铺记录 NodeId, SpaceId
        if (root.getNodeType() != NodeType.VALUE) {
            logger.warn("cannot check non-value node");
            return;
        }
        if (root.getSubNodes().isEmpty()) {
            return;
        }
        Set<String> visited = new HashSet<>();
        detect(root, visited, context);
    }

    @Override
    public void appendDag(NodeBuffer nodeBuffer, List<CalculationRule> newRuleList) {
        Map<String, Expression> originExpressions = compile(newRuleList);
        assembleDag2NodeBuffer(originExpressions, nodeBuffer);
    }

    private void detect(Node node, Set<String> visited, CalculationContext context) {
        Map<String, Node> subNodes = node.getSubNodes();
        if (subNodes.isEmpty()) {
            node.setValueForCheck(DETECT_VALUE_HOLDER);
            visited.add(node.getKey());
            node.setVisited(true);
            return;
        }
        if (node.isVisited() && node.getValueForCheck() == null) {
            logger.error("rule circle exists.unitId:{}", node.getKey());
            CalculationUtils.appendNodeMetricWarningMessage(node, context, "node exists circle reference");
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_RULE_CIRCLE_EXISTS);
        }
        visited.add(node.getKey());
        node.setVisited(true);
        for (Map.Entry<String, Node> entry : subNodes.entrySet()) {
            detect(entry.getValue(), visited, context);
        }
        node.setValueForCheck(DETECT_VALUE_HOLDER);
    }

    // 根据expression表达式构建subNode.
    // 20250307-将DB函数当做一个特殊节点，Node的unitId没法复用，新增一个dbName，根据节点类型判断取值
    private void appendSubNodes(ExpressionWrapper expression, Node parent, NodeBuffer nodeBuffer,
                                Pattern unitIdPattern) {
        List<String> variableNames = expression.getOriginVariableNames();
        checkLeafNode4Parent(parent, nodeBuffer, variableNames);
        checkRootNode4Parent(parent, nodeBuffer);
        for (String variableName : variableNames) {
            Node subNode;
            if (nodeBuffer.getAllNodes().containsKey(variableName)) {
                subNode = nodeBuffer.getAllNodes().get(variableName);
                subNode.getParent().add(parent);
            } else {
                subNode = new Node();
                subNode.getParent().add(parent);
                subNode.setKey(variableName);
                Matcher matcher = unitIdPattern.matcher(variableName);
                subNode.setUnitId(matcher.matches() ? Long.valueOf(matcher.group(1)) : null);
                nodeBuffer.getAllNodes().put(variableName, subNode);
            }
            parent.getSubNodes().put(variableName, subNode);
            checkLeafNode4Sub(nodeBuffer, variableName, subNode);
            checkRootNode4Sub(nodeBuffer, variableName, subNode);
        }
        // 20250310-增加DB函数的节点生成逻辑，不合适直接加在variableName中，函数不是参数的定义，会引起误解
        appendSubNode4DbFunc(expression, parent, nodeBuffer);
    }

    private void appendSubNode4DbFunc(ExpressionWrapper expression, Node parent, NodeBuffer nodeBuffer) {
        List<String> dbFuncNames = expression.getDbFuncNames();
        checkLeafNode4Parent(parent, nodeBuffer, dbFuncNames);
        // dbFuncName的格式为 “__DBFUNC(get_%s_%s_f)__”
        for (String dbFuncName : dbFuncNames) {
            Node subNode;
            if (nodeBuffer.getAllNodes().containsKey(dbFuncName)) {
                subNode = nodeBuffer.getAllNodes().get(dbFuncName);
                subNode.getParent().add(parent);
            } else {
                subNode = new Node();
                subNode.getParent().add(parent);
                subNode.setKey(dbFuncName);
                Matcher dbFuncMatcher = dbFuncPattern.matcher(dbFuncName);
                subNode.setNodeType(NodeType.DBFUNC);
                subNode.setFuncName(dbFuncMatcher.matches() ? dbFuncMatcher.group(1) : null);
                nodeBuffer.getAllNodes().put(dbFuncName, subNode);
            }
            parent.getSubNodes().put(dbFuncName, subNode);
            checkLeafNode4Sub(nodeBuffer, dbFuncName, subNode);
            checkRootNode4Sub(nodeBuffer, dbFuncName, subNode);
        }
    }

    private void checkRootNode4Sub(NodeBuffer nodeBuffer, String variableName, Node subNode) {
        nodeBuffer.getRootNodes().remove(variableName);
        nodeBuffer.getNonRootNodes().put(variableName, subNode);
    }

    private void checkLeafNode4Sub(NodeBuffer nodeBuffer, String variableName, Node subNode) {
        if (!nodeBuffer.getNonLeafNodes().containsKey(variableName)) {
            nodeBuffer.getLeafNodes().put(variableName, subNode);
        }
    }

    private void checkRootNode4Parent(Node parent, NodeBuffer nodeBuffer) {
        if (!nodeBuffer.getNonRootNodes().containsKey(parent.getKey())) {
            nodeBuffer.getRootNodes().put(parent.getKey(), parent);
        }
    }

    private void checkLeafNode4Parent(Node parent, NodeBuffer nodeBuffer, List<String> variableNames) {
        String key = parent.getKey();
        if (variableNames.size() > 0) {
            nodeBuffer.getNonLeafNodes().put(key, parent);
            nodeBuffer.getLeafNodes().remove(key); // if leaf node not empty,then remove parent node from leafNodes
        } else {
            // if no variable,then put into leafNodes.
            if (!nodeBuffer.getNonLeafNodes().containsKey(key)) {
                nodeBuffer.getLeafNodes().put(key, parent);
            }
        }
    }

    public void executeWithAviator(Node node, Map<String, Object> parameters, Metric metric) {
        node.setCalculated(true);
        if (checkNullValues(node, parameters, metric)) {
            logger.warn("parameter is incomplete,node is {}", node.getKey());
            return;
        }
        Object result;
        try {
            Map<String, Object> requestParameters = buildParameters(node, parameters);
            result = node.getExpressionWrapper().getExpression().execute(requestParameters);
            if (result instanceof Double) {
                // if infinite or NaN,then return 0
                if (!Double.isFinite((Double) result)) {
                    result = Double.valueOf(0);
                }
            } else if (CONTINUE_VALUE.equals(String.valueOf(result))) {
                node.setContinue(true);
                return;
            }
            metric.setValue(result);
        } catch (Exception e) {
            if (logger.isDebugEnabled()) {
                logger.debug("execution error,then continues,cause:{}", e.getMessage(), e);
            }
            if (metric.getValue() == null) {
                metric.setValue(
                        MetricDataType.NUMERIC.equals(metric.getMetricDataType()) ? BigDecimal.ZERO : Strings.EMPTY);
                logger.warn("unitId:{} exception occurred when calculating, set it {}", metric.getUnitId(),
                        metric.getValue());
            }
            metric.appendWarningMessage("exec failed,cause:" + e.getMessage());
        } finally {
            if (logger.isDebugEnabled()) {
                logCalDetail(node, parameters, metric);
            }
        }
    }

    private void logCalDetail(Node node, Map<String, Object> parameters, Metric metric) {
        String ruleContent = node.getExpressionWrapper().getCompressedRuleContent();
        String continueMsg = node.isContinue() ? ":" + CONTINUE_IN_CHINESE : StringUtil.EMPTY;
        if (StringUtil.isNullOrEmpty(ruleContent)) {
            logger.error("node: [{}], ruleContent is null", node);
            logger.debug(node.getKey() + ":" + metric.getValue() + continueMsg + "=" + ruleContent);
            return;
        }
        Map<String, String> variableNameHolders = node.getExpressionWrapper().getVariableNameHolders();
        for (Map.Entry<String, String> entry : variableNameHolders.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue() + ":" + parameters.get(entry.getValue());
            ruleContent = ruleContent.replace(key, value);
        }
        logger.debug(node.getKey() + ":" + metric.getValue() + continueMsg + "=" + ruleContent);
    }

    private Map<String, Object> buildParameters(Node node, Map<String, Object> parameters) {
        if (aviatorExecutorProperties.isCachedExpressionEnabled()) {
            return revertToHolderParameters(node, parameters);
        }
        return parameters;
    }

    private Map<String, Object> revertToHolderParameters(Node node, Map<String, Object> parameters) {
        ExpressionWrapper expressionWrapper = node.getExpressionWrapper();
        Map<String, String> variableNameHolders = expressionWrapper.getVariableNameHolders();
        if (variableNameHolders == null || variableNameHolders.isEmpty() || parameters.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, Object> holderParameters = new HashMap<>();
        for (Map.Entry<String, String> entry : variableNameHolders.entrySet()) {
            // TODO：20250311-根据variableNameHolders中的replaceId和unitId的关系，将value塞到对应的replaceId中
            holderParameters.put(entry.getKey(), parameters.get(entry.getValue()));
        }
        return holderParameters;
    }

    private boolean checkNullValues(Node node, Map<String, Object> parameters, Metric metric) {
        return parameters.entrySet().stream().filter(entry -> {
            boolean isNullValue = entry.getValue() == null;
            if (entry.getValue() == null) {
                logger.warn("parameter [{}] is null with node[{}]", entry.getKey(), node.getKey());
                metric.appendWarningMessage("reference cell:[" + entry.getKey() + "] is empty");
            }
            return isNullValue;
        }).count() > 0;
    }

    // 根据公式构建 map<unitId,计算表达式Expression>
    public Map<String, Expression> compile(List<CalculationRule> rules) {
        int size = rules.size();
        logger.debug("prepare to compile aviator expressions,ruleSize:{}", size);
        Map<String, Expression> originExpressions = new ConcurrentHashMap<>();
        if (size > aviatorExecutorProperties.getParallelCompileRulesMinSize()) {
            parallelCompile(rules, originExpressions);
        } else {
            batchCompile(0, size, rules, originExpressions);
        }
        logger.debug("compile aviator expressions successfully.ruleSize:{},expressionSize:{}", size,
                originExpressions.size());
        return originExpressions;
    }

    public void parallelCompile(List<CalculationRule> rules, Map<String, Expression> originExpressions) {
        int ruleSize = rules.size();
        int threadSize = aviatorExecutorProperties.getParallelCompileThreadSize();
        CountDownLatch countDownLatch = new CountDownLatch(threadSize);
        int increment = ruleSize / threadSize;
        for (int i = 0; i < threadSize; i++) {
            int start = i * increment;
            int end = start + increment;
            end = ruleSize - end > increment ? end : ruleSize;
            compileExecutors
                    .execute(new AviatorCompileTask(countDownLatch, this, start, end, rules, originExpressions));
        }
        try {
            countDownLatch.await(aviatorExecutorProperties.getParallelCompileTimeoutMs(), TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            logger.error("compile the aviator expressions timeout, rule size:{}. error: {}", ruleSize, e);
            throw new CalculationException("compile the aviator expressions timeout");
        }
        logger.debug("parallel compile the aviator expressions successfully.rule size:{}", ruleSize);
    }

    public void batchCompile(int start, int end, List<CalculationRule> rules,
                             Map<String, Expression> originExpressions) {
        for (int i = start; i < end; i++) {
            CalculationRule rule = rules.get(i);
            ExpressionWrapper wrapper = getExpressionWrapper(rule);
            wrapper.setUnitId(rule.getUnitId());
            wrapper.setBusinessVersionId(rule.getBusinessVersionId());
            wrapper.setVersionId(rule.getVersionId());
            wrapper.setGroupId(rule.getGroupId());
            // 这里塞入的等式左边的unitId和对应的计算公式
            originExpressions.put(String.format(Constants.KEY_FORMAT, rule.getUnitId()), wrapper);
        }
    }

    // 20250310：想法，从wrapper入手。考虑在wrapper中多加一个属性记录函数
    private ExpressionWrapper getExpressionWrapper(CalculationRule rule) {
        ExpressionWrapper wrapper = new ExpressionWrapper();
        try {
            String toCompileRuleContent = buildExpressionContent(wrapper, rule);
            // 待优化流程
            boolean containsContinueKeyWord = toCompileRuleContent.contains(CONTINUE_KEYWORD);
            if (containsContinueKeyWord) {
                // rule后续用于转成 前台渲染的展示公式
                rule.setContent(rule.getContent().replace(CONTINUE_KEYWORD, CONTINUE_IN_CHINESE));
                toCompileRuleContent = toCompileRuleContent.replace(CONTINUE_KEYWORD, CONTINUE_VALUE_WITH_QUOT); // toCompileRuleContent是用于计算的表达式
                wrapper.setContainContinue(true);
            }
            // TODO：这里evaluate编译失败，需要检查内容
            Expression expression = evaluator.compile(toCompileRuleContent);
            String compressedRuleContent = toCompileRuleContent.intern();
            wrapper.setExpression(expression);
            wrapper.setCompressedRuleContent(compressedRuleContent);
        } catch (ExpressionSyntaxErrorException e) {
            wrapper.setInvalid(true);
            wrapper.setExpression(emptyExpression);
            logger.warn("rule compile failed,ruleId:{},unitId:{},content:{},cause:{}", rule.getRuleId(),
                    rule.getUnitId(), rule.getContent(), e.getMessage(), e);
        }
        return wrapper;
    }

    private String buildExpressionContent(ExpressionWrapper wrapper, CalculationRule rule) {
        if (aviatorExecutorProperties.isCachedExpressionEnabled()) {
            return buildContentWithHolder(wrapper, rule);
        }
        return rule.getContent();
    }

    /**
     * before: __1234__ + __2345__ + __3456__ || DBFUNC(****)
     * after: __1__ + __2__ + __3__ || __1__
     * <p>
     * before: __444__ + __555__ + __666__
     * after: __1__ + __2__ + __3__
     * <p>
     * reuse the aviator expression when cacheExpressionEnabled is true
     *
     * @param wrapper
     * @return String
     */
    private String buildContentWithHolder(ExpressionWrapper wrapper, CalculationRule rule) {
        Matcher matcher = Constants.FULL_KEY_PATTERN.matcher(rule.getContent());
        int index = 1;
        StringBuffer contentBuffer = new StringBuffer();
        Map<String, String> variableNameHolders = new HashMap<>();
        Set<String> originVariableNames = new HashSet<>();
        // 这里只替换aviator表达式中含有__unitId__部分
        while (matcher.find()) {
            String replace = String.format("__%1$s__", index++);
            String unitId = matcher.group();
            // variableNameHolders记录自生成ID和实际unitId的关系：map<__1__:__XX__>
            variableNameHolders.put(replace, unitId);
            originVariableNames.add(unitId);
            matcher.appendReplacement(contentBuffer, replace);
        }
        // 将剩余没有被匹配的字符添加到 contentBuffer 中
        matcher.appendTail(contentBuffer);
        wrapper.setVariableNameHolders(variableNameHolders);
        wrapper.setOriginVariableNames(originVariableNames.stream().collect(Collectors.toList()));
        String content4VariableTrans = contentBuffer.toString();
        // 在这里处理DB函数,分别处理rule中的content和用于计算的content4VariableTrans
        return buildContent4DbFunc(wrapper, rule, content4VariableTrans, index);
    }

    // 将用于前端展示的content，DB函数部分替换为SUM/AVG(<...>)
    // 将用于后续计算的content4VariableTrans，DB函数部分替换为DBFUNC(函数名)；并单独记录DBFUNC(函数名)
    private String buildContent4DbFunc(ExpressionWrapper wrapper, CalculationRule rule, String content4VariableTrans,
                                       int index) {
        boolean containsDbFuncKeyWord = content4VariableTrans.contains(DBFUNC_KEYWORD);
        if (!containsDbFuncKeyWord) {
            return content4VariableTrans;
        }
        rule.setContent(processDbFuncStr(wrapper, rule.getContent(), index, false));
        return processDbFuncStr(wrapper, content4VariableTrans, index, true);
    }

    // DB函数部署得到的的aviator表达式为"DBFUNC(get_AvgFunc_5199f905085a4426996671b91ed64e09_f,
    // <modelId:modelId，dim:{member,...},...,dim{member,...}>)"
    private static String processDbFuncStr(ExpressionWrapper wrapper, String input, int index, boolean isAviator) {
        Pattern pattern = Pattern.compile("DBFUNC\\(get_([a-zA-Z]+)_([0-9a-f-]+)_f,\\s*" + "<(.*?)>\\)");
        Matcher matcher = pattern.matcher(input);
        StringBuffer contentBuffer = new StringBuffer();
        Set<String> dbFuncNames = new HashSet<>();
        while (matcher.find()) {
            String funcType = matcher.group(1);
            if (isAviator) {
                // 用于计算aviator表达式：dnFunc部分被替换为__DBFUNC(xxx)__
                String uuid = matcher.group(2);
                String dbFuncName = String.format("__DBFUNC(get_%s_%s_f)__", funcType, uuid);
                String replace = String.format("__%1$s__", index++);
                matcher.appendReplacement(contentBuffer, replace);
                dbFuncNames.add(dbFuncName);
                wrapper.getVariableNameHolders().put(replace, dbFuncName);
            } else {
                // 用于预览展示的表达式：dnFunc部分被替换为Sum(<model:modelId,dimInfos...>)
                String modelParams = matcher.group(3);
                String dbFuncTypeBusinessStr = funcType.equals("AvgFunc") ? "AVG" : "SUM";
                String formatBusinessStr = String.format("%s(<%s>)", dbFuncTypeBusinessStr, modelParams);
                matcher.appendReplacement(contentBuffer, formatBusinessStr);
            }
        }
        if (!dbFuncNames.isEmpty()) {
            wrapper.setDbFuncNames(dbFuncNames.stream().collect(Collectors.toList()));
        }
        matcher.appendTail(contentBuffer);
        return contentBuffer.toString();
    }

    private Expression buildEmptyExpressSion() {
        return getExpression();
    }

    @NotNull
    private static Expression getExpression() {
        return new Expression() {
            @Override
            public Object execute(Map<String, Object> map) {
                throw new ExpressionRuntimeException("empty expression not supported");
            }

            @Override
            public Object execute() {
                throw new ExpressionRuntimeException("empty expression not supported");
            }

            @Override
            public String getSourceFile() {
                throw new ExpressionRuntimeException("empty expression not supported");
            }

            @Override
            public List<String> getVariableNames() {
                return Collections.emptyList();
            }

            @Override
            public List<String> getVariableFullNames() {
                return Collections.emptyList();
            }

            @Override
            public Map<String, Object> newEnv(Object... objects) {
                return Collections.emptyMap();
            }

            @Override
            public String addSymbol(String s) {
                throw new ExpressionRuntimeException("empty expression not supported");
            }
        };
    }
}
