/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.workflow.deploy.impl;

import com.huawei.it.finance.opt.base.application.service.ICalculationScriptApplicationService;
import com.huawei.it.finance.opt.base.entity.CalculationScript;
import com.huawei.it.finance.opt.base.workflow.deploy.DslDeployContext;
import com.huawei.it.finance.opt.base.workflow.deploy.dto.DeployCellParam;
import com.huawei.it.finance.opt.base.workflow.deploy.dto.WorkflowParam;
import com.huawei.it.finance.opt.dsl.complier.ast.node.ComputeRule;
import com.huawei.it.finance.opt.dsl.complier.output.DeployEffect;
import com.huawei.it.finance.opt.dsl.service.CompileService;
import com.huawei.it.finance.opt.fsp.pub.common.exception.DataSourceException;
import com.huawei.it.finance.opt.task.entity.QuartzTaskInfo;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.core.util.exception.NoDataFoundException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.MDC;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

@ExtendWith(MockitoExtension.class)
public class DeployCellActivityImplTest {

    @Mock
    private CompileService compileService;

    @Mock
    private ICalculationScriptApplicationService iCalculationScriptApplicationService;

    @InjectMocks
    private DeployCellActivityImpl deployCellActivity;

    private DeployCellParam deployCellParam;
    private WorkflowParam workflowParam;
    private DslDeployContext dslDeployContext;
    private List<ComputeRule> computeRules;
    private DeployEffect deployEffect;
    private CalculationScript calculationScript;
    private QuartzTaskInfo taskInfo;
    private AutoCloseable mockitoCloseable;

    @AfterEach
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockitoCloseable = openMocks(this);
        deployCellParam = new DeployCellParam();
        deployCellParam.setStartIndex(0);
        deployCellParam.setEndIndex(10);
        deployCellParam.setWorkspaceId(1L);
        deployCellParam.setScriptId(123L);
        deployCellParam.setTaskId(456L);
        deployCellParam.setModelId("model-001");
        deployCellParam.setModelName("测试模型");
        deployCellParam.setDynamicDB("test_db");
        deployCellParam.setJobName("test-job");
        deployCellParam.setWorkFlowId(789);
        deployCellParam.setActivityId(1);

        workflowParam = new WorkflowParam();
        workflowParam.setWorkspaceId(1L);
        workflowParam.setScriptId(123L);
        workflowParam.setTaskId(456L);
        workflowParam.setModelId("model-001");
        workflowParam.setModelName("测试模型");
        workflowParam.setDynamicDB("test_db");
        workflowParam.setJobName("test-job");
        workflowParam.setWorkFlowId(789);

        dslDeployContext = mock(DslDeployContext.class);
        computeRules = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            computeRules.add(mock(ComputeRule.class));
        }
 //       when(dslDeployContext.getComputeRules()).thenReturn(computeRules);

        taskInfo = new QuartzTaskInfo();
        taskInfo.setJobName("test-job");
    //    when(dslDeployContext.getTaskInfo()).thenReturn(taskInfo);

        deployEffect = mock(DeployEffect.class);

        calculationScript = CalculationScript.builder()
                .scriptId(123L)
                .modelId("model-001")
                .build();
        calculationScript.setWorkspaceId(1L);
        calculationScript.setDbCode("test_db");
    }

    @Test
    @DisplayName("test_deployCellExecute_should_return_early_when_startIndex_is_null")
    void test_deployCellExecute_should_return_early_when_startIndex_is_null() {
        try (MockedStatic<MDC> mdcMockedStatic = mockStatic(MDC.class)) {
            // Set startIndex to null
            deployCellParam.setStartIndex(null);

            // Execute test
            deployCellActivity.deployCellExecute(deployCellParam);

            // Verify no interactions with other methods
            verify(compileService, times(0)).compileDSL(any());
            verify(compileService, times(0)).deployDSL(anyList(), any());
            verify(iCalculationScriptApplicationService, times(0)).saveCalculationRules(any(), any(), any(), any());
        } catch (DataSourceException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    @DisplayName("test_deployCellExecute_should_handle_NoDataFoundException")
    void test_deployCellExecute_should_handle_NoDataFoundException() throws Exception {
        try (MockedStatic<MDC> mdcMockedStatic = mockStatic(MDC.class);
             MockedStatic<RequestContextManager> requestContextManagerMockedStatic = mockStatic(RequestContextManager.class)) {

            // Create a spy of the deployCellActivity to mock protected methods
            DeployCellActivityImpl deployCellActivitySpy = spy(deployCellActivity);

            // Mock the protected method to throw NoDataFoundException
            doThrow(new NoDataFoundException("Task not found", "456"))
                    .when(deployCellActivitySpy).buildDeployContext(any(DeployCellParam.class));

            // Execute test
            deployCellActivitySpy.deployCellExecute(deployCellParam);

            // Verify MDC cleanup
            mdcMockedStatic.verify(() -> MDC.remove(anyString()), times(3));
            requestContextManagerMockedStatic.verify(RequestContextManager::removeCurrent, times(1));
        }
    }

    @Test
    @DisplayName("test_deployCellExecute_should_handle_generic_Exception")
    void test_deployCellExecute_should_handle_generic_Exception() throws Exception {
        try (MockedStatic<MDC> mdcMockedStatic = mockStatic(MDC.class);
             MockedStatic<RequestContextManager> requestContextManagerMockedStatic = mockStatic(RequestContextManager.class)) {

            // Create a spy of the deployCellActivity to mock protected methods
            DeployCellActivityImpl deployCellActivitySpy = spy(deployCellActivity);

            // Mock the protected method to throw RuntimeException
            doThrow(new RuntimeException("Test exception"))
                    .when(deployCellActivitySpy).buildDeployContext(any(DeployCellParam.class));

            // Execute test
            deployCellActivitySpy.deployCellExecute(deployCellParam);

            // Verify MDC cleanup
            mdcMockedStatic.verify(() -> MDC.remove(anyString()), times(3));
            requestContextManagerMockedStatic.verify(RequestContextManager::removeCurrent, times(1));
        }
    }

    @Test
    @DisplayName("test_buildComputeRules_should_handle_endIndex_greater_than_total")
    void test_buildComputeRules_should_handle_endIndex_greater_than_actual_total() throws Exception {
        // Set up test data
        deployCellParam.setStartIndex(0);
        deployCellParam.setEndIndex(0); // Greater than the total (20)

        // Use reflection to access private method
        java.lang.reflect.Method buildComputeRulesMethod = DeployCellActivityImpl.class.getDeclaredMethod(
                "buildComputeRules", DeployCellParam.class, DslDeployContext.class);
        buildComputeRulesMethod.setAccessible(true);

        // Execute test
        List<ComputeRule> result = (List<ComputeRule>) buildComputeRulesMethod.invoke(
                deployCellActivity, deployCellParam, dslDeployContext);

        // Verify result
        assertEquals(0, result.size(), "Should return sublist from startIndex to total");

        // Verify expandComputeUnit was called on each rule
        for (ComputeRule rule : result) {
            verify(rule, times(1)).expandComputeUnit();
        }
    }

    @Test
    @DisplayName("test_buildComputeRules_should_handle_boundary_condition_with_equal_indices")
    void test_buildComputeRules_should_handle_boundary_condition_with_equal_indices() throws Exception {
        // Set up test data with startIndex equal to endIndex
        deployCellParam.setStartIndex(5);
        deployCellParam.setEndIndex(5);

        // Use reflection to access private method
        java.lang.reflect.Method buildComputeRulesMethod = DeployCellActivityImpl.class.getDeclaredMethod(
                "buildComputeRules", DeployCellParam.class, DslDeployContext.class);
        buildComputeRulesMethod.setAccessible(true);

        // Execute test
        List<ComputeRule> result = (List<ComputeRule>) buildComputeRulesMethod.invoke(
                deployCellActivity, deployCellParam, dslDeployContext);

        // Verify result - should be empty list since subList(5,5) returns empty list
        assertEquals(0, result.size(), "Should return empty list when startIndex equals endIndex");
    }

    @Test
    @DisplayName("test_saveRuleResult_should_execute_successfully")
    void test_saveRuleResult_should_execute_successfully() throws Exception {
        // Use reflection to access private method
        java.lang.reflect.Method saveRuleResultMethod = DeployCellActivityImpl.class.getDeclaredMethod(
                "saveRuleResult", WorkflowParam.class, String.class);
        saveRuleResultMethod.setAccessible(true);

        // Execute test
        saveRuleResultMethod.invoke(deployCellActivity, workflowParam, "test_db");

        // Verify application service calls
        verify(iCalculationScriptApplicationService, times(1)).deleteTemporalRules(any(CalculationScript.class), any(QuartzTaskInfo.class));
        verify(iCalculationScriptApplicationService, times(1)).migrateDataToRuleTable(any(CalculationScript.class), eq(789));
    }

    @Test
    @DisplayName("test_saveCellResult_should_execute_successfully")
    void test_saveCellResult_should_execute_successfully() throws Exception {
        // Mock queryWorkflowInfo to return a list of table names
        List<String> tableNames = Arrays.asList("table1", "table2");
        when(iCalculationScriptApplicationService.queryWorkflowInfo(anyLong(), anyInt())).thenReturn(tableNames);

        // Create a DeployCellActivityImpl spy to avoid NullPointerException
        DeployCellActivityImpl deployCellActivitySpy = spy(deployCellActivity);

        // Execute test directly without reflection
        deployCellActivitySpy.saveCellResult(workflowParam);

        // Verify application service calls
        verify(iCalculationScriptApplicationService, times(1)).queryWorkflowInfo(eq(1L), eq(789));
        verify(iCalculationScriptApplicationService, times(1)).migrateDataToCalculationTable(eq(tableNames), eq(789));
        verify(iCalculationScriptApplicationService, times(1)).migrateDataToGlobalTable(eq(789));

    }

    @Test
    @DisplayName("test_saveCellResult_should_handle_empty_table_list")
    void test_saveCellResult_should_handle_empty_table_list() throws Exception {

        // Mock queryWorkflowInfo to return an empty list
        List<String> emptyTableNames = new ArrayList<>();
        when(iCalculationScriptApplicationService.queryWorkflowInfo(anyLong(), anyInt())).thenReturn(emptyTableNames);

        // Create a DeployCellActivityImpl spy to avoid NullPointerException
        DeployCellActivityImpl deployCellActivitySpy = spy(deployCellActivity);

//        // Execute test directly without reflection
        deployCellActivitySpy.saveCellResult(workflowParam);

        // Verify application service calls
        verify(iCalculationScriptApplicationService, times(1)).queryWorkflowInfo(eq(1L), eq(789));
        verify(iCalculationScriptApplicationService, times(1)).migrateDataToCalculationTable(eq(emptyTableNames), eq(789));
        verify(iCalculationScriptApplicationService, times(1)).migrateDataToGlobalTable(eq(789));
    }
}
