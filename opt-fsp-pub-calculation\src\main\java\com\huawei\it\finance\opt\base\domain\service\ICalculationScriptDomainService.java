/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.domain.service;

import com.huawei.it.finance.opt.base.entity.CalculationScript;
import com.huawei.it.finance.opt.base.entity.CalculationScriptModel;
import com.huawei.it.finance.opt.base.entity.CalculationScripts;
import com.huawei.it.finance.opt.base.entity.CalculationScriptsImport;
import com.huawei.it.finance.opt.base.entity.ScriptModel;
import com.huawei.it.finance.opt.base.vo.CalculationScriptRelativeModels;
import com.huawei.it.finance.opt.base.workflow.deploy.DslDeployContext;
import com.huawei.it.finance.opt.base.workflow.deploy.dto.DeployCellParam;
import com.huawei.it.finance.opt.common.exception.AlterVariableException;
import com.huawei.it.finance.opt.domain.entity.ImportTask;
import com.huawei.it.finance.opt.dsl.complier.input.DSLSourceInfo;
import com.huawei.it.finance.opt.dsl.complier.output.DeployEffect;
import com.huawei.it.finance.opt.fsp.pub.common.exception.DataSourceException;
import com.huawei.it.finance.opt.task.entity.QuartzTaskInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * icalculation script domain service
 *
 * <AUTHOR>
 * @since 2024/03/11
 */
public interface ICalculationScriptDomainService {
    /**
     * 根据ID查询计算脚本
     *
     * @param scriptId 脚本ID
     * @return CalculationScript
     */
    CalculationScript getCalculationScriptById(long scriptId);

    /**
     * 查询规则脚本列表
     *
     * @param calculationScript 计算脚本
     * @return List<CalculationScript>
     */
    List<CalculationScript> getCalculationScripts(CalculationScript calculationScript);

    /**
     * 保存规则脚本，创建规则
     *
     * @param calculationScript 计算脚本
     */
    CalculationScript saveCalculationScript(CalculationScript calculationScript);

    /**
     * 移动规则脚本
     *
     * @param calculationScript 计算脚本
     */
    void moveCalculationScript(CalculationScript calculationScript);

    /**
     * 重命名规则脚本
     *
     * @param calculationScript 计算脚本
     */
    void renameCalculationScript(CalculationScript calculationScript);

    /**
     * 粘贴规则脚本
     *
     * @param calculationScript 计算脚本
     */
    void pasteCalculationScript(CalculationScript calculationScript);

    /**
     * 删除规则脚本
     *
     * @param scriptId 脚本ID
     */
    void deleteCalculationScript(long scriptId, long workspaceId);

    /**
     * 单个规则脚本部署-单规则+批量部署单次循环
     *
     * @return 返回部署后的规则数量
     */
    int singleDeployCalculationScript(CalculationScript calculationScript, DSLSourceInfo dslSourceInfo,
                                      QuartzTaskInfo quartzTaskInfo) throws AlterVariableException, DataSourceException;

    void finishDeploy(CalculationScript calculationScript, DSLSourceInfo dslSourceInfo, QuartzTaskInfo quartzTaskInfo);

    /**
     * 规则更新状态
     *
     * @param calculationScript
     * @return
     */
    int updateStatus(CalculationScript calculationScript);


    void saveCalculationGraphToGesForTemporal(CalculationScript calculationScript, DslDeployContext context);

    /**
     * 查询一个规则是否正在部署
     *
     * @param scriptId
     * @param workspaceId
     * @return
     */
    boolean isCalculationScriptDeploying(long scriptId, long workspaceId);

    /**
     * 回滚规则，增加对状态的处理
     *
     * @param calculationScript 计算脚本
     */
    CalculationScript rollbackCalculationScript(CalculationScript calculationScript);

    /**
     * 部署规则后，再次修改DSL内容，规则修改为待部署状态
     *
     * @param calculationScript 计算脚本
     */
    void updateCalculationScriptAfterDeployment(CalculationScript calculationScript);

    /**
     * 规则脚本下载模板
     *
     * @param response HTTP响应
     */
    void downloadCalculationScriptTemplate(HttpServletResponse response);

    /**
     * 规则脚本导出功能
     *
     * @param calculationScripts 多个计算脚本
     */
    void exportCalculationScripts(CalculationScripts calculationScripts);

    /**
     * 规则脚本导入功能
     *
     * @param file                     file
     * @param importTask               importTask
     * @param calculationScriptsImport calculationScriptsImport
     * @param workspaceId              作业空间ID
     */
    void importCalculationScripts(MultipartFile file, ImportTask importTask,
                                  CalculationScriptsImport calculationScriptsImport, long workspaceId);

    /**
     * 规则导入导出打印错误日志功能
     *
     * @param importTask               导入任务
     * @param calculationScriptsImport 导入参数实体
     * @param ex                       异常信息
     * @param extraInfo                异常额外信息，用于定位错误数据的位置
     */
    void printImportLogAndUpdateTask(ImportTask importTask, CalculationScriptsImport calculationScriptsImport,
                                     Throwable ex, String extraInfo);

    /**
     * 根据多个规则ID，批量查询对应的多个脚本信息及关联的模型、文件夹等信息。
     *
     * @param calculationScripts
     * @return
     */
    List<CalculationScriptModel> getCalculationScriptsByList(List<CalculationScript> calculationScripts);

    /**
     * 查询某个作业空间下，与某些模型相关联的规则列表
     *
     * @param modelIds
     * @return
     */
    List<CalculationScriptModel> getCalculationScriptsByModelIds(List<String> modelIds, long workspaceId);

    /**
     * 查询某个作业空间下，所有的规则列表及相关跨模型列表
     *
     * @return
     */
    List<CalculationScriptRelativeModels> findALlScriptsWithRelativeModelsByWorkspaceId(long workspaceId);

    /**
     * 查询某个作业空间下，所有指定模型的规则列表
     *
     * @param calculationScript 计算脚本
     * @return List<CalculationScript>
     */
    List<CalculationScript> getCalculationScripts(CalculationScript calculationScript, List<String> modelIds);

    /**
     * 部署空规则时，按照作业空间id和规则脚本ID，删除对应的计算对象，包括：
     * 1、CalculationCells
     * 2、CalculationRules
     * 3、Entities
     * 4、Relationships
     *
     * @param scriptId
     * @param workspaceId
     */
    void deleteCalculationObjectsWhileDeployNullScript(long scriptId, long workspaceId);

    /**
     * 根据多个规则ID，批量查询对应的多个脚本信息及关联的模型、文件夹等信息。
     *
     * @param calculationScripts
     * @return
     */
    List<CalculationScriptModel> getSortedCalculationScriptsByList(List<CalculationScript> calculationScripts,
                                                                   long workspaceId);

    void updateCalculationScriptAndDraft(CalculationScript script);

    /**
     * 解析出DSL规则源码中，包含了关键字 Model('模型名称') SUM('模型名称') 中涉及到的关联模型名称。
     * 因为在同一个作业空间下，模型名称不重复，所以只需要解析出不重名的名称即可
     *
     * @param scriptModel
     * @return
     */
    Set<String> analysisRelatedModels(ScriptModel scriptModel);

    /**
     * 刷新任务进度
     *
     * @param quartzTaskInfo
     * @param taskStepDescription
     * @param roughStep
     * @param totalSteps
     */
    void updateTaskProgress(QuartzTaskInfo quartzTaskInfo, String taskStepDescription, int roughStep, int totalSteps);

    void saveCalculationRules(CalculationScript calculationScript, DeployEffect deployEffect,
                              QuartzTaskInfo quartzTaskInfo) throws DataSourceException;

    void saveCalculationRulesForTemporal(CalculationScript calculationScript, DeployEffect deployEffect,
                                         DslDeployContext context, DeployCellParam param) throws DataSourceException;

    void deleteHistoryDbFuncRules(CalculationScript calculationScript, QuartzTaskInfo quartzTaskInfo);

    void migrateDataToRuleTable(CalculationScript calculationScript, Integer workFlowId);

    List<String> queryWorkflowInfo(Long workspaceId, Integer workFlowId);

    int deleteWorkflowInfo(Long workspaceId, Integer workFlowId);

    void migrateDataToCalculationTable(List<String> temporalTableName, Integer workFlowId);

    void migrateDataToGlobalTable(Integer workFlowId);

    int queryRulesNumByScirptIdAndWorkspaceId(Long workspaceId, Long scriptId);
}