/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.application.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.huawei.it.common.exception.ApplicationException;
import com.huawei.it.finance.opt.application.service.IImportTaskAppService;
import com.huawei.it.finance.opt.base.application.service.ICalculationScriptApplicationService;
import com.huawei.it.finance.opt.base.domain.service.ICalculationScriptDomainService;
import com.huawei.it.finance.opt.base.domain.service.ICalculationScriptDraftDomainService;
import com.huawei.it.finance.opt.base.domain.service.impl.DslDeployDomainService;
import com.huawei.it.finance.opt.base.entity.CalculationScript;
import com.huawei.it.finance.opt.base.entity.CalculationScriptDraft;
import com.huawei.it.finance.opt.base.entity.CalculationScriptModel;
import com.huawei.it.finance.opt.base.entity.CalculationScripts;
import com.huawei.it.finance.opt.base.entity.CalculationScriptsImport;
import com.huawei.it.finance.opt.base.entity.RulesCompare;
import com.huawei.it.finance.opt.base.entity.ScriptFolder;
import com.huawei.it.finance.opt.base.entity.ScriptModel;
import com.huawei.it.finance.opt.base.exception.CalculationException;
import com.huawei.it.finance.opt.base.protocol.ApiResponse;
import com.huawei.it.finance.opt.base.util.CalculationScriptConstants;
import com.huawei.it.finance.opt.base.vo.CalculationScriptRelativeModels;
import com.huawei.it.finance.opt.base.vo.ScriptModelVO;
import com.huawei.it.finance.opt.base.workflow.deploy.DslDeployContext;
import com.huawei.it.finance.opt.base.workflow.deploy.dto.DeployCellParam;
import com.huawei.it.finance.opt.common.exception.AlterVariableCode;
import com.huawei.it.finance.opt.common.exception.AlterVariableException;
import com.huawei.it.finance.opt.domain.entity.ImportTask;
import com.huawei.it.finance.opt.domain.enums.ExportTaskEnum;
import com.huawei.it.finance.opt.dsl.complier.input.DSLSourceInfo;
import com.huawei.it.finance.opt.dsl.complier.objcode.PreviewRule;
import com.huawei.it.finance.opt.dsl.complier.output.DeployEffect;
import com.huawei.it.finance.opt.dsl.complier.output.PreviewEffect;
import com.huawei.it.finance.opt.dsl.exception.OptCalculationDslException;
import com.huawei.it.finance.opt.dsl.handler.ScriptVerifyChain;
import com.huawei.it.finance.opt.dsl.service.CompileService;
import com.huawei.it.finance.opt.fsp.pub.common.exception.DataSourceException;
import com.huawei.it.finance.opt.task.entity.QuartzTaskInfo;
import com.huawei.it.finance.opt.task.util.TaskTypeEnum;
import com.huawei.it.finance.opt.tech.enums.ModuleEnum;
import com.huawei.it.finance.opt.tech.exception.PubErrorCode;
import com.huawei.it.finance.opt.tech.service.IWorkspaceCommonService;
import com.huawei.it.finance.opt.tech.util.DataSourceUtil;
import com.huawei.it.finance.opt.tech.util.JsonUtils;
import com.huawei.it.finance.opt.tech.util.UserUtil;
import com.huawei.it.finance.pub.dimmodel.application.dto.response.DimModelResponse;
import com.huawei.it.finance.pub.dimmodel.application.service.IDimModelApplicationService;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.util.PathUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.huawei.it.finance.opt.base.workflow.deploy.impl.DslDeployWorkflowImpl.isWorkflowDeploy;
import static com.huawei.it.finance.opt.tech.util.DataSourceUtil.getDynamicDBFromRequestContext;

/**
 * calculation script application service
 *
 * <AUTHOR>
 * @since 2024/03/11
 */
@Slf4j
@Service
@DS("#mysession.dynamicDB")
public class CalculationScriptApplicationService implements ICalculationScriptApplicationService {
    @Autowired
    private ICalculationScriptDomainService iCalculationScriptDomainService;

    @Autowired
    private DslDeployDomainService dslDeployDomainService;

    @Autowired
    private ICalculationScriptDraftDomainService iCalculationScriptDraftDomainService;

    @Autowired
    private ScriptVerifyChain scriptVerifyChain;

    @Autowired
    private CompileService compileService;

    @Autowired
    private IImportTaskAppService importTaskAppService;

    @Autowired
    private IDimModelApplicationService iDimModelApplicationService;

    @Autowired
    private IWorkspaceCommonService workspaceCommonService;

    /**
     * 根据ID查询计算脚本，包含脚本的草稿信息
     *
     * @param scriptId 脚本ID
     * @return CalculationScript
     */
    @Override
    public CalculationScript getCalculationScriptById(Long scriptId) {
        if (scriptId == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_ID_NULL);
        }
        CalculationScript script = iCalculationScriptDomainService.getCalculationScriptById(scriptId);
        CalculationScriptDraft draft = iCalculationScriptDraftDomainService
                .getCalculationScriptDraftByScriptId(scriptId);
        script.setDraft(draft);
        return script;
    }

    /**
     * 规则脚本保存功能，有两种情况：
     * 1、如果是直接创建规则，那么对应的scriptId和Content必须为空，规则名、模型ID和文件夹ID不为空，且规则名称全局唯一。
     * 2、如果是已经有了规则，需要修改DSL脚本内容，那么直接存草稿，如果已经有了草稿，就更新草稿，并更新脚本状态。
     *
     * @param calculationScript 计算脚本
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Audit(message = "规则名称:%s", module = "规则管理", operation = "规则脚本保存")
    public CalculationScript saveCalculationScript(CalculationScript calculationScript) {
        calculationScript.setWorkspaceId(getCurrentWorkspaceId());
        if (calculationScript.getScriptId() == null) {
            checkScriptName(calculationScript);
            if (calculationScript.getFolderId() == null) {
                throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_FOLDER_ID_NULL);
            }
            if (calculationScript.getModelId() == null) {
                throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_MODEL_ID_NULL);
            }
            if (RequestContext.getCurrent(true) != null) {
                RequestContext.getCurrent(true).setItem(CalculationScriptConstants.OBJECT_NAME,
                        calculationScript.getScriptName());
            }
            return iCalculationScriptDomainService.saveCalculationScript(calculationScript);
        } else {
            if (calculationScript.getContent() == null) {
                throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_CONTENT_NULL);
            }
            CalculationScript originalScript = iCalculationScriptDomainService
                    .getCalculationScriptById(calculationScript.getScriptId());
            if (RequestContext.getCurrent(true) != null) {
                RequestContext.getCurrent(true).setItem(CalculationScriptConstants.OBJECT_NAME,
                        originalScript.getScriptName());
            }
            CalculationScriptDraft draft = new CalculationScriptDraft();
            draft.setScriptId(calculationScript.getScriptId());
            draft.setContent(calculationScript.getContent());
            draft.setWorkspaceId(calculationScript.getWorkspaceId());
            draft.setModelId(originalScript.getModelId());
            iCalculationScriptDraftDomainService.saveCalculationScriptDraft(draft);
            iCalculationScriptDomainService.updateCalculationScriptAfterDeployment(originalScript);
            if (CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_DEPLOY_SUCCESS
                    .equals(originalScript.getStatus())) {
                originalScript.setStatus(CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_UPDATED);
            }
            return originalScript;
        }
    }

    /**
     * 规则脚本的更新功能，提供规则的重命名、移动功能
     *
     * @param calculationScript 计算脚本
     */
    @Override
    @Audit(message = "规则名称:{#calculationScript.scriptName}", module = "规则管理", operation = "规则脚本修改(移动)")
    public void updateCalculationScript(CalculationScript calculationScript) {
        calculationScript.setWorkspaceId(getCurrentWorkspaceId());
        if (calculationScript.getScriptId() == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_ID_NULL);
        }
        CalculationScript newCalculationScript = iCalculationScriptDomainService
                .getCalculationScriptById(calculationScript.getScriptId());
        // 判断是重命名还是移动
        if (calculationScript.getFolderId() != null) {
            // 如果是移动目录，需要将规则名称set到对象里方便后续校验
            calculationScript.setScriptName(newCalculationScript.getScriptName());
            iCalculationScriptDomainService.moveCalculationScript(calculationScript);
        } else {
            checkScriptName(calculationScript);
            calculationScript.setFolderId(newCalculationScript.getFolderId());
            iCalculationScriptDomainService.renameCalculationScript(calculationScript);
        }
    }

    /**
     * 规则脚本的粘贴功能
     *
     * @param calculationScript 计算脚本
     */
    @Override
    @Audit(message = "规则名称:{#calculationScript.scriptName}", module = "规则管理", operation = "规则脚本粘贴")
    public void pasteCalculationScript(CalculationScript calculationScript) {
        calculationScript.setWorkspaceId(getCurrentWorkspaceId());
        checkScriptName(calculationScript);
        if (calculationScript.getFolderId() == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_FOLDER_ID_NULL);
        }
        if (calculationScript.getScriptId() == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_ID_NULL);
        }
        if (calculationScript.getModelId() == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_MODEL_ID_NULL);
        }
        iCalculationScriptDomainService.pasteCalculationScript(calculationScript);
    }

    @Override
    @Audit(message = "规则名称:%s", module = "规则管理", operation = "规则脚本删除")
    public void deleteCalculationScript(Long scriptId) {
        if (scriptId == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_ID_NULL);
        }
        CalculationScript calculationScript = iCalculationScriptDomainService.getCalculationScriptById(scriptId);
        RequestContext.getCurrent(true).setItem(CalculationScriptConstants.OBJECT_NAME,
                calculationScript.getScriptName());
        iCalculationScriptDomainService.deleteCalculationScript(scriptId, getCurrentWorkspaceId());
    }

    /**
     * 规则脚本校验功能
     *
     * @param scriptModel scriptModel
     * @return ApiResponse<Object>
     */
    @Override
    public ApiResponse<Object> verifyCalculationScript(ScriptModel scriptModel) {
        DSLSourceInfo dslSourceInfo = initDSLSourceInfo(scriptModel);
        if (isDslSourceTextNullStr(dslSourceInfo)) {
            return ApiResponse.success(CalculationScriptConstants.VERIFY_SUCCESS);
        }
        Object result = scriptVerifyChain.verify(dslSourceInfo);
        if (CalculationScriptConstants.VERIFY_SUCCESS.equals(result)) {
            return ApiResponse.success(result);
        } else if (OptCalculationDslException.class.equals(result.getClass())) {
            OptCalculationDslException dlsException = (OptCalculationDslException) result;
            if (AlterVariableCode.ALTER_VARIABLE_NAME_IS_NOT_EXIST.getErrorCode().equals(dlsException.getErrCode())) {
                String msg = dlsException.getMessage().split(":")[0];
                String data = dlsException.getMessage().split(":")[1];
                ApiResponse<Object> fail = ApiResponse.failure(msg);
                fail.setCode(dlsException.getErrCode());
                fail.setData(data);
                return fail;
            } else {
                return ApiResponse.failure(dlsException.getMessage());
            }
        } else {
            return ApiResponse.failure(result.toString());
        }
    }

    /**
     * 判断DSL源码是否为空
     */
    private boolean isDslSourceTextNullStr(DSLSourceInfo dslSourceInfo) {
        return isDslSourceTextNullStr(dslSourceInfo.getDslSourceText());
    }

    /**
     * 判断DSL源码是否为空
     */
    private boolean isDslSourceTextNullStr(String dslSourceText) {
        if (dslSourceText == null) {
            return true;
        }
        String dslSourceTextTrim = dslSourceText.trim().replaceAll(" ", "").replaceAll("\\t", "").replaceAll("\\n", "")
                .replaceAll("\\r", "");
        return dslSourceTextTrim.equals("");
    }

    /**
     * 规则脚本预览功能
     *
     * @param scriptModel scriptModel
     * @return RulesCompare
     */
    @Override
    public RulesCompare previewCalculationScript(ScriptModel scriptModel) throws DataSourceException {
        checkDeploymentScriptModel(scriptModel);
        DSLSourceInfo dslSourceInfo = initDSLSourceInfo(scriptModel);
        RulesCompare rulesCompare = new RulesCompare();
        // 分别编译当前传入的DSL文本，和上一个版本的DSL文本
        PreviewEffect current = compileService.previewDSLDeployEffect(dslSourceInfo);
        rulesCompare.setCurrentRule(initRules(current));
        // 从已部署的脚本里查询上一个版本的DSL文本，将对应的业务规则展示给前端
        CalculationScript previousScript = iCalculationScriptDomainService
                .getCalculationScriptById(scriptModel.getCalculationScript().getScriptId());
        if (previousScript.getContent() != null) {
            dslSourceInfo.setDslSourceText(previousScript.getContent());
            PreviewEffect previous = compileService.previewDSLDeployEffect(dslSourceInfo);
            rulesCompare.setPreviousRule(initRules(previous));
        }
        return rulesCompare;
    }

    /**
     * 校验规则脚本入参
     *
     * @param scriptModel
     */
    @Override
    public void checkDeploymentScriptModel(ScriptModel scriptModel) throws IllegalArgumentException {
        checkScriptModel(scriptModel);
        Long scriptId = scriptModel.getCalculationScript().getScriptId();
        CalculationScript calculationScript = iCalculationScriptDomainService.getCalculationScriptById(scriptId);
        if (calculationScript == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_NOT_FOUND);
        } else {
            // 补全必要的信息给异步任务使用
            scriptModel.getCalculationScript().setScriptName(calculationScript.getScriptName());
            scriptModel.getCalculationScript().setStatus(calculationScript.getStatus());
            scriptModel.getCalculationScript().setDeployedBy(calculationScript.getDeployedBy());
            scriptModel.getCalculationScript().setDeployedDate(calculationScript.getDeployedDate());
            scriptModel.getCalculationScript().setDraft(calculationScript.getDraft());
        }
    }

    /**
     * 1、校验规则脚本入参
     * 2、当传入的规则内容为空时，自动填充之前部署的规则
     *
     * @param scriptModel
     */
    @Override
    public void checkDeploymentScriptModelForSyncDeployment(ScriptModel scriptModel) throws IllegalArgumentException {
        checkScriptModel(scriptModel);
        Long scriptId = scriptModel.getCalculationScript().getScriptId();
        CalculationScript calculationScript = iCalculationScriptDomainService.getCalculationScriptById(scriptId);
        if (calculationScript == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_NOT_FOUND);
        } else {
            if (StringUtils.isEmpty(scriptModel.getCalculationScript().getContent())) {
                scriptModel.getCalculationScript().setContent(calculationScript.getContent());
            }
        }
    }

    private void checkScriptModel(ScriptModel scriptModel) {
        if (scriptModel.getCalculationScript() == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_NULL);
        }
        if (StringUtils.isBlank(scriptModel.getModelName())) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_MODEL_NAME_NULL);
        }
        if (scriptModel.getModelId() == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_MODEL_ID_NULL);
        }
        Long scriptId = scriptModel.getCalculationScript().getScriptId();
        if (scriptId == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_ID_NULL);
        }
    }

    /**
     * 更新规则状态
     *
     * @param calculationScript
     */
    @Override
    public int updateStatus(CalculationScript calculationScript) {
        return iCalculationScriptDomainService.updateStatus(calculationScript);
    }

    /**
     * 规则脚本回退功能
     *
     * @param calculationScript 计算脚本
     * @return CalculationScript
     */
    @Override
    @Audit(message = "规则名称:%s", module = "规则管理", operation = "规则脚本回退")
    public CalculationScript rollbackCalculationScript(CalculationScript calculationScript) {
        if (calculationScript == null || calculationScript.getScriptId() == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_ID_NULL);
        }
        CalculationScript newScript = new CalculationScript();
        newScript.setScriptId(calculationScript.getScriptId());
        List<CalculationScript> calculationScripts = iCalculationScriptDomainService.getCalculationScripts(newScript);
        if (calculationScripts.isEmpty()) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_PAST_NOT_FOUND);
        } else {
            CalculationScript latestVersion = calculationScripts.stream().findFirst().get();
            RequestContext.getCurrent(true).setItem(CalculationScriptConstants.OBJECT_NAME,
                    latestVersion.getScriptName());
            return iCalculationScriptDomainService.rollbackCalculationScript(latestVersion);
        }
    }

    public void finishDeploy(CalculationScript calculationScript, DSLSourceInfo dslSourceInfo, QuartzTaskInfo quartzTaskInfo){
        iCalculationScriptDomainService.finishDeploy(calculationScript, dslSourceInfo, quartzTaskInfo);
    }
    /**
     * 下载模板文件
     *
     * @param response response
     */
    @Override
    public void downloadCalculationScriptTemplate(HttpServletResponse response) {
        iCalculationScriptDomainService.downloadCalculationScriptTemplate(response);
    }

    /**
     * 规则脚本导出功能
     *
     * @param calculationScripts 多个计算脚本
     */
    @Override
    public void exportCalculationScripts(CalculationScripts calculationScripts) {
        iCalculationScriptDomainService.exportCalculationScripts(calculationScripts);
    }

    /**
     * 规则脚本导入功能，进行基本校验，如果文件有问题，则将错误记录到EDM日志里
     *
     * @param file file
     */
    @Override
    @Audit(message = "规则名称:%s", module = "规则管理", operation = "规则脚本导入")
    public void importCalculationScripts(MultipartFile file) {
        CalculationScriptsImport calculationScriptsImport = createCalculationScriptsImport();
        calculationScriptsImport.setXmlFileName(file.getOriginalFilename());
        ImportTask importTask = createImportTask(calculationScriptsImport.getXmlFileName());
        // 判断文件是否存在
        if (Objects.isNull(file) || file.isEmpty()) {
            iCalculationScriptDomainService.printImportLogAndUpdateTask(importTask, calculationScriptsImport,
                    new CalculationException(PubErrorCode.PUB_CAL_ERROR_UPLOAD_FILE_NULL), null);
            return;
        }
        // 判断那文件是否是XML文件格式
        if (!file.getOriginalFilename().toLowerCase(Locale.ROOT)
                .endsWith(CalculationScriptConstants.CALCULATION_SCRIPT_IMPORT_XML_FORMAT)) {
            iCalculationScriptDomainService.printImportLogAndUpdateTask(importTask, calculationScriptsImport,
                    new CalculationException(PubErrorCode.PUB_CAL_ERROR_UPLOAD_XML_REQUIRED),
                    file.getOriginalFilename());
            return;
        }
        // 判断文件大小是否过大
        if (file.getSize() > CalculationScriptConstants.CALCULATION_SCRIPT_IMPORT_FILE_MAX_BYTE) {
            iCalculationScriptDomainService.printImportLogAndUpdateTask(importTask, calculationScriptsImport,
                    new CalculationException(PubErrorCode.PUB_CAL_ERROR_UPLOAD_FILE_OVERSIZE), null);
            return;
        }
        iCalculationScriptDomainService.importCalculationScripts(file, importTask, calculationScriptsImport,
                getCurrentWorkspaceId());
    }

    private CalculationScriptsImport createCalculationScriptsImport() {
        String pathByDay = PathUtil.getPathByDay(CalculationScriptConstants.RULE);
        String userId = UserUtil.getUser(CalculationScriptConstants.USERID);
        String xmlBasePath = String.format(Locale.ROOT, "%s%s%s", pathByDay, File.separator, userId);
        String logPath = String.format(Locale.ROOT, "%s%s%s", xmlBasePath, File.separator,
                CalculationScriptConstants.CALCULATION_SCRIPT_EXPORT_LOG_PATH);
        String logFileName = System.currentTimeMillis()
                + CalculationScriptConstants.CALCULATION_SCRIPT_EXPORT_LOG_FORMAT;
        return CalculationScriptsImport.builder().pathByDay(pathByDay).userId(userId).xmlBasePath(xmlBasePath)
                .logPath(logPath).logFileName(logFileName).build();
    }

    private ImportTask createImportTask(String name) {
        ImportTask importTask = new ImportTask();
        importTask.setFileName(name);
        importTask.setModuleName(ModuleEnum.RULE.getMessage());
        importTask.setStatus(ExportTaskEnum.RUN.getCode());
        importTask.setRunStartTime(LocalDateTime.now());
        importTask.setCreatedBy(UserUtil.getUser(CalculationScriptConstants.USERID));
        importTaskAppService.createImportTask(importTask);
        return importTask;
    }

    /**
     * 初始化DSL源文件信息
     *
     * @param scriptModel scriptModel
     * @return DSLSourceInfo
     * @throws IllegalArgumentException IllegalArgumentException
     */
    public DSLSourceInfo initDSLSourceInfo(ScriptModel scriptModel) {
        DSLSourceInfo dsl = new DSLSourceInfo();
        dsl.setModelName(scriptModel.getModelName());
        dsl.setModelId(scriptModel.getModelId());
        dsl.setDslSourceText(scriptModel.getCalculationScript().getContent());
        dsl.setWorkspaceId(scriptModel.getCalculationScript().getWorkspaceId());
        if (scriptModel.getCalculationScript().getScriptId() != null) {
            dsl.setScriptId(scriptModel.getCalculationScript().getScriptId());
        }
        return dsl;
    }

    /**
     * 初始化规则
     *
     * @param effect effect
     * @return List<Map < String, String>>
     */
    private List<Map<String, String>> initRules(PreviewEffect effect) {
        List<Map<String, String>> rules = new ArrayList<>();
        List<String> businessRules = effect.getBusinessRules().stream().map(PreviewRule::getBusinessRule)
                .collect(Collectors.toList());
        for (String rule : businessRules) {
            Map<String, String> map = new HashMap<>(1);
            map.put(CalculationScriptConstants.RULE, rule);
            rules.add(map);
        }
        return rules;
    }

    /**
     * 查询某个作业空间下，所有的规则列表
     *
     * @param workspaceId
     * @return
     */
    @Override
    public List<CalculationScriptModel> findALlScriptsByWorkspaceId(long workspaceId) {
        return iCalculationScriptDomainService.getCalculationScriptsByModelIds(Collections.EMPTY_LIST, workspaceId);
    }

    /**
     * 查询某个作业空间下，所有的规则列表及相关跨模型列表
     *
     * @param workspaceId
     * @return
     */
    @Override
    public List<CalculationScriptRelativeModels> findALlScriptsWithRelativeModelsByWorkspaceId(long workspaceId) {
        return iCalculationScriptDomainService.findALlScriptsWithRelativeModelsByWorkspaceId(workspaceId);
    }

    private long getCurrentWorkspaceId() {
        return DataSourceUtil.getWorkspaceId();
    }

    /**
     * 规则脚本部署功能-同步
     * 加上QuartzTaskInfo作为入参，以便在任务执行时候，同步刷新相关的进度信息
     * 
     * @param scriptModel scriptModel
     * @param quartzTaskInfo 异步部署时，根据任务信息做部署进展刷新
     * @return 脚本部署后细分的规则数量
     */
    @Override
    public int executeDeployment(ScriptModel scriptModel, QuartzTaskInfo quartzTaskInfo) {
        String dynamicDB = null;
        try {
            dynamicDB = getDynamicDBFromRequestContext();
        } catch (ApplicationException e) {
            throw new CalculationException(e);
        }
        scriptModel.getCalculationScript().setWorkspaceId(getCurrentWorkspaceId());
        checkDeploymentScriptModel(scriptModel);
        DSLSourceInfo dslSourceInfo = initDSLSourceInfo(scriptModel);
        if (isDslSourceTextNullStr(dslSourceInfo)) {
            // 新增对规则内容的判断，如果为空，清除对应的单元粒度规则
            iCalculationScriptDomainService.deleteCalculationObjectsWhileDeployNullScript(
                    scriptModel.getCalculationScript().getScriptId(),
                    scriptModel.getCalculationScript().getWorkspaceId());
            // 新增对空DSL的草稿信息同步
            CalculationScriptDraft draft = new CalculationScriptDraft();
            draft.setScriptId(scriptModel.getCalculationScript().getScriptId());
            draft.setContent(scriptModel.getCalculationScript().getContent());
            draft.setWorkspaceId(scriptModel.getCalculationScript().getWorkspaceId());
            draft.setModelId(scriptModel.getCalculationScript().getModelId());
            iCalculationScriptDraftDomainService.saveCalculationScriptDraft(draft);
            // 刷新空内容的规则脚本状态，从运行中到部署成功
            if (CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_DEPLOYING
                    .equals(scriptModel.getCalculationScript().getStatus())) {
                scriptModel.getCalculationScript()
                        .setStatus(CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_DEPLOY_SUCCESS);
                iCalculationScriptDomainService.updateStatus(scriptModel.getCalculationScript());
            }
            // 刷新空规则对应的任务状态为完成 （两个数字为当前步骤和总步骤数）
            iCalculationScriptDomainService.updateTaskProgress(quartzTaskInfo,
                    CalculationScriptConstants.EXECUTE_SUCCESS, 1, 1);
            // 空规则没有任何细分规则
            return 0;
        } else {
            long scriptId = scriptModel.getCalculationScript().getScriptId();
            long workspaceId = scriptModel.getCalculationScript().getWorkspaceId();
            String modelId = scriptModel.getModelId();
            CalculationScript calculationScript = CalculationScript.builder().scriptId(scriptId).modelId(modelId)
                    .build();
            calculationScript.setWorkspaceId(workspaceId);
            calculationScript.setDbCode(dynamicDB);
            // 启动规则部署事务
            try {
                if(isWorkflowDeploy(scriptId)) {
                    log.info("使用工作流方式部署规则，ID：{}", scriptId);
                    dslDeployDomainService.singleDeployCalculationScript(calculationScript, dslSourceInfo, quartzTaskInfo);
                    return iCalculationScriptDomainService.queryRulesNumByScirptIdAndWorkspaceId(workspaceId, scriptId);
                }
                return iCalculationScriptDomainService.singleDeployCalculationScript(calculationScript, dslSourceInfo,
                        quartzTaskInfo);
            } catch (AlterVariableException | RuntimeException | DataSourceException e) {
                // 如果产生语法异常，更新状态至失败
                calculationScript.setStatus(CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_DEPLOY_FAIL);
                iCalculationScriptDomainService.updateStatus(calculationScript);
                log.info("规则ID：{}, 部署失败", scriptId, e);
                throw new CalculationException(e.getMessage(), e);
            }
        }
    }

    @Override
    public void saveCalculationGraphToGesForTemporal(CalculationScript calculationScript, DslDeployContext context){
        iCalculationScriptDomainService.saveCalculationGraphToGesForTemporal(calculationScript, context);
    }

    /**
     * 更新规则状态
     *
     * @param calculationScript
     */
    @Override
    public int executeUpdateStatus(CalculationScript calculationScript) {
        try {
            String dynamicDB = getDynamicDBFromRequestContext();
            calculationScript.setDbCode(dynamicDB);
            return iCalculationScriptDomainService.updateStatus(calculationScript);
        } catch (ApplicationException e) {
            throw new CalculationException(e);
        }
    }

    /**
     * 按照之前的部署时间来排序查询多个规则列表
     *
     * @param calculationScripts scriptModel
     */
    @Override
    public List<CalculationScriptModel> getSortedCalculationScriptsByList(CalculationScripts calculationScripts) {
        List<CalculationScript> scripts = calculationScripts.getCalculationScripts();
        if (scripts.isEmpty()) {
            return Collections.emptyList();
        }
        return iCalculationScriptDomainService.getSortedCalculationScriptsByList(scripts, getCurrentWorkspaceId());
    }

    /**
     * 规则脚本状态重置功能，解决一些脚本部署到一半失败
     *
     * @param calculationScript 计算脚本
     * @return
     */
    @Override
    @Audit(message = "规则名称：{#calculationScript.scriptName}", module = "规则管理", operation = "规则脚本状态重置")
    public CalculationScript resetCalculationScript(CalculationScript calculationScript) {
        Long scriptId = calculationScript.getScriptId();
        if (scriptId == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_ID_NULL);
        }
        CalculationScript script = iCalculationScriptDomainService.getCalculationScriptById(scriptId);
        if (script == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_NOT_FOUND);
        }
        script.setStatus(CalculationScriptConstants.CALCULATION_SCRIPT_STATUS_DRAFT);
        iCalculationScriptDomainService.updateStatus(script);
        return iCalculationScriptDomainService.getCalculationScriptById(scriptId);
    }

    @Override
    public ScriptModel initWorkspaceInfo(ScriptModel scriptModel) {
        long workspaceId = getCurrentWorkspaceId();
        return initWorkspaceInfo(workspaceId, scriptModel);
    }

    @Override
    public ScriptModel initWorkspaceInfo(long workspaceId, ScriptModel scriptModel) {
        CalculationScript calculationScript = scriptModel.getCalculationScript();
        calculationScript.setWorkspaceId(workspaceId);
        scriptModel.setWorkspaceName(workspaceCommonService.findWorkspaceNameById(workspaceId));
        return scriptModel;
    }

    /**
     * public API使用，主要用来校验入参
     * 
     * @param scriptModelVO
     * @return
     */
    @Override
    public ScriptModel initScriptModelForVerifyV2(ScriptModelVO scriptModelVO) {
        if (scriptModelVO == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_NULL);
        }
        if (scriptModelVO.getModelId() == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_MODEL_ID_NULL);
        }
        // 查询模型名称
        DimModelResponse modelDetail = iDimModelApplicationService.findDimModelDetail(scriptModelVO.getModelId());
        if (modelDetail == null) {
            throw new CalculationException(PubErrorCode.MODEL_ERROR_CODE_ILLEGAL);
        }
        if (scriptModelVO.getContent() == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_CONTENT_NULL);
        }
        // 拼装参数
        ScriptModel scriptModel = new ScriptModel();
        scriptModel.setModelId(scriptModelVO.getModelId());
        scriptModel.setModelName(modelDetail.getModelName());
        scriptModel.setCalculationScript(CalculationScript.builder().content(scriptModelVO.getContent()).build());
        return scriptModel;
    }

    /**
     * public API使用，主要用来校验入参
     * 
     * @param scriptModelVO
     * @return
     */
    @Override
    public ScriptModel initScriptModelForDeployV2(ScriptModelVO scriptModelVO) {
        ScriptModel scriptModel = initScriptModelForVerifyV2(scriptModelVO);
        scriptModel.getCalculationScript().setScriptId(scriptModelVO.getScriptId());
        return scriptModel;
    }

    /**
     * API使用，主要用来自动保存规则脚本
     *
     * @param scriptModelVO
     */
    @Override
    public CalculationScript saveCalculationScriptV2(ScriptModelVO scriptModelVO, ScriptFolder scriptFolder) {
        DateTimeFormatter formatter = DateTimeFormatter
                .ofPattern(CalculationScriptConstants.CALCULATION_SCRIPT_EXPORT_FORMAT);
        CalculationScript calculationScript = CalculationScript.builder().modelId(scriptModelVO.getModelId())
                .scriptName(CalculationScriptConstants.FIN_DM_OPT_TOD_DEFAULT_SCRIPT
                        + LocalDateTime.now().format(formatter))
                .folderId(scriptFolder.getFolderId()).folderCode(scriptFolder.getFolderCode()).build();
        calculationScript.setWorkspaceId(getCurrentWorkspaceId());
        iCalculationScriptDomainService.saveCalculationScript(calculationScript);
        // 正常情况下一个作业空间，规则名称不重复，因此以时间戳为唯一值的查询有且只有一个
        List<CalculationScript> calculationScripts = iCalculationScriptDomainService
                .getCalculationScripts(calculationScript);
        if (calculationScripts.size() != 1) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_NOT_FOUND);
        } else {
            // 保存对应的规则脚本草稿信息
            CalculationScript script = calculationScripts.get(0);
            CalculationScriptDraft draft = new CalculationScriptDraft();
            draft.setScriptId(script.getScriptId());
            draft.setContent(script.getContent());
            draft.setWorkspaceId(script.getWorkspaceId());
            draft.setModelId(script.getModelId());
            iCalculationScriptDraftDomainService.saveCalculationScriptDraft(draft);
            return script;
        }
    }

    /**
     * public API使用，主要用来重命名规则脚本
     *
     * @param calculationScript
     */
    @Override
    public ApiResponse<Object> renameCalculationScriptV2(CalculationScript calculationScript) {
        if (calculationScript == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_NULL);
        }
        if (calculationScript.getScriptId() == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_ID_NULL);
        }
        CalculationScript script = iCalculationScriptDomainService
                .getCalculationScriptById(calculationScript.getScriptId());
        if (script == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_NOT_FOUND);
        }
        if (calculationScript.getScriptName() == null) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_NAME_NULL);
        }
        if (calculationScript.getScriptName()
                .length() > CalculationScriptConstants.CALCULATION_SCRIPT_NAME_MAX_LENGTH) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_CALCULATION_SCRIPT_NAME_TOO_LONG);
        }
        iCalculationScriptDomainService.renameCalculationScript(calculationScript);
        return ApiResponse
                .success(iCalculationScriptDomainService.getCalculationScriptById(calculationScript.getScriptId()));
    }

    /**
     * 规则脚本内容更新
     *
     * @param scriptModelVO
     */
    @Override
    public CalculationScript updateCalculationScriptV2(ScriptModelVO scriptModelVO) {
        CalculationScript existsScript = iCalculationScriptDomainService
                .getCalculationScriptById(scriptModelVO.getScriptId());
        CalculationScript calculationScript = CalculationScript.builder().modelId(scriptModelVO.getModelId())
                .scriptId(scriptModelVO.getScriptId()).scriptName(existsScript.getScriptName())
                .content(scriptModelVO.getContent()).build();
        calculationScript.setWorkspaceId(getCurrentWorkspaceId());
        iCalculationScriptDomainService.updateCalculationScriptAndDraft(calculationScript);
        return calculationScript;
    }

    @Override
    public ScriptModel convertQuartzTaskToScriptModel(QuartzTaskInfo taskInfo) {
        if (taskInfo == null || taskInfo.getTaskContent() == null) {
            return null;
        }
        if (!TaskTypeEnum.SINGLE_DEPLOY.getCode().equals(taskInfo.getTaskType())) {
            return null;
        }
        ScriptModel scriptModel = null;
        try {
            scriptModel = JsonUtils.stringToObject(taskInfo.getTaskContent(), ScriptModel.class);
        } catch (IOException e) {
            log.error("转换任务数据失败，{}", e.getMessage());
        }
        return scriptModel;
    }

    @Override
    public void saveCalculationRules(CalculationScript calculationScript, DeployEffect deployEffect,
                                     DslDeployContext context, DeployCellParam param) throws DataSourceException {
        iCalculationScriptDomainService.saveCalculationRulesForTemporal(calculationScript, deployEffect,
                context, param);
    }

    @Override
    public void deleteTemporalRules(CalculationScript calculationScript, QuartzTaskInfo quartzTaskInfo) {
        iCalculationScriptDomainService.deleteHistoryDbFuncRules(calculationScript, quartzTaskInfo);
    }

    @Override
    public void migrateDataToRuleTable(CalculationScript calculationScript, Integer workFlowId) {
        iCalculationScriptDomainService.migrateDataToRuleTable(calculationScript, workFlowId);
    }

    @Override
    public List<String> queryWorkflowInfo(Long workspaceId, Integer workFlowId) {
        return iCalculationScriptDomainService.queryWorkflowInfo(workspaceId, workFlowId);
    }

    @Override
    public int deleteWorkflowInfo(Long workspaceId, Integer workFlowId) {
        return iCalculationScriptDomainService.deleteWorkflowInfo(workspaceId, workFlowId);
    }

    @Override
    public void migrateDataToCalculationTable(List<String> temporalTableName, Integer workFlowId) {
        iCalculationScriptDomainService.migrateDataToCalculationTable(temporalTableName, workFlowId);
    }

    @Override
    public void migrateDataToGlobalTable(Integer workFlowId) {
        iCalculationScriptDomainService.migrateDataToGlobalTable(workFlowId);
    }


    private boolean stringIsEmpty(String s) {
        return (s == null) ? true : (s.trim().isEmpty());
    }

    private void checkScriptName(CalculationScript calculationScript) {
        // 校验规则名称不能为空
        if (stringIsEmpty(calculationScript.getScriptName())) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_SCRIPT_NAME_NULL);
        }
        // 校验规则名称长度
        if (calculationScript.getScriptName()
                .length() > CalculationScriptConstants.CALCULATION_SCRIPT_NAME_MAX_LENGTH) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_CALCULATION_SCRIPT_NAME_TOO_LONG);
        }
        // 校验名称不允许有正反斜杠
        if (calculationScript.getScriptName().contains(CalculationScriptConstants.SCRIPT_FOLDER_FORWARD_SLASH)
                || calculationScript.getScriptName().contains(CalculationScriptConstants.SCRIPT_FOLDER_BACK_SLASH)) {
            throw new CalculationException(PubErrorCode.PUB_CAL_ERROR_FOLDER_NAME_CONTAINS_SLASHES);
        }
    }
}