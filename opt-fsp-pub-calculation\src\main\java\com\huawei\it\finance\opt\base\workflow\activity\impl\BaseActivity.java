/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.workflow.activity.impl;

import com.huawei.it.finance.opt.base.application.service.impl.CalculationScriptApplicationService;
import com.huawei.it.finance.opt.base.domain.repository.ICalculationScriptRepo;
import com.huawei.it.finance.opt.base.entity.CalculationScript;
import com.huawei.it.finance.opt.base.entity.ScriptModel;
import com.huawei.it.finance.opt.base.workflow.deploy.DslDeployContext;
import com.huawei.it.finance.opt.dsl.complier.input.DSLSourceInfo;
import com.huawei.it.finance.opt.task.entity.QuartzTaskInfo;
import com.huawei.it.finance.opt.task.infrastructure.db.repository.IQuartzTaskInfoRepo;
import com.huawei.it.finance.opt.tech.util.JsonUtils;
import com.huawei.it.finance.opt.workflow.deploy.dto.BaseParam;
import com.huawei.it.jalor5.core.util.exception.NoDataFoundException;
import lombok.AllArgsConstructor;
import org.slf4j.MDC;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

import static com.huawei.it.jalor5.core.request.impl.RequestContextConstants.KEY_TRACE_ID;
import static java.util.Objects.isNull;

/**
 * 基础活动类
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
@AllArgsConstructor
public abstract class BaseActivity {
    private final CalculationScriptApplicationService calcAppService;
    private final IQuartzTaskInfoRepo iQuartzTaskInfoRepo;
    private final ICalculationScriptRepo iCalculationScriptRepo;

    protected DslDeployContext buildDeployContext(BaseParam param) throws IOException, NoDataFoundException {
        // TODO:2025/4/22 同步接口调试，这里的quartzInfo没用，先写跳过逻辑，跑通后视情况删除
        QuartzTaskInfo taskInfo = new QuartzTaskInfo();
        taskInfo.setJobName(param.getJobName());
        DSLSourceInfo dslSourceInfo;
        if (param.getTaskId() != null) {
            taskInfo = loadTask(param.getTaskId());
            dslSourceInfo = buildDslSourceInfo(taskInfo);
        } else {
            List<CalculationScript> calculationScripts = iCalculationScriptRepo
                    .findCalculationScriptsByScriptIds(param.getWorkspaceId(),
                            LongStream.of(param.getScriptId())
                                    .boxed()
                                    .collect(Collectors.toList())
                    );
            if (calculationScripts.isEmpty()) {
                throw new NoDataFoundException("规则ID查不到规则", param.getScriptId().toString());
            }
            CalculationScript script = calculationScripts.get(0);
            dslSourceInfo = DSLSourceInfo.builder().scriptId(param.getScriptId()).workspaceId(param.getWorkspaceId())
                    .modelId(script.getModelId()).dslName(script.getScriptName()).modelName(param.getModelName())
                    .dslSourceText(script.getContent()).build();
        }

        return new DslDeployContext(iQuartzTaskInfoRepo, taskInfo, dslSourceInfo, param.getWorkspaceId(),
                param.getWorkFlowId(), param.getActivityId());
    }

    protected CalculationScript buildScript(BaseParam param) {
        CalculationScript calculationScript = CalculationScript.builder()
                .scriptId(param.getScriptId())
                .modelId(param.getModelId())
                .build();
        calculationScript.setWorkspaceId(param.getWorkspaceId());
        calculationScript.setDbCode(param.getDynamicDB());
        return calculationScript;
    }

    private QuartzTaskInfo loadTask(Long taskId) throws NoDataFoundException {
        QuartzTaskInfo taskInfo = iQuartzTaskInfoRepo.findQuartzTaskById(taskId.toString());
        if (isNull(taskInfo)) {
            throw new NoDataFoundException("", taskId.toString());
        }
        return taskInfo;
    }

    private DSLSourceInfo buildDslSourceInfo(QuartzTaskInfo taskInfo) throws IOException {
        ScriptModel scriptModel = JsonUtils.stringToObject(taskInfo.getTaskContent(), ScriptModel.class);
        // 将规则名称写入作为traceID，方便定位
        Optional.of(scriptModel).map(ScriptModel::getCalculationScript)
                .map(CalculationScript::getScriptName)
                .ifPresent(name -> MDC.put(KEY_TRACE_ID, name));
        return calcAppService.initDSLSourceInfo(scriptModel);
    }
}
