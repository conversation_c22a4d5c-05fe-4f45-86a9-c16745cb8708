/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.domain.repository;

import com.huawei.it.finance.opt.base.entity.CalculationRule;
import com.huawei.it.finance.opt.base.entity.CalculationScript;
import com.huawei.it.finance.opt.base.workflow.deploy.dto.DeployCellParam;
import com.huawei.it.finance.opt.fsp.pub.common.exception.DataSourceException;
import com.huawei.it.finance.opt.wrap.entity.CellElement;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ICalculationRuleRepo {
    /**
     * 查询计算规则
     *
     * @param calculationRule
     * @return
     */
    List<CalculationRule> getCalculationRules(CalculationRule calculationRule);

    /**
     * 根据单元格ID批量查询单元格名称
     *
     * @param cellElementIds
     * @param workspaceId
     * @return
     */
    List<CellElement> getCellElementNamesByIds(List<Long> cellElementIds, long workspaceId);

    /**
     * 查询最高优先级的计算规则
     *
     * @param calculationRule
     * @return
     */
    List<CalculationRule> getHighestPriorityUnitCalculationRules(CalculationRule calculationRule);

    /**
     * 根据规则脚本，删除所有已经部署的单元格粒度规则
     *
     * @param calculationScript 计算脚本
     * @return
     */
    void deleteAllCalculationRulesByCalculationScript(CalculationScript calculationScript);

    void doBatchDeleteDbFuncDimsCombs(List<Long> scriptIds);

    List<String> queryDbFuncNamesByScriptIds(List<Long> scriptIds);

    void doBatchDeleteDbFunc(List<Long> scriptIds);

    /**
     * 批量插入单元格粒度规则
     *
     * @param ruleMap
     */
    void batchInsertCalculationRules(Map<Long, CalculationRule> ruleMap);

    void batchInsertCalculationRulesForTemporal(Map<Long, CalculationRule> ruleMap, DeployCellParam param);

    void batchInsertDbFuncDimsCombs(Map<Long, CalculationRule> ruleMap, DeployCellParam param) throws DataSourceException;

    /**
     * 删除一组所有已经部署的单元格粒度规则
     *
     * @param groupId
     * @return
     */
    void deleteAllCalculationRulesByGroupId(String groupId, long workspaceId);

    /**
     * 删除一个规则里所有已经部署的单元格粒度规则
     *
     * @param scriptId
     * @param workspaceId
     * @return
     */
    void deleteCalculationRulesByScriptId(long scriptId, long workspaceId);

    /**
     * 获取输入的单元格id的规则
     *
     * @param param    待查询的规则
     * @param isDetail 规则详情
     * @return 计算规则列表
     */
    List<CalculationRule> batchQueryCalculationRules(List<CalculationRule> param, boolean isDetail);

    /**
     * 删除一个作业空间下的所有已经部署的单元格粒度规则
     *
     * @param workspaceId
     * @return
     */
    void deleteAllCalculationRulesByWorkspaceId(long workspaceId);

    /**
     * 查询一个模型瞎的多路径的计算规则
     *
     * @param modelId
     * @param workspaceId
     * @return
     */
    List<CalculationRule> getCalculationRules(String modelId, long workspaceId);

    void updateCalculationRule(CalculationRule calculationRule);

    List<CalculationRule> batchQueryCalculationRulesV2(List<Long> unitIdList, boolean isDetail);

    void migrateTempTableToRuleTable(CalculationScript calculationScript, Integer workFlowId);

    void migrateTempTableDbFuncInfoTable(CalculationScript calculationScript, Integer workFlowId);

    void migrateTempTableDbFuncCellInfoTable(CalculationScript calculationScript, Integer workFlowId);

    List<String> queryWorkflowInfo(Long workspaceId, Integer workFlowId);

    int deleteWorkflowInfo(Long workspaceId, Integer workFlowId);

    List<CalculationRule> queryRulesByScriptAndWorkspaceId(@Param("workspaceId") Long workspaceId,
                                                           @Param("scriptId") Long scriptId);
}
