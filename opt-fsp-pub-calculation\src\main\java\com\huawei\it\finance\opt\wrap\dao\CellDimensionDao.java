/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.wrap.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.huawei.it.finance.opt.base.workflow.deploy.entity.WorkFlowOperator;
import com.huawei.it.finance.opt.wrap.entity.CellElement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
@DS("#mysession.dynamicDB")
public interface CellDimensionDao {

    List<Map> findCellElementsByCondition(@Param("condition") CellElementQueryCondition condition,
            @Param("offset") Integer offset);

    List<Map> findSpecifyCellElements(@Param("cellElement") CellElement cellElement,
            @Param("dimField2dimValues") Map<String, Set<String>> dimField2dimValues);

    List<CellElement> batchFindCellElement(@Param("cellElementList") List<CellElement> cellElements);

    /**
     * 根据 unitIds 批量查询 model_id 映射关系
     */
    Map<Long, String> batchFindModelIdByUnitIds(@Param("unitIds") List<Long> unitIds);

    List<CellElement> findGlobalCellElements(CellElement cellElement);

    List<CellElement> findSimpleCellElements(CellElement cellElement);

    int deleteGlobalCellElements(CellElement cellElement);

    void saveWorkFlowOperateInfo(WorkFlowOperator workFlowOperator);

    void batchSaveGlobalCellElements(@Param("globalCellElements") List<CellElement> cellElements);

    void batchDeleteGlobalCellElementsById(@Param("globalCellElements") List<CellElement> cellElements);

    void batchDeleteCellElementsById(@Param("cellElements") List<CellElement> cellElements);

    List<CellElement> findGlobalCellElementsById(@Param("cellElementIds") List<Long> cellElementIds);

    List<CellElement> findGlobalCellElementsByIdV2(@Param("idStr") String idStr);

    List<Long> generateNextCellElementId(@Param("cellElements") List<CellElement> cellElements);

    List<Long> generateNextCellElementIds(@Param("cellElementSize") int cellElementSize);

    void batchSaveUniqueGlobalCellElements(@Param("globalCellElements") List<CellElement> cellElements);

    int deleteDuplicatedGlobalCells(@Param("workspaceId") long workspaceId, @Param("modelId") String modelId);

    void batchSaveCalculationCellElements(@Param("cellElements") List<CellElement> cellElements);

    int deleteDuplicatedCalculationCells(@Param("calculationTableName") String calculationTableName);

    List<Map> batchQueryValueByMetric(@Param("condition") CellElementQueryCondition queryCondition);

    int deleteDirtyGlobalCell(@Param("modelId") String modelId, @Param("modelTableName") String modelTableName);

    int deleteCalculationCell(@Param("modelTableName") String modelTableName);

    List<Long> findUnitIdByDim2Members(@Param("cellElement") CellElement cellElement,
            @Param("columnNames") List<String> sortedColumnNames,
            @Param("memberCodesList") List<List<String>> sortedMemberCodesSlice);

    void dropCalculationTable(@Param("calculationTableName") String calculationTableName);

    void deleteGlobalCellElementsFromCalculationTable(@Param("calculationTableName") String calculationTableName);
}
