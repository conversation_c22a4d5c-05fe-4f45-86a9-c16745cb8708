/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.workflow.deploy.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 部署单元格参数类
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class DeployCellParam extends BaseParam {
    private Integer totalCellCount;
    private Integer startIndex;
    private Integer endIndex;

    /**
     * 构造函数
     *
     * @param workflowParam 工作流参数
     * @param startIndex 开始索引
     * @param endIndex 结束索引
     * @param activityId 活动ID
     */
    public DeployCellParam(WorkflowParam workflowParam, Integer startIndex, Integer endIndex, Integer activityId) {
        super(workflowParam.getWorkspaceId(),
              workflowParam.getScriptId(),
              workflowParam.getTaskId(),
              workflowParam.getModelId(),
              workflowParam.getModelName(),
              workflowParam.getDynamicDB(),
              workflowParam.getJobName(),
              workflowParam.getAppIslandId(),
              workflowParam.getWorkFlowId(),
              activityId);
        this.totalCellCount = workflowParam.getTotalCellCount();
        this.startIndex = startIndex;
        this.endIndex = endIndex;
    }
}
