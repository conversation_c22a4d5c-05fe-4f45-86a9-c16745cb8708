/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.base.infrastructure.db.repository.impl;

import com.huawei.it.finance.opt.base.entity.Constants;
import com.huawei.it.finance.opt.base.entity.dbfunction.DbFuncDimsCombEntity;
import com.huawei.it.finance.opt.base.entity.dbfunction.DbFuncEntity;
import com.huawei.it.finance.opt.base.infrastructure.db.dao.DbFunctionDao;
import com.huawei.it.finance.opt.workflow.deploy.dto.DeployCellParam;
import com.huawei.it.finance.opt.fsp.pub.common.exception.DataSourceException;
import com.huawei.it.finance.opt.tech.util.CustomSQLUtil;
import com.huawei.it.finance.opt.tech.util.UserUtil;
import com.huawei.it.finance.pub.dimmodel.common.advice.TrackExecutionTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.UUID;

import static com.huawei.it.finance.opt.workflow.deploy.impl.DslDeployWorkflowImpl.isWorkflowDeploy;

/*
 * 1.保存单元格到动态成员的信息
 * 2.生成DB函数名称
 * 3.
 * */
@Slf4j
@Repository
public class DbFunctionRepo {

    private final DbFunctionDao dbFunctionDao;
    private static final String DM_FSPPUB_CALCULATION_DBFUNC_T = "dm_fsppub_calculation_dbfunc_info_t";
    private static final String DM_FSPPUB_CALCULATION_DBFUNC_CELL_INFO_T = "dm_fsppub_calculation_dbfunc_cell_info_t";

    private static final String DM_FSPPUB_CALCULATION_DBFUNC_TEMPORAL_T = "dm_fsppub_calculation_dbfunc_info_temporal_t";
    private static final String DM_FSPPUB_CALCULATION_DBFUNC_CELL_INFO_TEMPORAL_T = "dm_fsppub_calculation_dbfunc_cell_info_temporal_t";


    private static final String TEMPORAL_COLUMNS_TARGET = ", \"workflow_id\",\"activity_id\"";

    private static final String INSERT_COLUMNS_TARGET = "INSERT INTO %s  (\"script_id\", \"dbfunc_name\"," +
            " \"parm_type_list\", \"model_id\", \"workspace_id\", \"created_by\", \"creation_date\"," +
            " \"last_updated_by\", \"last_update_date\"";



    public static final String SUM_FUNC_TYPE = "SumFunc";
    public static final String AVG_FUNC_TYPE = "AvgFunc";
    private static final String SUM_FUNC_KEY_WORD = "Sum";
    private static final String AVG_FUNC_KEY_WORD = "Avg";
    private static final String EMPTY_STR = "";
    private static final String NULL_STR = "NULL";

    public DbFunctionRepo(DbFunctionDao dbFunctionDao) {
        this.dbFunctionDao = dbFunctionDao;
    }

    public static String generateInsertDbfuncInfoSQL(String tableName) {
        return String.format(INSERT_COLUMNS_TARGET, tableName);
    }


    public static void saveDbFuncInfos(List<DbFuncEntity> dbFuncEntities, DeployCellParam param) throws DataSourceException {
        Long scriptId = Optional.ofNullable(dbFuncEntities.get(0)).map(DbFuncEntity::getScriptId)
                .orElse(0L);

        Connection connection = null;
        String batchInsertSql;
        if(isWorkflowDeploy(scriptId)){
            batchInsertSql = generateTempDbFuncInfoInsertSQL(dbFuncEntities, param);
        }else{
            batchInsertSql = generateDbFuncInfoInsertSQL(dbFuncEntities);
        }

        try {
            connection = CustomSQLUtil.initConnection();
            CustomSQLUtil.executeSqlCommand(connection, batchInsertSql);
        } catch (Exception e) {
            CustomSQLUtil.rollback(connection);
            log.error("error sql: {}; error info:", batchInsertSql, e);
            throw new DataSourceException("execute save db function Info sql failed!");
        } finally {
            CustomSQLUtil.close(connection);
        }
    }

    public static void dropDbFunctionsByFuncNames(List<String> funcNames) throws DataSourceException {
        Connection connection = null;
        List<String> dropFunctionSQLs = generateFuncDropSQL(funcNames);
        try {
            connection = CustomSQLUtil.initConnection();
            for (String dropFunctionSQL : dropFunctionSQLs) {
                CustomSQLUtil.executeSqlCommand(connection, dropFunctionSQL);
            }
        } catch (Exception e) {
            CustomSQLUtil.rollback(connection);
            log.error("error sql: {}; error info:", dropFunctionSQLs, e);
            throw new DataSourceException("drop history db function :" + funcNames + " failed!");
        } finally {
            CustomSQLUtil.close(connection);
        }
    }

    private static List<String> generateFuncDropSQL(List<String> funcNames) {
        List<String> dropFuncSQLs = new ArrayList<>();
        for (String funcName : funcNames) {
            StringBuilder dropFuncSql = new StringBuilder();
            dropFuncSql.append("DROP FUNCTION IF EXISTS ").append(funcName).append("();");
            dropFuncSQLs.add(dropFuncSql.toString());
        }
        return dropFuncSQLs;
    }

    public static String generateDbFuncInfoInsertSQL(List<DbFuncEntity> dbFuncEntities) {
        StringBuilder sql = new StringBuilder();
        sql.append(generateInsertDbfuncInfoSQL(DM_FSPPUB_CALCULATION_DBFUNC_T)).append(")   VALUES");
        StringJoiner valuesJoiner = new StringJoiner(",");
        String userId = UserUtil.getUser(UserUtil.USERID);
        String INSERT_VALUES_AUDITS_TARGET = userId + ",CURRENT_TIMESTAMP," + userId + ",CURRENT_TIMESTAMP";
        for (DbFuncEntity dbFuncEntity : dbFuncEntities) {
            StringJoiner valueJoiner = new StringJoiner(",", "(", ")");
            valueJoiner.add(dbFuncEntity.getScriptId() == null ? NULL_STR : String.valueOf(dbFuncEntity.getScriptId()))
                    .add(dbFuncEntity.getDbFuncName() == null
                            ? NULL_STR
                            : String.format(Constants.FIELD_FORMAT, dbFuncEntity.getDbFuncName()))
                    .add(dbFuncEntity.getParmaTypes() == null
                            ? NULL_STR
                            : String.format(Constants.FIELD_FORMAT, dbFuncEntity.getParmaTypes()))
                    .add(dbFuncEntity.getModelId() == null
                            ? NULL_STR
                            : String.format(Constants.FIELD_FORMAT, dbFuncEntity.getModelId()))
                    .add(dbFuncEntity.getWorkspaceId() == null
                            ? NULL_STR
                            : String.valueOf(dbFuncEntity.getWorkspaceId()))
                    .add(INSERT_VALUES_AUDITS_TARGET);
            valuesJoiner.add(valueJoiner.toString());
        }
        sql.append(valuesJoiner).append(";");
        return sql.toString();
    }

    public static String generateTempDbFuncInfoInsertSQL(List<DbFuncEntity> dbFuncEntities, DeployCellParam param) {
        StringBuilder sql = new StringBuilder();
        sql.append(generateInsertDbfuncInfoSQL(DM_FSPPUB_CALCULATION_DBFUNC_TEMPORAL_T)).append(TEMPORAL_COLUMNS_TARGET).append(")   VALUES");
        StringJoiner valuesJoiner = new StringJoiner(",");
        String userId = UserUtil.getUser(UserUtil.USERID);
        String INSERT_VALUES_AUDITS_TARGET = userId + ",CURRENT_TIMESTAMP," + userId + ",CURRENT_TIMESTAMP";
        for (DbFuncEntity dbFuncEntity : dbFuncEntities) {
            StringJoiner valueJoiner = new StringJoiner(",", "(", ")");
            valueJoiner.add(dbFuncEntity.getScriptId() == null ? NULL_STR : String.valueOf(dbFuncEntity.getScriptId()))
                    .add(dbFuncEntity.getDbFuncName() == null
                            ? NULL_STR
                            : String.format(Constants.FIELD_FORMAT, dbFuncEntity.getDbFuncName()))
                    .add(dbFuncEntity.getParmaTypes() == null
                            ? NULL_STR
                            : String.format(Constants.FIELD_FORMAT, dbFuncEntity.getParmaTypes()))
                    .add(dbFuncEntity.getModelId() == null
                            ? NULL_STR
                            : String.format(Constants.FIELD_FORMAT, dbFuncEntity.getModelId()))
                    .add(dbFuncEntity.getWorkspaceId() == null
                            ? NULL_STR
                            : String.valueOf(dbFuncEntity.getWorkspaceId()))
                    .add(INSERT_VALUES_AUDITS_TARGET)
                    .add(param.getWorkFlowId().toString()).add(param.getActivityId().toString());
            valuesJoiner.add(valueJoiner.toString());
        }
        sql.append(valuesJoiner).append(";");
        return sql.toString();
    }

    public static String generateDbFuncName(String funcType) {
        String uuid = UUID.randomUUID().toString().replace("-", EMPTY_STR);
        return "get_" + funcType + "_" + uuid + "_f";
    }

    public static String generateDbFuncExecuteSql(String dbFuncName) {
        return "select * from " + dbFuncName + "();";
    }

    public static Map executeDbFunc(String dbFuncName) throws DataSourceException {
        Connection connection = null;
        Map<Integer, BigDecimal> resultMap;
        String dbFuncExecuteSql = generateDbFuncExecuteSql(dbFuncName);
        try {
            connection = CustomSQLUtil.initConnection();
            resultMap = CustomSQLUtil.executeDbFunction(connection, dbFuncExecuteSql);
        } catch (Exception e) {
            CustomSQLUtil.rollback(connection);
            log.error("error sql: {}; error info:", dbFuncExecuteSql, e);
            throw new DataSourceException("execute db function failed!");
        } finally {
            CustomSQLUtil.close(connection);
        }
        return resultMap;
    }

    public static String deployDbFunc(String funcType, Long workspaceId, String tableName, List<String> dynamicFields,
                                      Map<String, List<String>> fixedFields) throws DataSourceException {
        Connection connection = null;
        // 1. 生成函数名称
        String funcName = generateDbFuncName(funcType);
        String createFuncSql = generateFunctionCreateSql(funcType, workspaceId, funcName, tableName, dynamicFields,
                fixedFields);
        try {
            connection = CustomSQLUtil.initConnection();
            // 2. 生成函数SQL
            CustomSQLUtil.executeSqlCommand(connection, createFuncSql);
        } catch (Exception e) {
            CustomSQLUtil.rollback(connection);
            log.error("error sql: {}; error info:", createFuncSql, e);
            throw new DataSourceException("execute generate DbFunc sql failed!");
        } finally {
            CustomSQLUtil.close(connection);
        }
        return funcName;
    }

    public static String generateFunctionCreateSql(String funcType, Long workspaceId, String funcName, String tableName,
                                                   List<String> dynamicFields, Map<String, List<String>> fixedFields) {
        // 基础SQL结构
        StringBuilder sql = new StringBuilder();
        // 创建函数的SQL部分
        sql.append("CREATE OR REPLACE FUNCTION ").append(funcName).append("()\n")
                .append("RETURNS TABLE (unit_id BIGINT, amount DECIMAL) AS $$\nBEGIN\n")
                .append("    RETURN QUERY\n    WITH temp_table AS (\n");

        // 动态生成select部分
        sql.append(" SELECT\n").append(getAggregateFunction(funcType)).append("(amount) AS amount,\n");

        // 动态生成拼接字段部分
        for (int i = 0; i < dynamicFields.size(); i++) {
            if (i > 0) {
                sql.append(" || '##' || ");
            }
            sql.append(dynamicFields.get(i));
        }
        sql.append(" AS dynamic_info\n");
        // FROM位置：schema + 底表,示例：sql.append(" FROM fin_dm_opt_tod.dm_tod_bp_overseas_company_fsp_t\n");
        sql.append(" FROM ").append(tableName).append("\n");

        // 动态WHERE条件
        StringJoiner whereCondition = new StringJoiner(" AND ");
        for (Map.Entry<String, List<String>> entry : fixedFields.entrySet()) {
            String key = entry.getKey();
            List<String> values = entry.getValue();
            // 判断 key 和 value 是否为空
            if (key != null && !key.isEmpty() && values != null && !values.isEmpty()) {
                // 构建 IN 条件
                StringJoiner valueJoiner = new StringJoiner(",", "(", ")");
                for (String value : values) {
                    if (value != null && !value.isEmpty()) {
                        valueJoiner.add("'" + value + "'");
                    }
                }
                // 如果 valueJoiner 非空，则拼接到结果中
                if (valueJoiner.length() > 0) {
                    whereCondition.add(key + " in " + valueJoiner);
                }
            }
        }
        sql.append(" WHERE workspace_id = ").append(workspaceId).append(" AND \n").append(whereCondition);

        // 聚合条件 GROUP BY
        StringJoiner groupCondition = new StringJoiner(", ");
        for (String dynamicField : dynamicFields) {
            groupCondition.add(dynamicField);
        }
        sql.append("    GROUP BY ").append(groupCondition);

        // 临时表WITH tableName AS (xx),最后有个右括号
        sql.append("\n   )\n");

        // 最后的查询部分
        sql.append(
                        " SELECT a.unit_id, b.amount\n FROM dm_fsppub_calculation_dbfunc_cell_info_t a\n "
                                + "INNER JOIN temp_table b\n ON a.dynamic_dim_infos = b.dynamic_info\n WHERE a.dbfunc_name = \n'")
                .append(funcName).append("' ;\nEND;\n$$ LANGUAGE plpgsql;\n");

        log.debug("SQL of DB Function generation is {}", sql);
        return sql.toString();
    }

    private static String getAggregateFunction(String funcType) {
        if (funcType.equals(SUM_FUNC_TYPE)) {
            return SUM_FUNC_KEY_WORD;
        } else if (funcType.equals(AVG_FUNC_TYPE)) {
            return AVG_FUNC_KEY_WORD;
        }
        return EMPTY_STR;
    }

    @TrackExecutionTime
    public void batchInsertDbFuncCellInfo(List<DbFuncDimsCombEntity> dbFuncDimsCombEntityList) {
        dbFunctionDao.batchInsertDbFuncCellInfo(DM_FSPPUB_CALCULATION_DBFUNC_CELL_INFO_T, dbFuncDimsCombEntityList);
    }

    @TrackExecutionTime
    public void batchInsertTempDbFuncCellInfo(List<DbFuncDimsCombEntity> dbFuncDimsCombEntityList, DeployCellParam param) {
        dbFunctionDao.batchInsertTempDbFuncCellInfo(DM_FSPPUB_CALCULATION_DBFUNC_CELL_INFO_TEMPORAL_T, param.getWorkFlowId(),
                param.getActivityId(), dbFuncDimsCombEntityList);
    }


    @TrackExecutionTime
    public void batchDeleteDbFuncCellInfo(List<Long> scriptIds) {
        dbFunctionDao.batchDeleteCellIdToCombCodeInfos(scriptIds);
    }

    @TrackExecutionTime
    public void batchDeleteDbFuncInfo(List<Long> scriptIds) {
        dbFunctionDao.batchDeleteDbFuncByScriptIds(scriptIds);
    }

    public List<DbFuncEntity> queryDbFuncByScriptIds(List<Long> scriptIds) {
        return dbFunctionDao.queryDbFuncByScriptIds(scriptIds);
    }
}
