/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.finance.opt.workflow.deploy;

import com.huawei.it.finance.opt.workflow.deploy.dto.DeployCellParam;
import com.huawei.it.finance.opt.workflow.deploy.dto.WorkflowParam;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

/**
 * 部署单元格活动接口
 *
 * <AUTHOR>
 * @since 2025年01月16日
 */
@ActivityInterface
public interface DeployCellActivity {
    
    /**
     * 执行单元格部署
     *
     * @param param 部署单元格参数
     */
    @ActivityMethod
    void deployCellExecute(DeployCellParam param);

    /**
     * 执行部署结果保存
     *
     * @param workflowParam 工作流参数
     */
    @ActivityMethod
    void deployResultSaveExecute(WorkflowParam workflowParam);
}
